import{f as $e,an as ze,r as o,j as e,ao as ee,aq as Le,aW as Ve,a1 as D,aX as se,z as p,aY as qe,o as He,h as m,aZ as We,l as Z,Y as S,a9 as Ye,Z as T,V as Ge,p as Je,q as g,s as u,v as j,w as f,I as A,x as y,a_ as Ze,a$ as Be,a0 as Xe,J as N,b0 as Ke,y as Qe,b1 as es,b2 as ss}from"./index-YryEuZxD.js";import{f as ae,T as as,O as rs,W as ts,C as ls,g as os,h as ns,i as re,R as is,P as cs,D as $,a as z,b as L,c as V,d as q,e as H}from"./dialog-KgldRBPN.js";import{S as B,a as X,b as K,c as Q,d as W}from"./select-CPuDI4qD.js";import{A as ds}from"./AdminLayout-B7D2x_ni.js";import{S as ms}from"./shield-ha3OYHdw.js";import{T as xs}from"./trash-2-CiFV88WN.js";import{E as ps}from"./eye-off-IOJfHBfM.js";import{E as hs}from"./eye-Dr9o_L6v.js";import"./index-CEBFZLRX.js";import"./chevron-up-C5Bz9x-Q.js";import"./users-Cq4h5U_e.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gs=$e("ShieldOff",[["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M5 5a1 1 0 0 0-1 1v7c0 5 3.5 7.5 7.67 8.94a1 1 0 0 0 .67.01c2.35-.82 4.48-1.97 5.9-3.71",key:"1jlk70"}],["path",{d:"M9.309 3.652A12.252 12.252 0 0 0 11.24 2.28a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1v7a9.784 9.784 0 0 1-.08 1.264",key:"18rp1v"}]]);var te="AlertDialog",[us,Ls]=ze(te,[ae]),v=ae(),le=a=>{const{__scopeAlertDialog:r,...t}=a,n=v(r);return e.jsx(is,{...n,...t,modal:!0})};le.displayName=te;var js="AlertDialogTrigger",fs=o.forwardRef((a,r)=>{const{__scopeAlertDialog:t,...n}=a,i=v(t);return e.jsx(as,{...i,...n,ref:r})});fs.displayName=js;var ys="AlertDialogPortal",oe=a=>{const{__scopeAlertDialog:r,...t}=a,n=v(r);return e.jsx(cs,{...n,...t})};oe.displayName=ys;var Ns="AlertDialogOverlay",ne=o.forwardRef((a,r)=>{const{__scopeAlertDialog:t,...n}=a,i=v(t);return e.jsx(rs,{...i,...n,ref:r})});ne.displayName=Ns;var w="AlertDialogContent",[vs,As]=us(w),ie=o.forwardRef((a,r)=>{const{__scopeAlertDialog:t,children:n,...i}=a,b=v(t),l=o.useRef(null),C=ee(r,l),P=o.useRef(null);return e.jsx(ts,{contentName:w,titleName:ce,docsSlug:"alert-dialog",children:e.jsx(vs,{scope:t,cancelRef:P,children:e.jsxs(ls,{role:"alertdialog",...b,...i,ref:C,onOpenAutoFocus:Le(i.onOpenAutoFocus,h=>{var R;h.preventDefault(),(R=P.current)==null||R.focus({preventScroll:!0})}),onPointerDownOutside:h=>h.preventDefault(),onInteractOutside:h=>h.preventDefault(),children:[e.jsx(Ve,{children:n}),e.jsx(bs,{contentRef:l})]})})})});ie.displayName=w;var ce="AlertDialogTitle",de=o.forwardRef((a,r)=>{const{__scopeAlertDialog:t,...n}=a,i=v(t);return e.jsx(os,{...i,...n,ref:r})});de.displayName=ce;var me="AlertDialogDescription",xe=o.forwardRef((a,r)=>{const{__scopeAlertDialog:t,...n}=a,i=v(t);return e.jsx(ns,{...i,...n,ref:r})});xe.displayName=me;var Ds="AlertDialogAction",pe=o.forwardRef((a,r)=>{const{__scopeAlertDialog:t,...n}=a,i=v(t);return e.jsx(re,{...i,...n,ref:r})});pe.displayName=Ds;var he="AlertDialogCancel",ge=o.forwardRef((a,r)=>{const{__scopeAlertDialog:t,...n}=a,{cancelRef:i}=As(he,t),b=v(t),l=ee(r,i);return e.jsx(re,{...b,...n,ref:l})});ge.displayName=he;var bs=({contentRef:a})=>{const r=`\`${w}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${w}\` by passing a \`${me}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${w}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return o.useEffect(()=>{var n;document.getElementById((n=a.current)==null?void 0:n.getAttribute("aria-describedby"))||console.warn(r)},[r,a]),null},ws=le,Cs=oe,ue=ne,je=ie,fe=pe,ye=ge,Ne=de,ve=xe;const Ss=ws,Ps=Cs,Ae=o.forwardRef(({className:a,...r},t)=>e.jsx(ue,{className:D("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...r,ref:t}));Ae.displayName=ue.displayName;const De=o.forwardRef(({className:a,...r},t)=>e.jsxs(Ps,{children:[e.jsx(Ae,{}),e.jsx(je,{ref:t,className:D("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...r})]}));De.displayName=je.displayName;const be=({className:a,...r})=>e.jsx("div",{className:D("flex flex-col space-y-2 text-center sm:text-left",a),...r});be.displayName="AlertDialogHeader";const we=({className:a,...r})=>e.jsx("div",{className:D("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...r});we.displayName="AlertDialogFooter";const Ce=o.forwardRef(({className:a,...r},t)=>e.jsx(Ne,{ref:t,className:D("text-lg font-semibold",a),...r}));Ce.displayName=Ne.displayName;const Se=o.forwardRef(({className:a,...r},t)=>e.jsx(ve,{ref:t,className:D("text-sm text-muted-foreground",a),...r}));Se.displayName=ve.displayName;const Pe=o.forwardRef(({className:a,...r},t)=>e.jsx(fe,{ref:t,className:D(se(),a),...r}));Pe.displayName=fe.displayName;const Re=o.forwardRef(({className:a,...r},t)=>e.jsx(ye,{ref:t,className:D(se({variant:"outline"}),"mt-2 sm:mt-0",a),...r}));Re.displayName=ye.displayName;const Rs=p.object({email:p.string().email({message:"Please enter a valid email address"}),password:p.string().min(8,{message:"Password must be at least 8 characters"}).refine(a=>{const r=/[A-Z]/.test(a),t=/[a-z]/.test(a),n=/[0-9]/.test(a),i=/[!@#$%^&*(),.?":{}|<>]/.test(a);return r&&t&&n&&i},{message:"Password must include uppercase, lowercase, number, and special character"}),displayName:p.string().min(2,{message:"Display name must be at least 2 characters"}),phone:p.string().min(10,{message:"Please enter a valid phone number"}).max(15).optional(),address:p.string().optional(),apartment:p.string().optional(),city:p.string().optional(),state:p.string().optional(),pincode:p.string().min(6,{message:"Please enter a valid pincode"}).max(6).optional(),role:p.enum(["user","admin"],{required_error:"Please select a role"})}),Vs=()=>{const[a,r]=o.useState([]),[t,n]=o.useState(!0),[i,b]=o.useState(null),[l,C]=o.useState(null),[P,h]=o.useState(!1),[R,E]=o.useState(!1),[Ee,O]=o.useState(!1),[Oe,U]=o.useState(!1),[_,F]=o.useState(!1),[d,Y]=o.useState(!1),[k,G]=o.useState(!1),[I,J]=o.useState(!1);o.useEffect(()=>{M()},[]);const M=async()=>{try{n(!0),b(null);const s=await qe();r(s)}catch(s){console.error("Error fetching users:",s),b("Failed to load users. Please try again.")}finally{n(!1)}},_e=s=>{C(s),h(!0)},Fe=s=>{C(s),E(!0)},Te=async()=>{if(l)try{F(!0),await Xe(l.uid),r(a.map(s=>s.uid===l.uid?{...s,role:T.Admin}:s)),N.success(`${l.displayName||l.email} has been promoted to admin`),h(!1)}catch(s){console.error("Error promoting user to admin:",s),N.error("Failed to promote user. Please try again.")}finally{F(!1)}},Ue=async()=>{if(l)try{F(!0),await Ke(l.uid),r(a.map(s=>s.uid===l.uid?{...s,role:T.User}:s)),N.success(`Admin role removed from ${l.displayName||l.email}`),E(!1)}catch(s){console.error("Error removing admin role:",s),N.error("Failed to remove admin role. Please try again.")}finally{F(!1)}},c=He({resolver:Qe(Rs),defaultValues:{email:"",password:"",displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",role:"user"}}),ke=async s=>{try{Y(!0);const x=await es(s.email,s.password,{displayName:s.displayName,phone:s.phone,address:s.address,apartment:s.apartment,city:s.city,state:s.state,pincode:s.pincode,role:s.role});x.success?(N.success(x.message),O(!1),c.reset(),M()):N.error(x.message)}catch(x){console.error("Error creating user:",x),N.error("Failed to create user. Please try again.")}finally{Y(!1)}},Ie=s=>{C(s),U(!0)},Me=async()=>{if(l)try{G(!0);const s=await ss(l.uid);s.success?(r(a.filter(x=>x.uid!==l.uid)),N.success(s.message)):N.error(s.message),U(!1)}catch(s){console.error("Error deleting user:",s),N.error("Failed to delete user. Please try again.")}finally{G(!1)}};return e.jsxs(ds,{title:"User Management",description:"Manage users and their permissions",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"User Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage users and their permissions"})]}),e.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[e.jsxs(m,{variant:"default",onClick:()=>O(!0),className:"flex items-center",children:[e.jsx(We,{className:"h-4 w-4 mr-2"}),"Create New User"]}),e.jsx(m,{variant:"outline",onClick:M,disabled:t,children:t?e.jsxs(e.Fragment,{children:[e.jsx(Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(Z,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),i&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:i})}),t?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(S,{size:"lg"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading users..."})]}):a.length>0?e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:a.map(s=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:s.photoURL?e.jsx("img",{className:"h-10 w-10 rounded-full",src:s.photoURL,alt:""}):e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.jsx(Ye,{className:"h-6 w-6 text-gray-500"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:s.displayName||"No Name"}),e.jsx("div",{className:"text-sm text-gray-500",children:s.phone||"No Phone"})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.email})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.role===T.Admin?e.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-burgundy-100 text-burgundy-800",children:"Admin"}):e.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"User"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex gap-2",children:[s.role===T.Admin?e.jsxs(m,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>Fe(s),children:[e.jsx(gs,{className:"h-4 w-4"}),"Remove Admin"]}):e.jsxs(m,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>_e(s),children:[e.jsx(ms,{className:"h-4 w-4"}),"Make Admin"]}),e.jsxs(m,{variant:"outline",size:"sm",className:"flex items-center gap-1 text-red-600 hover:text-red-800 hover:bg-red-50",onClick:()=>Ie(s),children:[e.jsx(xs,{className:"h-4 w-4"}),"Delete"]})]})})]},s.uid))})]})}):e.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"There are no users in the system."})]}),e.jsx($,{open:P,onOpenChange:h,children:e.jsxs(z,{children:[e.jsxs(L,{children:[e.jsx(V,{children:"Promote to Admin"}),e.jsxs(q,{children:["Are you sure you want to promote ",(l==null?void 0:l.displayName)||(l==null?void 0:l.email)," to admin? This will give them full access to the admin dashboard and all administrative functions."]})]}),e.jsxs(H,{children:[e.jsx(m,{variant:"outline",onClick:()=>h(!1),children:"Cancel"}),e.jsx(m,{variant:"default",onClick:Te,disabled:_,children:_?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Promotion"})]})]})}),e.jsx($,{open:R,onOpenChange:E,children:e.jsxs(z,{children:[e.jsxs(L,{children:[e.jsx(V,{children:"Remove Admin Role"}),e.jsxs(q,{children:["Are you sure you want to remove admin privileges from ",(l==null?void 0:l.displayName)||(l==null?void 0:l.email),"? They will no longer have access to the admin dashboard and administrative functions."]})]}),e.jsxs(H,{children:[e.jsx(m,{variant:"outline",onClick:()=>E(!1),children:"Cancel"}),e.jsx(m,{variant:"destructive",onClick:Ue,disabled:_,children:_?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Removal"})]})]})}),e.jsx(Ss,{open:Oe,onOpenChange:U,children:e.jsxs(De,{children:[e.jsxs(be,{children:[e.jsxs(Ce,{className:"text-red-600 flex items-center gap-2",children:[e.jsx(Ge,{className:"h-5 w-5"}),"Delete User Account"]}),e.jsxs(Se,{children:["Are you sure you want to delete the account for ",e.jsx("strong",{children:(l==null?void 0:l.displayName)||(l==null?void 0:l.email)}),"?",e.jsx("br",{}),e.jsx("br",{}),"This action is permanent and cannot be undone. All user data will be removed from the system."]})]}),e.jsxs(we,{children:[e.jsx(Re,{disabled:k,children:"Cancel"}),e.jsx(Pe,{onClick:Me,disabled:k,className:"bg-red-600 hover:bg-red-700 text-white",children:k?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete User"})]})]})}),e.jsx($,{open:Ee,onOpenChange:s=>{O(s),s||(c.reset(),J(!1))},children:e.jsxs(z,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[e.jsxs(L,{children:[e.jsx(V,{children:"Create New User"}),e.jsx(q,{children:"Fill in the details to create a new user account. All fields marked with * are required."})]}),e.jsx(Je,{...c,children:e.jsxs("form",{onSubmit:c.handleSubmit(ke),className:"space-y-4 py-2",children:[e.jsx(g,{control:c.control,name:"email",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"Email *"}),e.jsx(f,{children:e.jsx(A,{placeholder:"<EMAIL>",...s,disabled:d})}),e.jsx(y,{})]})}),e.jsx(g,{control:c.control,name:"password",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"Password *"}),e.jsxs("div",{className:"relative",children:[e.jsx(f,{children:e.jsx(A,{type:I?"text":"password",placeholder:"••••••••",...s,disabled:d})}),e.jsx(m,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3",onClick:()=>J(!I),children:I?e.jsx(ps,{className:"h-4 w-4"}):e.jsx(hs,{className:"h-4 w-4"})})]}),e.jsx(Ze,{children:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character."}),e.jsx(y,{})]})}),e.jsx(g,{control:c.control,name:"displayName",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"Display Name *"}),e.jsx(f,{children:e.jsx(A,{placeholder:"John Doe",...s,disabled:d})}),e.jsx(y,{})]})}),e.jsx(g,{control:c.control,name:"phone",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"Phone Number"}),e.jsx(f,{children:e.jsx(A,{placeholder:"9876543210",...s,disabled:d})}),e.jsx(y,{})]})}),e.jsx(g,{control:c.control,name:"role",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"User Role *"}),e.jsxs(B,{onValueChange:s.onChange,defaultValue:s.value,disabled:d,children:[e.jsx(f,{children:e.jsx(X,{children:e.jsx(K,{placeholder:"Select a role"})})}),e.jsxs(Q,{children:[e.jsx(W,{value:"user",children:"Regular User"}),e.jsx(W,{value:"admin",children:"Administrator"})]})]}),e.jsx(y,{})]})}),e.jsx(g,{control:c.control,name:"address",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"Address"}),e.jsx(f,{children:e.jsx(A,{placeholder:"123 Main St",...s,disabled:d})}),e.jsx(y,{})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(g,{control:c.control,name:"city",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"City"}),e.jsx(f,{children:e.jsx(A,{placeholder:"Mumbai",...s,disabled:d})}),e.jsx(y,{})]})}),e.jsx(g,{control:c.control,name:"state",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"State"}),e.jsxs(B,{onValueChange:s.onChange,value:s.value,disabled:d,children:[e.jsx(f,{children:e.jsx(X,{children:e.jsx(K,{placeholder:"Select a state"})})}),e.jsx(Q,{children:Be.map(x=>e.jsx(W,{value:x,children:x},x))})]}),e.jsx(y,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(g,{control:c.control,name:"pincode",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"Pincode"}),e.jsx(f,{children:e.jsx(A,{placeholder:"400001",...s,disabled:d})}),e.jsx(y,{})]})}),e.jsx(g,{control:c.control,name:"apartment",render:({field:s})=>e.jsxs(u,{children:[e.jsx(j,{children:"Apartment"}),e.jsx(f,{children:e.jsx(A,{placeholder:"Apartment name",...s,disabled:d})}),e.jsx(y,{})]})})]}),e.jsxs(H,{className:"pt-4",children:[e.jsx(m,{variant:"outline",type:"button",onClick:()=>O(!1),disabled:d,children:"Cancel"}),e.jsx(m,{type:"submit",disabled:d,children:d?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Creating User..."]}):"Create User"})]})]})})]})})]})};export{Vs as default};
