const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-B1ZgTNj8.js","assets/chunk-CpdlqjqK.js","assets/chunk-b0HHmiEU.js"])))=>i.map(i=>d[i]);
import{v as t,_ as a,w as e}from"./index-DwRq5Uwb.js";const c=async c=>{try{await t();const{collection:s,addDoc:o,serverTimestamp:r}=await a((async()=>{const{collection:t,addDoc:a,serverTimestamp:e}=await import("./chunk-B1ZgTNj8.js").then((t=>t.k));return{collection:t,addDoc:a,serverTimestamp:e}}),__vite__mapDeps([0,1,2])),n=s(e,"contactMessages"),i={...c,createdAt:r(),isRead:!1};return(await o(n,i)).id}catch(s){throw s}},s=async()=>{try{await t();const{collection:c,query:s,getDocs:o,orderBy:r}=await a((async()=>{const{collection:t,query:a,getDocs:e,orderBy:c}=await import("./chunk-B1ZgTNj8.js").then((t=>t.k));return{collection:t,query:a,getDocs:e,orderBy:c}}),__vite__mapDeps([0,1,2])),n=s(c(e,"contactMessages"),r("createdAt","desc")),i=await o(n),d=[];return i.forEach((t=>{const a=t.data();d.push({id:t.id,email:a.email||"",phone:a.phone||"",message:a.message||"",createdAt:a.createdAt,isRead:a.isRead||!1,readAt:a.readAt||null})})),d}catch(c){throw c}},o=async c=>{try{await t();const{doc:s,updateDoc:o,serverTimestamp:r}=await a((async()=>{const{doc:t,updateDoc:a,serverTimestamp:e}=await import("./chunk-B1ZgTNj8.js").then((t=>t.k));return{doc:t,updateDoc:a,serverTimestamp:e}}),__vite__mapDeps([0,1,2])),n=s(e,"contactMessages",c);await o(n,{isRead:!0,readAt:r()})}catch(s){throw s}},r=async c=>{try{await t();const{doc:s,updateDoc:o}=await a((async()=>{const{doc:t,updateDoc:a}=await import("./chunk-B1ZgTNj8.js").then((t=>t.k));return{doc:t,updateDoc:a}}),__vite__mapDeps([0,1,2])),r=s(e,"contactMessages",c);await o(r,{isRead:!1,readAt:null})}catch(s){throw s}};export{r as a,s as g,o as m,c as s};
