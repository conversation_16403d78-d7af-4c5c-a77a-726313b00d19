/**
 * LazyFormComponents
 * 
 * Lazy-loaded wrapper for heavy form components to reduce initial bundle size
 */

import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Form component loading skeleton
const FormSkeleton: React.FC = () => (
  <div className="space-y-4">
    <Skeleton className="h-10 w-full" />
    <Skeleton className="h-10 w-full" />
    <Skeleton className="h-20 w-full" />
    <Skeleton className="h-10 w-32" />
  </div>
);

// Lazy load react-hook-form components
const useForm = React.lazy(() => 
  import('react-hook-form').then(module => ({ default: module.useForm }))
);

const Controller = React.lazy(() => 
  import('react-hook-form').then(module => ({ default: module.Controller }))
);

const FormProvider = React.lazy(() => 
  import('react-hook-form').then(module => ({ default: module.FormProvider }))
);

const useFormContext = React.lazy(() => 
  import('react-hook-form').then(module => ({ default: module.useFormContext }))
);

const useWatch = React.lazy(() => 
  import('react-hook-form').then(module => ({ default: module.useWatch }))
);

const useController = React.lazy(() => 
  import('react-hook-form').then(module => ({ default: module.useController }))
);

// Lazy load zod resolver
const zodResolver = React.lazy(() => 
  import('@hookform/resolvers/zod').then(module => ({ default: module.zodResolver }))
);

// Lazy load date picker
const DatePicker = React.lazy(() => import('react-day-picker'));

// Lazy load input OTP
const InputOTP = React.lazy(() => 
  import('input-otp').then(module => ({ default: module.OTPInput }))
);

// Wrapper components with suspense
interface LazyFormWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const LazyFormWrapper: React.FC<LazyFormWrapperProps> = ({ 
  children, 
  fallback 
}) => (
  <Suspense fallback={fallback || <FormSkeleton />}>
    {children}
  </Suspense>
);

export const LazyDatePickerWrapper: React.FC<LazyFormWrapperProps> = ({ 
  children, 
  fallback 
}) => (
  <Suspense fallback={fallback || <Skeleton className="h-10 w-full" />}>
    {children}
  </Suspense>
);

export const LazyOTPWrapper: React.FC<LazyFormWrapperProps> = ({ 
  children, 
  fallback 
}) => (
  <Suspense fallback={fallback || <Skeleton className="h-12 w-full" />}>
    {children}
  </Suspense>
);

// Export lazy-loaded components
export {
  useForm,
  Controller,
  FormProvider,
  useFormContext,
  useWatch,
  useController,
  zodResolver,
  DatePicker,
  InputOTP,
  FormSkeleton
};

// Default export for convenience
export default {
  FormWrapper: LazyFormWrapper,
  DatePickerWrapper: LazyDatePickerWrapper,
  OTPWrapper: LazyOTPWrapper,
  FormSkeleton
};
