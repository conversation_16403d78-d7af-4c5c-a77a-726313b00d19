{"hash": "70d124a6", "configHash": "6c62acdb", "lockfileHash": "d98c36ea", "browserHash": "c23604b1", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "a41b9a88", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "67ee7f94", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "2a7a6df3", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "e2aa2661", "needsInterop": false}, "firebase/app": {"src": "../../firebase/app/dist/esm/index.esm.js", "file": "firebase_app.js", "fileHash": "a8e9c00e", "needsInterop": false}, "firebase/auth": {"src": "../../firebase/auth/dist/esm/index.esm.js", "file": "firebase_auth.js", "fileHash": "46c113b8", "needsInterop": false}, "firebase/firestore": {"src": "../../firebase/firestore/dist/esm/index.esm.js", "file": "firebase_firestore.js", "fileHash": "55e51332", "needsInterop": false}, "firebase/storage": {"src": "../../firebase/storage/dist/esm/index.esm.js", "file": "firebase_storage.js", "fileHash": "a9feef31", "needsInterop": false}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "0da3baa5", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "42ce8524", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "0ce41024", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "ab13906b", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "67c6538e", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "c2940930", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "54f98428", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "8e6980cd", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "585c4e53", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "2a4490cd", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "e589f115", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "fea59e00", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "077d333a", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "699aaa24", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "1954e9bd", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "beca3041", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "0c8541e3", "needsInterop": false}, "firebase/analytics": {"src": "../../firebase/analytics/dist/esm/index.esm.js", "file": "firebase_analytics.js", "fileHash": "039982b5", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "29b6c9c4", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "ec6a15d9", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "634bb458", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "a659b267", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "89c1a351", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "285111c8", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "28bed340", "needsInterop": false}, "zod": {"src": "../../zod/lib/index.mjs", "file": "zod.js", "fileHash": "86822e8a", "needsInterop": false}}, "chunks": {"chunk-DDW565K2": {"file": "chunk-DDW565K2.js"}, "chunk-3X2BLM77": {"file": "chunk-3X2BLM77.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-RBMNEPGM": {"file": "chunk-RBMNEPGM.js"}, "chunk-5CWCUKYA": {"file": "chunk-5CWCUKYA.js"}, "chunk-JZMVWMZD": {"file": "chunk-JZMVWMZD.js"}, "chunk-7X7HBA5K": {"file": "chunk-7X7HBA5K.js"}, "chunk-5BLYCQSK": {"file": "chunk-5BLYCQSK.js"}, "chunk-O2UA4OQB": {"file": "chunk-O2UA4OQB.js"}, "chunk-6SPNF6KQ": {"file": "chunk-6SPNF6KQ.js"}, "chunk-GV5OLEEW": {"file": "chunk-GV5OLEEW.js"}, "chunk-E743DKIS": {"file": "chunk-E743DKIS.js"}, "chunk-QJWIQYY7": {"file": "chunk-QJWIQYY7.js"}, "chunk-Q6UFC6W2": {"file": "chunk-Q6UFC6W2.js"}, "chunk-XPEH7EET": {"file": "chunk-XPEH7EET.js"}, "chunk-AFJ6VPUJ": {"file": "chunk-AFJ6VPUJ.js"}, "chunk-AYSQM7VZ": {"file": "chunk-AYSQM7VZ.js"}, "chunk-4WIT4MX7": {"file": "chunk-4WIT4MX7.js"}, "chunk-WERSD76P": {"file": "chunk-WERSD76P.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-WJZILWLB": {"file": "chunk-WJZILWLB.js"}, "chunk-EX4XSTJC": {"file": "chunk-EX4XSTJC.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}