import{j as a,O as e,g as s,h as t,i as o,D as d,k as l,l as n}from"./chunk-CpdlqjqK.js";import{r as i}from"./chunk-b0HHmiEU.js";import{x as r}from"./index-DwRq5Uwb.js";import{X as c}from"./chunk-BGoCADfv.js";const m=l,f=n,p=i.forwardRef((({className:s,...t},o)=>a.jsx(e,{ref:o,className:r("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...t})));p.displayName=e.displayName;const x=i.forwardRef((({className:e,children:o,...d},l)=>a.jsxs(f,{children:[a.jsx(p,{}),a.jsxs(s,{ref:l,className:r("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...d,children:[o,a.jsxs(t,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(c,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]})));x.displayName=s.displayName;const u=({className:e,...s})=>a.jsx("div",{className:r("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});u.displayName="DialogHeader";const g=({className:e,...s})=>a.jsx("div",{className:r("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});g.displayName="DialogFooter";const N=i.forwardRef((({className:e,...s},t)=>a.jsx(o,{ref:t,className:r("text-lg font-semibold leading-none tracking-tight",e),...s})));N.displayName=o.displayName;const j=i.forwardRef((({className:e,...s},t)=>a.jsx(d,{ref:t,className:r("text-sm text-muted-foreground",e),...s})));j.displayName=d.displayName;export{m as D,x as a,u as b,N as c,j as d,g as e};
