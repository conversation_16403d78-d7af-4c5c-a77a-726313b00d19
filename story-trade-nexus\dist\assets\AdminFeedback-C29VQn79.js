import{j as e}from"./chunk-CpdlqjqK.js";import{r as s}from"./chunk-b0HHmiEU.js";import{f as a}from"./chunk-D1Z7_RhR.js";import{u as t,k as r,c as l,t as n}from"./index-DwRq5Uwb.js";import{S as i,g as d,a as c,m}from"./chunk-JuOXOFq2.js";import{D as x,a as o,b as h,c as j,d as u}from"./chunk-BjtUNXro.js";import{C as g,a as p,b as f,c as y}from"./chunk-D25sxu8E.js";import{A as N}from"./chunk-BS5eevMn.js";import{k as b,R as v,l as w,o as k,y as R,z as A,m as M,E as C}from"./chunk-BGoCADfv.js";import"./chunk-Cw96wKwP.js";import"./chunk-BvIisuNF.js";import"./chunk-DgNUPAoM.js";const F=()=>{var F;t();const[S,H]=s.useState([]),[D,E]=s.useState(null),[z,B]=s.useState(!0),[T,U]=s.useState(null),[q,O]=s.useState(!1),P=async()=>{try{B(!0);const[e,s]=await Promise.all([d(),c()]);H(e),E(s)}catch(e){n({title:"Error",description:"Failed to load feedback data. Please try again.",variant:"destructive"})}finally{B(!1)}};s.useEffect((()=>{P()}),[]);const V=e=>({"Bug Report":"bg-red-100 text-red-800","Feature Request":"bg-blue-100 text-blue-800","General Feedback":"bg-green-100 text-green-800","Technical Support":"bg-yellow-100 text-yellow-800","Account Issues":"bg-purple-100 text-purple-800"}[e]||"bg-gray-100 text-gray-800"),G=S.filter((e=>!e.isRead)).length;return e.jsxs(N,{title:"Feedback Management",description:"View and manage user feedback submissions",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Feedback Management"}),e.jsxs("p",{className:"text-gray-600",children:["View and respond to user feedback and support requests",G>0&&e.jsxs(r,{variant:"destructive",className:"ml-2",children:[G," unread"]})]})]}),e.jsx(l,{onClick:P,variant:"outline",className:"mt-4 md:mt-0",disabled:z,children:z?e.jsxs(e.Fragment,{children:[e.jsx(b,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(v,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),D&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs(g,{children:[e.jsxs(p,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(f,{className:"text-sm font-medium",children:"Total Submissions"}),e.jsx(w,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(y,{children:e.jsx("div",{className:"text-2xl font-bold",children:D.totalSubmissions})})]}),e.jsxs(g,{children:[e.jsxs(p,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(f,{className:"text-sm font-medium",children:"Unread"}),e.jsx(k,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(y,{children:e.jsx("div",{className:"text-2xl font-bold text-red-600",children:D.unreadCount})})]}),e.jsxs(g,{children:[e.jsxs(p,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(f,{className:"text-sm font-medium",children:"Average Rating"}),e.jsx(R,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(y,{children:[e.jsx("div",{className:"text-2xl font-bold",children:D.averageRating||"N/A"}),D.averageRating>0&&e.jsx(i,{value:D.averageRating,readonly:!0,size:"sm",showText:!1})]})]}),e.jsxs(g,{children:[e.jsxs(p,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(f,{className:"text-sm font-medium",children:"Most Common"}),e.jsx(A,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(y,{children:e.jsx("div",{className:"text-sm font-bold",children:(null==(F=Object.entries(D.categoryBreakdown).sort((([,e],[,s])=>s-e))[0])?void 0:F[0])||"N/A"})})]})]}),e.jsx("div",{className:"space-y-4",children:z?Array.from({length:3}).map(((s,a)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-4 animate-pulse",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),e.jsx("div",{className:"h-6 bg-gray-200 rounded w-20"})]}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-full mb-1"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]},a))):0===S.length?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(w,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No feedback yet"}),e.jsx("p",{className:"text-gray-500",children:"Feedback submissions will appear here when users submit them."})]}):S.map((s=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer "+(s.isRead?"":"border-l-4 border-burgundy-500"),onClick:()=>(async e=>{if(U(e),O(!0),!e.isRead&&e.id)try{await m(e.id),H((s=>s.map((s=>s.id===e.id?{...s,isRead:!0,readAt:new Date}:s)))),D&&E((e=>e?{...e,unreadCount:e.unreadCount-1}:null))}catch(s){}})(s),children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2 md:mb-0",children:[e.jsx("h3",{className:"font-medium text-navy-800",children:s.subject}),!s.isRead&&e.jsx(r,{variant:"default",children:"New"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(r,{className:V(s.category),children:s.category}),s.rating&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(R,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),e.jsx("span",{className:"text-sm text-gray-600",children:s.rating})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-2",children:[e.jsx("span",{className:"font-medium",children:s.name}),e.jsx("span",{children:s.email}),e.jsx("span",{children:a(new Date(1e3*s.createdAt.seconds),"MMM dd, yyyy HH:mm")})]}),e.jsx("p",{className:"text-gray-700 line-clamp-2",children:s.message})]},s.id)))}),e.jsx(x,{open:q,onOpenChange:O,children:e.jsxs(o,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[e.jsxs(h,{children:[e.jsxs(j,{className:"flex items-center gap-2",children:[e.jsx(w,{className:"h-5 w-5"}),null==T?void 0:T.subject]}),e.jsxs(u,{children:["Feedback submitted by ",null==T?void 0:T.name," on"," ",T&&a(new Date(1e3*T.createdAt.seconds),"MMMM dd, yyyy at HH:mm")]})]}),T&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Name:"}),e.jsx("p",{children:T.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Email:"}),e.jsx("p",{children:T.email})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Category:"}),e.jsx(r,{className:V(T.category),children:T.category})]}),T.rating&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Rating:"}),e.jsx(i,{value:T.rating,readonly:!0,size:"sm"})]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Message:"}),e.jsx("div",{className:"mt-2 p-3 bg-gray-50 rounded-md",children:e.jsx("p",{className:"whitespace-pre-wrap",children:T.message})})]}),T.userAgent&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"User Agent:"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:T.userAgent})]}),e.jsx("div",{className:"flex items-center gap-2 pt-4 border-t",children:T.isRead?e.jsxs("div",{className:"flex items-center gap-2 text-green-600",children:[e.jsx(M,{className:"h-4 w-4"}),e.jsxs("span",{className:"text-sm",children:["Read ",T.readAt&&a(new Date(1e3*T.readAt.seconds),"MMM dd, yyyy HH:mm")]})]}):e.jsxs("div",{className:"flex items-center gap-2 text-gray-500",children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Unread"})]})})]})]})})]})};export{F as default};
