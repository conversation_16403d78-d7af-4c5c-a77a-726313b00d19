import{D as n,G as e}from"./index-DwRq5Uwb.js";import{logEvent as t}from"./index.esm-CptSyBEO.js";import"./chunk-CpdlqjqK.js";import"./chunk-b0HHmiEU.js";import"./chunk-Cw96wKwP.js";import"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";import"./chunk-B1ZgTNj8.js";const o=async e=>{try{const t=await n(e);return t?{ownerPhone:t.phone||void 0,ownerName:t.displayName||"Unknown",ownerEmail:t.email||void 0,success:!0,message:"Owner contact information retrieved successfully"}:{success:!1,message:"Owner information not found",ownerName:"Unknown"}}catch(t){return{success:!1,message:"Failed to retrieve owner contact information",ownerName:"Unknown"}}},r=(n,o)=>{try{const r=n.replace(/[\s-\(\)]/g,""),c=`https://wa.me/${r}?text=${encodeURIComponent(o)}`;return window.open(c,"_blank"),e&&t(e,"contact_whatsapp_launched",{phone_number:r}),!0}catch(r){return!1}},c=async(n,o,r,c)=>{try{return e&&t(e,"contact_email_notification_sent",{book_title:o}),!0}catch(s){return!1}},s=(n,o,r,c)=>{e&&t(e,"book_owner_contacted",{book_id:n,owner_id:o,user_id:r,contact_method:c,timestamp:(new Date).toISOString()})};export{o as getOwnerContactInfo,r as launchWhatsApp,c as sendOwnerEmailNotification,s as trackContactInteraction};
