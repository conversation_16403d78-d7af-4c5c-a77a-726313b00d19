/**
 * React Hook for LCP Optimization
 *
 * This hook manages preloading of critical images for Largest Contentful Paint optimization.
 * It provides automatic preloading based on page type and user behavior.
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import { useEffect, useRef, useCallback } from 'react';
import {
  preloadFeaturedBooksImages,
  preloadBrowseBooksImages,
  clearPreloadLinks,
  initializeLCPOptimization,
  ImagePreloadConfig,
  PreloadLinkConfig,
  preloadImages,
} from '@/lib/headManager';
import { Book } from '@/types';

// Hook configuration options
interface LCPOptimizationOptions {
  enabled?: boolean;
  maxPreloads?: number;
  delayMs?: number;
  pageType?: 'homepage' | 'browse' | 'detail' | 'custom';
  priority?: 'high' | 'low' | 'auto';
}

// Hook return type
interface LCPOptimizationReturn {
  preloadBookImages: (books: Book[]) => void;
  preloadCustomImages: (images: ImagePreloadConfig[]) => void;
  clearPreloads: () => void;
  isEnabled: boolean;
}

/**
 * Hook for managing LCP optimization through image preloading
 */
export const useLCPOptimization = (
  options: LCPOptimizationOptions = {}
): LCPOptimizationReturn => {
  const {
    enabled = true,
    maxPreloads = 8,
    delayMs = 0,
    pageType = 'custom',
    priority = 'high',
  } = options;

  const preloadedUrls = useRef<Set<string>>(new Set());
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialized = useRef(false);

  // Initialize LCP optimization on mount
  useEffect(() => {
    if (enabled && !isInitialized.current) {
      initializeLCPOptimization();
      isInitialized.current = true;
    }

    // Cleanup on unmount
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      clearPreloadLinks();
    };
  }, [enabled]);

  // Clear preloads when page type changes
  useEffect(() => {
    clearPreloadLinks();
    preloadedUrls.current.clear();
  }, [pageType]);

  /**
   * Preloads book images based on page type and configuration
   */
  const preloadBookImages = useCallback((books: Book[]) => {
    if (!enabled || books.length === 0) {
      return;
    }

    const executePreload = () => {
      // Filter out already preloaded images
      const newBooks = books.filter(book =>
        book.imageUrl && !preloadedUrls.current.has(book.imageUrl)
      );

      if (newBooks.length === 0) {
        return;
      }

      // Mark URLs as preloaded
      newBooks.forEach(book => {
        if (book.imageUrl) {
          preloadedUrls.current.add(book.imageUrl);
        }
      });

      // Preload based on page type
      switch (pageType) {
        case 'homepage':
          preloadFeaturedBooksImages(
            newBooks.slice(0, Math.min(6, maxPreloads)).map(book => ({
              imageUrl: book.imageUrl,
              id: book.id,
            }))
          );
          break;

        case 'browse':
          preloadBrowseBooksImages(
            newBooks.slice(0, Math.min(8, maxPreloads)).map(book => ({
              imageUrl: book.imageUrl,
              id: book.id,
            }))
          );
          break;

        case 'custom':
        default:
          // Custom preloading logic
          const images: ImagePreloadConfig[] = newBooks
            .slice(0, maxPreloads)
            .map((book, index) => ({
              url: book.imageUrl,
              priority: index < 4 ? 'high' : 'low',
              isAboveFold: index < 6,
            }));

          preloadImages(images);
          break;
      }

      console.log(`[LCP Optimization] Preloaded ${newBooks.length} book images for ${pageType} page`);
    };

    // Execute immediately or with delay
    if (delayMs > 0) {
      timeoutRef.current = setTimeout(executePreload, delayMs);
    } else {
      executePreload();
    }
  }, [enabled, pageType, maxPreloads, delayMs]);

  /**
   * Preloads custom images with full control
   */
  const preloadCustomImages = useCallback((images: ImagePreloadConfig[]) => {
    if (!enabled || images.length === 0) {
      return;
    }

    const executePreload = () => {
      // Filter out already preloaded images
      const newImages = images.filter(image =>
        !preloadedUrls.current.has(image.url)
      );

      if (newImages.length === 0) {
        return;
      }

      // Mark URLs as preloaded
      newImages.forEach(image => {
        preloadedUrls.current.add(image.url);
      });

      // Apply default priority if not specified
      const imagesWithPriority = newImages.map(image => ({
        ...image,
        priority: image.priority || priority,
      }));

      preloadImages(imagesWithPriority.slice(0, maxPreloads));

      console.log(`[LCP Optimization] Preloaded ${newImages.length} custom images`);
    };

    // Execute immediately or with delay
    if (delayMs > 0) {
      timeoutRef.current = setTimeout(executePreload, delayMs);
    } else {
      executePreload();
    }
  }, [enabled, maxPreloads, delayMs, priority]);

  /**
   * Clears all preload links and resets tracking
   */
  const clearPreloads = useCallback(() => {
    clearPreloadLinks();
    preloadedUrls.current.clear();

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    console.log('[LCP Optimization] Cleared all preload links');
  }, []);

  return {
    preloadBookImages,
    preloadCustomImages,
    clearPreloads,
    isEnabled: enabled,
  };
};

/**
 * Hook specifically for homepage featured books preloading
 */
export const useHomepageLCPOptimization = (books: Book[], enabled: boolean = true) => {
  const { preloadBookImages } = useLCPOptimization({
    enabled,
    pageType: 'homepage',
    maxPreloads: 6,
    delayMs: 100, // Small delay to avoid blocking critical resources
  });

  useEffect(() => {
    if (books.length > 0) {
      preloadBookImages(books);
    }
  }, [books, preloadBookImages]);
};

/**
 * Hook specifically for browse books page preloading
 */
export const useBrowseBooksLCPOptimization = (books: Book[], enabled: boolean = true) => {
  const { preloadBookImages } = useLCPOptimization({
    enabled,
    pageType: 'browse',
    maxPreloads: 8,
    delayMs: 50, // Minimal delay for browse page
  });

  useEffect(() => {
    if (books.length > 0) {
      preloadBookImages(books);
    }
  }, [books, preloadBookImages]);
};

/**
 * Hook for intersection-based preloading (progressive enhancement)
 */
export const useIntersectionPreloading = (
  enabled: boolean = true,
  threshold: number = 0.1
) => {
  const observerRef = useRef<IntersectionObserver | null>(null);
  const { preloadCustomImages } = useLCPOptimization({ enabled });

  useEffect(() => {
    if (!enabled || typeof window === 'undefined' || !('IntersectionObserver' in window)) {
      return;
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.target instanceof HTMLImageElement) {
            const img = entry.target;
            const src = img.dataset.src || img.src;

            if (src) {
              preloadCustomImages([{
                url: src,
                priority: 'low',
                isAboveFold: false,
              }]);
            }

            // Stop observing this element
            observerRef.current?.unobserve(entry.target);
          }
        });
      },
      { threshold }
    );

    return () => {
      observerRef.current?.disconnect();
    };
  }, [enabled, threshold, preloadCustomImages]);

  const observeElement = useCallback((element: HTMLElement) => {
    if (observerRef.current && element) {
      observerRef.current.observe(element);
    }
  }, []);

  return { observeElement };
};
