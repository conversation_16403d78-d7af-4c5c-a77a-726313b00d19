import{j as e}from"./chunk-CpdlqjqK.js";import{r as s,L as a}from"./chunk-b0HHmiEU.js";import{u as i,U as r,H as l,c as n,j as c,r as t,D as m,E as d}from"./index-DwRq5Uwb.js";import{J as o}from"./chunk-Cw96wKwP.js";import{q as x,o as h,R as j,C as g}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const u=()=>{const{currentUser:u,isAdmin:f,checkAdminStatus:N,refreshUserData:b}=i(),[p,y]=s.useState(!1),[v,w]=s.useState(!1),[A,R]=s.useState(null),[k,D]=s.useState(null),[S,F]=s.useState([]),[E,U]=s.useState(!1);s.useEffect((()=>{const e=console.log,s=console.error;return console.log=(...s)=>{e(...s),F((e=>[...e,`LOG: ${s.map((e=>String(e))).join(" ")}`]))},console.error=(...e)=>{s(...e),F((s=>[...s,`ERROR: ${e.map((e=>String(e))).join(" ")}`]))},()=>{console.log=e,console.error=s}}),[]);const Y="<EMAIL>"===(null==u?void 0:u.email),C=(null==A?void 0:A.role)===r.Admin;return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(l,{}),e.jsx("main",{className:"flex-grow flex items-center justify-center py-8",children:e.jsxs("div",{className:"max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(x,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Access Diagnostic"}),e.jsxs("p",{className:"text-gray-600",children:["Troubleshooting admin access for ",(null==u?void 0:u.email)||"Not signed in"]})]}),u?e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"User Information:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"User ID:"}),e.jsx("span",{className:"text-gray-700",children:u.uid})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Email:"}),e.jsx("span",{className:"text-gray-700",children:u.email})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Email Verified:"}),e.jsx("span",{className:""+(u.emailVerified?"text-green-600":"text-red-600"),children:u.emailVerified?"Yes":"No"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Admin Status:"}),e.jsx("span",{className:""+(f?"text-green-600":"text-red-600"),children:f?"Yes":"No"})]})]})]}),A&&e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Firestore Document:"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Role:"}),e.jsx("span",{className:""+(C?"text-green-600":"text-red-600"),children:A.role||"Not set"})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx("span",{className:"font-medium mr-2",children:"Display Name:"}),e.jsx("span",{className:"text-gray-700",children:A.displayName||"Not set"})]})]})]}),e.jsxs("div",{className:"flex flex-col md:flex-row justify-center gap-4 mb-6",children:[e.jsx(n,{onClick:async()=>{if(u)try{w(!0),F([]);const e=await m(u.uid);R(e);const s=await N();D(s)}catch(e){}finally{w(!1)}else o.error("You must be signed in to run diagnostics")},disabled:v,className:"flex items-center",children:v?e.jsxs(e.Fragment,{children:[e.jsx(c,{size:"sm",className:"mr-2"}),"Running Diagnostics..."]}):e.jsxs(e.Fragment,{children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"Run Diagnostics"]})}),e.jsx(n,{onClick:async()=>{if(u)try{y(!0),F([]),await d(u.uid),await b();const e=await N();D(e);const s=await m(u.uid);R(s),U(!0),o.success("Admin access fix attempted")}catch(e){o.error("Failed to fix admin access")}finally{y(!1)}else o.error("You must be signed in to fix admin access")},disabled:p||!Y,variant:Y?"default":"outline",className:"flex items-center",children:p?e.jsxs(e.Fragment,{children:[e.jsx(c,{size:"sm",className:"mr-2"}),"Fixing Access..."]}):e.jsxs(e.Fragment,{children:[e.jsx(x,{className:"h-4 w-4 mr-2"}),"Fix Admin Access"]})})]}),!Y&&e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6",children:e.jsxs("p",{className:"font-semibold flex items-center",children:[e.jsx(h,{className:"h-5 w-5 mr-2"}),"Admin fix is only <NAME_EMAIL>"]})}),E&&k&&e.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center",children:[e.jsx(g,{className:"h-5 w-5 mr-2"}),e.jsx("p",{children:"Admin access fixed successfully! Try accessing the admin dashboard now."})]})]}):e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded mb-6",children:e.jsxs("p",{className:"font-semibold flex items-center",children:[e.jsx(h,{className:"h-5 w-5 mr-2"}),"You must be signed in to run diagnostics"]})}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Diagnostic Logs:"}),e.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono",children:S.length>0?S.map(((s,a)=>e.jsx("div",{className:"py-1 "+(s.startsWith("ERROR")?"text-red-600":"text-gray-700"),children:s},a))):e.jsx("p",{className:"text-gray-500 italic",children:"No logs available yet. Run diagnostics to see logs."})})]}),e.jsxs("div",{className:"flex justify-center mt-8",children:[e.jsx(a,{to:"/",children:e.jsx(n,{variant:"outline",className:"mr-4",children:"Return to Home"})}),e.jsx(a,{to:"/admin",children:e.jsx(n,{children:"Try Admin Dashboard"})})]})]})}),e.jsx(t,{})]})};export{u as default};
