const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-B1ZgTNj8.js","assets/chunk-CpdlqjqK.js","assets/chunk-b0HHmiEU.js","assets/index-DwRq5Uwb.js","assets/chunk-Cw96wKwP.js","assets/chunk-BGoCADfv.js","assets/chunk-BvIisuNF.js","assets/chunk-D1Z7_RhR.js","assets/chunk-DgNUPAoM.js","assets/index-BGvwYOHI.css"])))=>i.map(i=>d[i]);
import{_ as e,Z as i,u as t,H as r,F as n,l as o,m as s,n as l,o as a,I as d,p as c,a as u,r as h,q as m,v as x}from"./index-DwRq5Uwb.js";import{j as g}from"./chunk-CpdlqjqK.js";import{u as p,r as j}from"./chunk-b0HHmiEU.js";import{T as b}from"./chunk-DQKmWaJS.js";import{z as f,J as y}from"./chunk-Cw96wKwP.js";import{u as v}from"./chunk-DgNUPAoM.js";import{getCurrentPosition as w}from"./geolocationUtils-DEQwKbhP.js";import{w as N,X as k,V as S,i as A,o as P,W as F}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";const R={maxWidth:1200,maxHeight:1200,webpQuality:.85,jpegQuality:.85,maxFileSize:5242880,targetFileSize:204800,supportedFormats:["image/jpeg","image/jpg","image/png","image/webp"],outputFormat:"webp",fallbackFormat:"jpeg"};function D(){return new Promise((e=>{const i=new Image;i.onload=i.onerror=()=>{e(2===i.height)},i.src="data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA"}))}function E(e){return R.supportedFormats.includes(e.type)?e.size>R.maxFileSize?{valid:!1,error:`File size too large. Maximum size: ${R.maxFileSize/1024/1024}MB`}:{valid:!0}:{valid:!1,error:`Unsupported file format. Supported formats: ${R.supportedFormats.join(", ")}`}}function C(e,i="webp",t=.85){return new Promise(((r,n)=>{e.toBlob((e=>{e?r(e):n(new Error("Failed to convert canvas to blob"))}),`image/${i}`,t)}))}async function I(e,i={}){const t=E(e);if(!t.valid)throw new Error(t.error);const{maxWidth:r=R.maxWidth,maxHeight:n=R.maxHeight,quality:o=R.webpQuality,format:s=(await D()?"webp":R.fallbackFormat),targetSize:l=R.targetFileSize}=i;try{const i=await function(e){return new Promise(((i,t)=>{const r=new Image;r.onload=()=>i(r),r.onerror=()=>t(new Error("Failed to load image")),r.src=URL.createObjectURL(e)}))}(e),{width:t,height:a}=function(e,i,t=R.maxWidth,r=R.maxHeight){let{width:n,height:o}={width:e,height:i};const s=t/n,l=r/o,a=Math.min(s,l,1);return n=Math.round(n*a),o=Math.round(o*a),{width:n,height:o}}(i.naturalWidth,i.naturalHeight,r,n),d=function(e,i){const t=document.createElement("canvas");return t.width=e,t.height=i,t}(t,a),c=d.getContext("2d");if(!c)throw new Error("Failed to get canvas context");c.imageSmoothingEnabled=!0,c.imageSmoothingQuality="high",c.drawImage(i,0,0,t,a),URL.revokeObjectURL(i.src);let u=await C(d,s,o),h=o;for(;u.size>l&&h>.3;)h-=.1,u=await C(d,s,h);const m=new File([u],`${e.name.split(".")[0]}.${s}`,{type:`image/${s}`});return{file:m,originalSize:e.size,processedSize:m.size,format:s}}catch(a){throw new Error(`Image processing failed: ${a instanceof Error?a.message:"Unknown error"}`)}}const B="https://via.placeholder.com/150?text=No+Image",U=async(t,r,n,o={})=>{try{if(!t)return B;if(!r)return B;if(!E(t).valid)return B;let a=t,d=r;if(!o.skipProcessing)try{n&&n(10);const e=await I(t,{maxWidth:o.maxWidth,maxHeight:o.maxHeight,quality:o.quality});a=e.file,e.format!==t.type.split("/")[1]&&(d=r.replace(/\.[^.]+$/,`.${e.format}`)),n&&n(30)}catch(s){}const{ref:c,uploadBytes:u,getDownloadURL:h}=await e((async()=>{const{ref:e,uploadBytes:i,getDownloadURL:t}=await import("./chunk-B1ZgTNj8.js").then((e=>e.l));return{ref:e,uploadBytes:i,getDownloadURL:t}}),__vite__mapDeps([0,1,2])),m=c(i,d),x={contentType:a.type,customMetadata:{originalName:t.name,originalSize:t.size.toString(),processedSize:a.size.toString(),uploadedAt:(new Date).toISOString()}};n&&n(50);try{const e=await u(m,a,x);n&&n(90);const i=await h(e.ref);return n&&n(100),i}catch(l){throw Error,l}}catch(a){return Error,B}},L=(e,i)=>`book-images/${e}/${(new Date).getTime()}-${Math.random().toString(36).substring(2,6)}.webp`,H=f.object({title:f.string().min(1,{message:"Book title is required"}),author:f.string().min(1,{message:"Author name is required"}),isbn:f.string().optional(),genre:f.string().min(1,{message:"Please select at least one genre"}),condition:f.string().min(1,{message:"Please select a condition"}),description:f.string().min(10,{message:"Description should be at least 10 characters"}),availability:f.string().min(1,{message:"Please select availability option"}),price:f.string().optional(),rentalPrice:f.string().optional(),rentalPeriod:f.string().optional(),securityDepositRequired:f.boolean().optional().default(!1),securityDepositAmount:f.string().optional()}),q=()=>{const i=p(),[f,R]=j.useState(!1);j.useState("");const[D,E]=j.useState([]),[C,I]=j.useState([]),[q,z]=j.useState(0),[M,T]=j.useState(0),[W,$]=j.useState(!1),[_,G]=j.useState(null),[Y,O]=j.useState(null),{currentUser:V,userData:Q}=t(),J=v({resolver:m(H),defaultValues:{title:"",author:"",isbn:"",genre:"",condition:"",description:"",availability:"",price:"",rentalPrice:"",rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:""}});return g.jsxs("div",{className:"min-h-screen flex flex-col",children:[g.jsx(r,{}),g.jsx("main",{className:"flex-grow",children:g.jsx("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:g.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[g.jsxs("div",{className:"text-center mb-6",children:[g.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Add Your Book"}),g.jsx("p",{className:"text-gray-600",children:"Share your book with the community"})]}),g.jsx(n,{...J,children:g.jsxs("form",{onSubmit:J.handleSubmit((async t=>{var r,n;R(!0);try{const{createBook:a}=await e((async()=>{const{createBook:e}=await import("./index-DwRq5Uwb.js").then((e=>e.a2));return{createBook:e}}),__vite__mapDeps([3,1,2,4,5,6,7,8,9]));if(!V)return y.error("You must be signed in to add a book"),void R(!1);let d=null;d=await(async()=>{$(!0),G(null);try{const e=await w({enableHighAccuracy:!0,timeout:15e3,maximumAge:6e4});return O(e),e}catch(e){let i="Unable to get your current location.";return e instanceof Error&&(e.message.includes("permission")?i="Location permission denied. Using your registered address instead.":e.message.includes("timeout")?i="Location request timed out. Using your registered address instead.":e.message.includes("unavailable")&&(i="Location service unavailable. Using your registered address instead.")),G(i),null}finally{$(!1)}})(),d||(d=await(async()=>{if(!(null==Q?void 0:Q.pincode))return null;try{const e={500001:{latitude:17.385,longitude:78.4867},500032:{latitude:17.4399,longitude:78.3489},500081:{latitude:17.4485,longitude:78.3908},400001:{latitude:18.9322,longitude:72.8264},400051:{latitude:19.0596,longitude:72.8295},110001:{latitude:28.6139,longitude:77.209},110016:{latitude:28.5494,longitude:77.2001},560001:{latitude:12.9716,longitude:77.5946},560066:{latitude:12.9698,longitude:77.75},600001:{latitude:13.0827,longitude:80.2707},600028:{latitude:13.0569,longitude:80.2091}}[Q.pincode];if(e)return e;const i=Q.pincode.substring(0,2);return{50:{latitude:17.385,longitude:78.4867},40:{latitude:19.076,longitude:72.8777},11:{latitude:28.7041,longitude:77.1025},56:{latitude:12.9716,longitude:77.5946},60:{latitude:13.0827,longitude:80.2707},70:{latitude:22.5726,longitude:88.3639},30:{latitude:26.9124,longitude:75.7873},22:{latitude:26.8467,longitude:80.9462}}[i]||null}catch(e){return null}})());let c=null;try{c=await(async()=>{if(!(null==V?void 0:V.uid))return null;try{await x();const{doc:i,getDoc:t,getFirestore:r}=await e((async()=>{const{doc:e,getDoc:i,getFirestore:t}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,getDoc:i,getFirestore:t}}),__vite__mapDeps([0,1,2])),n=i(r(),"users",V.uid),o=await t(n);if(o.exists()){const e=o.data().community;return e&&"string"==typeof e&&""!==e.trim()?e.trim():null}return null}catch(i){return null}})(),c?y.info(`Community "${c}" will be added to your book listing`):y.warning("No community information found in your profile. Consider updating your profile to help others find books in your area.")}catch(o){y.error("Failed to retrieve community information. Your book will be listed without community data.")}let u=[];try{u=t.genre.split(",").map((e=>e.trim())).filter((e=>e.length>0)),0===u.length&&(u=[t.genre.trim()])}catch(s){u=[t.genre.trim()]}let h=null;if(t.price&&(h=Number(t.price),isNaN(h)))return y.error("Invalid price value. Please enter a valid number."),void R(!1);let m=null;if(t.rentalPrice&&(m=Number(t.rentalPrice),isNaN(m)))return y.error("Invalid rental price value. Please enter a valid number."),void R(!1);let g=t.rentalPeriod;m||(g=null);let p=null;if(t.securityDepositRequired&&t.securityDepositAmount&&(p=Number(t.securityDepositAmount),isNaN(p)))return y.error("Invalid security deposit amount. Please enter a valid number."),void R(!1);let j=[],b="https://via.placeholder.com/150?text=No+Image";if(D.length>0)try{y.info("Uploading images..."),j=await(async(e,i,t)=>{try{if(!e||0===e.length)return[B];if(!i)return[B];const r=[],n=e.length;let o=0;for(const a of e){const e=L(i,a.name),s=U(a,e,(e=>{if(100===e&&(o++,t)){const e=Math.round(o/n*100);t(e)}}));r.push(s)}const s=await Promise.all(r),l=s.filter((e=>e!==B));return 0===l.length&&s.length>0?[B]:l}catch(r){return Error,[B]}})(D,V.uid,(e=>{T(e)})),j.length>0&&(b=j[q]||j[0])}catch(l){y.error("Failed to upload images. Using default image instead.")}const f={title:t.title.trim(),author:t.author.trim(),isbn:(null==(r=t.isbn)?void 0:r.trim())||null,genre:u,condition:t.condition,description:t.description.trim(),imageUrl:b,imageUrls:j.length>0?j:void 0,displayImageIndex:j.length>0?q:void 0,availability:t.availability,price:h,rentalPrice:m,rentalPeriod:g,securityDepositRequired:t.securityDepositRequired,securityDepositAmount:p,ownerId:V.uid,ownerName:V.displayName||(null==(n=V.email)?void 0:n.split("@")[0])||"Unknown",ownerEmail:V.email||void 0,ownerCommunity:c||void 0,ownerCoordinates:d,ownerPincode:(null==Q?void 0:Q.pincode)||void 0,ownerRating:0,perceivedValue:5};await a(f);y.success("Book added successfully! It will be visible after admin approval."),i("/browse")}catch(a){let e="Failed to add book. Please try again.";a instanceof Error&&(e=`Error: ${a.message}`,a.message.includes("permission-denied")?e="You don't have permission to add books. Please check your account.":a.message.includes("network")?e="Network error. Please check your internet connection and try again.":a.message.includes("quota-exceeded")&&(e="Database quota exceeded. Please try again later.")),y.error(e)}finally{R(!1),T(0)}})),className:"space-y-6",children:[g.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[g.jsxs("div",{className:"space-y-6",children:[g.jsx(o,{control:J.control,name:"title",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Book Title*"}),g.jsx(a,{children:g.jsx(d,{placeholder:"Enter book title",disabled:f,...e})}),g.jsx(c,{})]})}),g.jsx(o,{control:J.control,name:"author",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Author*"}),g.jsx(a,{children:g.jsx(d,{placeholder:"Enter author name",disabled:f,...e})}),g.jsx(c,{})]})}),g.jsx(o,{control:J.control,name:"isbn",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"ISBN (Optional)"}),g.jsx(a,{children:g.jsx(d,{placeholder:"Enter ISBN",disabled:f,...e})}),g.jsx(c,{})]})}),g.jsx(o,{control:J.control,name:"genre",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Genre*"}),g.jsx(a,{children:g.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:f,...e,children:[g.jsx("option",{value:"Fantasy",children:"Fantasy"}),g.jsx("option",{value:"Science Fiction",children:"Science Fiction"}),g.jsx("option",{value:"Mystery",children:"Mystery"}),g.jsx("option",{value:"Thriller",children:"Thriller"}),g.jsx("option",{value:"Horror",children:"Horror"}),g.jsx("option",{value:"Romance",children:"Romance"}),g.jsx("option",{value:"Comedy",children:"Comedy"}),g.jsx("option",{value:"Drama",children:"Drama"}),g.jsx("option",{value:"Historical Fiction",children:"Historical Fiction"}),g.jsx("option",{value:"Paranormal",children:"Paranormal"}),g.jsx("option",{value:"Adventure",children:"Adventure"}),g.jsx("option",{value:"Action",children:"Action"}),g.jsx("option",{value:"Western",children:"Western"}),g.jsx("option",{value:"Literary Fiction",children:"Literary Fiction"}),g.jsx("option",{value:"Dystopian",children:"Dystopian"}),g.jsx("option",{value:"Coming-of-Age",children:"Coming-of-Age"}),g.jsx("option",{value:"Young Adult (YA)",children:"Young Adult (YA)"}),g.jsx("option",{value:"Children’s",children:"Children’s"}),g.jsx("option",{value:"Biography",children:"Biography"}),g.jsx("option",{value:"Memoir",children:"Memoir"}),g.jsx("option",{value:"Self-Help",children:"Self-Help"}),g.jsx("option",{value:"Psychology",children:"Psychology"}),g.jsx("option",{value:"Philosophy",children:"Philosophy"}),g.jsx("option",{value:"Business",children:"Business"}),g.jsx("option",{value:"Finance",children:"Finance"}),g.jsx("option",{value:"Leadership",children:"Leadership"}),g.jsx("option",{value:"Science",children:"Science"}),g.jsx("option",{value:"Technology",children:"Technology"}),g.jsx("option",{value:"History",children:"History"}),g.jsx("option",{value:"Politics",children:"Politics"}),g.jsx("option",{value:"Cooking",children:"Cooking"}),g.jsx("option",{value:"Travel",children:"Travel"}),g.jsx("option",{value:"Health & Wellness",children:"Health & Wellness"}),g.jsx("option",{value:"Religion",children:"Religion"}),g.jsx("option",{value:"Spirituality",children:"Spirituality"}),g.jsx("option",{value:"Parenting",children:"Parenting"}),g.jsx("option",{value:"Home & Garden",children:"Home & Garden"}),g.jsx("option",{value:"Art & Design",children:"Art & Design"}),g.jsx("option",{value:"Graphic Novel",children:"Graphic Novel"}),g.jsx("option",{value:"Comic Book",children:"Comic Book"}),g.jsx("option",{value:"Manga",children:"Manga"}),g.jsx("option",{value:"Classic",children:"Classic"}),g.jsx("option",{value:"Poetry",children:"Poetry"}),g.jsx("option",{value:"Essays",children:"Essays"}),g.jsx("option",{value:"Anthology",children:"Anthology"}),g.jsx("option",{value:"Short Stories",children:"Short Stories"}),g.jsx("option",{value:"Education",children:"Education"}),g.jsx("option",{value:"Reference",children:"Reference"}),g.jsx("option",{value:"True Crime",children:"True Crime"}),g.jsx("option",{value:"Inspirational",children:"Inspirational"})]})}),g.jsx(c,{})]})}),g.jsx(o,{control:J.control,name:"condition",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Condition*"}),g.jsx(a,{children:g.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:f,...e,children:[g.jsx("option",{value:"",children:"Select Condition"}),g.jsx("option",{value:"New",children:"New"}),g.jsx("option",{value:"Like New",children:"Like New"}),g.jsx("option",{value:"Good",children:"Good"}),g.jsx("option",{value:"Fair",children:"Fair"})]})}),g.jsx(c,{})]})})]}),g.jsxs("div",{className:"space-y-6",children:[g.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:[g.jsxs("div",{className:"flex flex-col items-center justify-center mb-4",children:[g.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Book Images"}),g.jsx("p",{className:"text-sm text-gray-500 mb-2 text-center",children:"Upload up to 4 images of your book. The first image will be the display image."}),g.jsxs("div",{className:"flex items-center justify-center mb-2",children:[g.jsxs("span",{className:"text-sm font-medium text-gray-700 mr-2",children:[D.length,"/4 images"]}),M>0&&M<100&&g.jsx("div",{className:"w-24 h-2 bg-gray-200 rounded-full overflow-hidden",children:g.jsx("div",{className:"h-full bg-burgundy-500",style:{width:`${M}%`}})})]})]}),C.length>0&&g.jsx("div",{className:"grid grid-cols-2 gap-4 mb-4",children:C.map(((e,i)=>g.jsxs("div",{className:"relative border rounded-md overflow-hidden "+(i===q?"ring-2 ring-burgundy-500":""),children:[g.jsx("img",{src:e,alt:`Book image ${i+1}`,className:"w-full h-32 object-contain"}),g.jsxs("div",{className:"absolute top-0 right-0 p-1 flex space-x-1",children:[i!==q&&g.jsx("button",{type:"button",className:"bg-burgundy-500 text-white p-1 rounded-full hover:bg-burgundy-600",onClick:()=>(e=>{z(e)})(i),title:"Set as display image",children:g.jsx(N,{className:"h-4 w-4"})}),g.jsx("button",{type:"button",className:"bg-gray-700 text-white p-1 rounded-full hover:bg-gray-800",onClick:()=>(e=>{const i=[...D],t=[...C];i.splice(e,1),t.splice(e,1),E(i),I(t),e===q?(i.length,z(0)):e<q&&z(q-1)})(i),title:"Remove image",children:g.jsx(k,{className:"h-4 w-4"})})]}),i===q&&g.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-burgundy-500 text-white text-xs py-1 text-center",children:"Display Image"})]},i)))}),D.length<4&&g.jsxs("div",{className:"flex justify-center",children:[g.jsx("input",{type:"file",id:"image-upload",className:"hidden",accept:"image/*",multiple:D.length<3,onChange:e=>{const i=e.target.files;if(!i)return;if(D.length+i.length>4)return void y.error("Maximum 4 images allowed");const t=Array.from(i),r=[...D,...t];E(r);const n=t.map((e=>URL.createObjectURL(e))),o=[...C,...n];I(o),0===D.length&&t.length>0&&z(0)},disabled:f}),g.jsxs("label",{htmlFor:"image-upload",className:"flex items-center justify-center text-sm bg-burgundy-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-burgundy-600 "+(f?"opacity-50 cursor-not-allowed":""),children:[g.jsx(S,{className:"h-4 w-4 mr-2"}),0===D.length?"Upload Images":"Add More Images"]})]})]}),g.jsx(o,{control:J.control,name:"availability",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Availability*"}),g.jsx(a,{children:g.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:f,onChange:i=>{e.onChange(i)},value:e.value,children:[g.jsx("option",{value:"",children:"Select Availability"}),g.jsx("option",{value:"For Exchange",children:"For Exchange"}),g.jsx("option",{value:"For Sale",children:"For Sale"}),g.jsx("option",{value:"For Rent",children:"For Rent"}),g.jsx("option",{value:"For Sale & Exchange",children:"For Sale & Exchange"}),g.jsx("option",{value:"For Rent & Exchange",children:"For Rent & Exchange"}),g.jsx("option",{value:"For Rent & Sale",children:"For Rent & Sale"}),g.jsx("option",{value:"For Rent, Sale & Exchange",children:"For Rent, Sale & Exchange"})]})}),g.jsx(c,{})]})}),J.watch("availability")&&(J.watch("availability").includes("Sale")||J.watch("availability").includes("Rent"))&&g.jsxs("div",{className:"space-y-6",children:[J.watch("availability").includes("Sale")&&g.jsx(o,{control:J.control,name:"price",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Sale Price (₹)"}),g.jsx(a,{children:g.jsx(d,{type:"number",placeholder:"Enter price",disabled:f,...e})}),g.jsx(c,{})]})}),J.watch("availability").includes("Rent")&&g.jsxs(g.Fragment,{children:[g.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[g.jsx(o,{control:J.control,name:"rentalPrice",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Rental Price (₹)"}),g.jsx(a,{children:g.jsx(d,{type:"number",placeholder:"Enter rental price",disabled:f,...e})}),g.jsx(c,{})]})}),g.jsx(o,{control:J.control,name:"rentalPeriod",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Period"}),g.jsx(a,{children:g.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:f,...e,children:[g.jsx("option",{value:"per day",children:"Per Day"}),g.jsx("option",{value:"per week",children:"Per Week"}),g.jsx("option",{value:"per month",children:"Per Month"})]})}),g.jsx(c,{})]})})]}),g.jsx("div",{className:"mt-4",children:g.jsx(o,{control:J.control,name:"securityDepositRequired",render:({field:e})=>g.jsxs(s,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[g.jsx(a,{children:g.jsx("input",{type:"checkbox",className:"h-4 w-4 mt-1",checked:e.value,onChange:i=>e.onChange(i.target.checked),disabled:f})}),g.jsxs("div",{className:"space-y-1 leading-none",children:[g.jsx(l,{children:"Security Deposit Required"}),g.jsx("p",{className:"text-sm text-gray-500",children:"Require a security deposit for renting this book"})]})]})})}),J.watch("securityDepositRequired")&&g.jsx("div",{className:"mt-4",children:g.jsx(o,{control:J.control,name:"securityDepositAmount",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Security Deposit Amount (₹)"}),g.jsx(a,{children:g.jsx(d,{type:"number",placeholder:"Enter security deposit amount",disabled:f,...e})}),g.jsx(c,{})]})})})]})]})]})]}),g.jsx(o,{control:J.control,name:"description",render:({field:e})=>g.jsxs(s,{children:[g.jsx(l,{children:"Description*"}),g.jsx(a,{children:g.jsx(b,{placeholder:"Describe the book, its condition, and any other details...",className:"min-h-[120px]",disabled:f,...e})}),g.jsx(c,{})]})}),g.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:g.jsxs("div",{className:"flex items-start space-x-3",children:[g.jsx(A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),g.jsxs("div",{className:"flex-1",children:[g.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Location Information"}),g.jsx("p",{className:"text-sm text-blue-800 mb-2",children:"When you submit this book listing, we will automatically capture your current GPS location to help other users find books near them. This location data will be used solely for:"}),g.jsxs("ul",{className:"text-sm text-blue-800 list-disc list-inside space-y-1 mb-3",children:[g.jsx("li",{children:"Calculating and displaying distance to other users browsing books"}),g.jsx("li",{children:"Helping users find books in their local area"}),g.jsx("li",{children:"Improving the book discovery experience"})]}),g.jsx("p",{className:"text-sm text-blue-800",children:"If GPS location cannot be accessed, we'll use your registered address as a fallback. Your exact location will not be displayed to other users - only the calculated distance."}),W&&g.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-blue-700",children:[g.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),g.jsx("span",{children:"Capturing your location..."})]}),Y&&g.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-green-700",children:[g.jsx(N,{className:"h-4 w-4 text-green-600"}),g.jsx("span",{children:"Location captured successfully"})]}),_&&g.jsxs("div",{className:"mt-3 flex items-start space-x-2 text-sm text-amber-700",children:[g.jsx(P,{className:"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0"}),g.jsx("span",{children:_})]})]})]})}),g.jsxs("div",{className:"flex justify-end space-x-4",children:[g.jsx(u,{type:"button",variant:"outline",disabled:f,onClick:()=>i("/"),children:"Cancel"}),g.jsxs(u,{type:"submit",disabled:f,className:"flex items-center gap-2",children:[g.jsx(F,{className:"h-4 w-4"}),"Add Book"]})]})]})})]})})}),g.jsx(h,{})]})};export{q as default};
