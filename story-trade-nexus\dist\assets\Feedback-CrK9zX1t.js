import{j as e}from"./chunk-CpdlqjqK.js";import{r as s}from"./chunk-b0HHmiEU.js";import{u as a}from"./chunk-DgNUPAoM.js";import{x as r,u as t,q as l,H as i,F as n,l as o,m as c,n as d,o as m,I as u,p as x,c as h,r as g,t as p}from"./index-DwRq5Uwb.js";import{c as j,o as v,s as b,e as f,n as y}from"./chunk-Cw96wKwP.js";import{S as N,c as w,f as k,s as S}from"./chunk-JuOXOFq2.js";import{S as F,a as A,b as q,c as R,d as B}from"./chunk-BpJWUrWd.js";import{T as I}from"./chunk-DQKmWaJS.js";import{Q as T,c as P,M as Y,o as W,l as C,C as E,k as H,v as G}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";const M=j("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),V=s.forwardRef((({className:s,variant:a,...t},l)=>e.jsx("div",{ref:l,role:"alert",className:r(M({variant:a}),s),...t})));V.displayName="Alert";s.forwardRef((({className:s,...a},t)=>e.jsx("h5",{ref:t,className:r("mb-1 font-medium leading-none tracking-tight",s),...a}))).displayName="AlertTitle";const U=s.forwardRef((({className:s,...a},t)=>e.jsx("div",{ref:t,className:r("text-sm [&_p]:leading-relaxed",s),...a})));U.displayName="AlertDescription";const z=v({name:b().min(2,{message:"Name must be at least 2 characters"}).max(100),email:b().email({message:"Please enter a valid email address"}),subject:b().min(5,{message:"Subject must be at least 5 characters"}).max(200),category:f(["Bug Report","Feature Request","General Feedback","Technical Support","Account Issues"],{required_error:"Please select a category"}),message:b().min(10,{message:"Message must be at least 10 characters"}).max(2e3,{message:"Message must be less than 2000 characters"}),rating:y().min(1).max(5).optional()}),D=[{question:"How do I add books to the platform?",answer:"After creating an account and verifying your email, go to 'Add Your Books' in the navigation menu. Fill out the book details form with title, author, condition, and upload photos."},{question:"How does the book exchange process work?",answer:"Browse available books, contact the owner through WhatsApp or email, arrange the exchange details, and meet safely to exchange books. Always verify the book condition before finalizing."},{question:"Is my personal information safe?",answer:"Yes, we take privacy seriously. Your email is never displayed publicly, and we only share your contact information when you initiate contact with a book owner."},{question:"How do I report inappropriate content or users?",answer:"Use the 'Bug Report' or 'General Feedback' category in this form to report any issues. We review all reports promptly and take appropriate action."},{question:"Can I edit or delete my book listings?",answer:"Yes, go to your Dashboard to manage your book listings. You can edit details, update availability, or remove books from the platform."}],O=()=>{const{currentUser:r}=t(),[j,v]=s.useState(!1),[b,f]=s.useState(0),y=a({resolver:l(z),defaultValues:{name:(null==r?void 0:r.displayName)||"",email:(null==r?void 0:r.email)||"",subject:"",category:void 0,message:"",rating:void 0}});return e.jsxs("div",{className:"flex flex-col min-h-screen",children:[e.jsx(i,{}),e.jsx("main",{className:"flex-1 bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-12",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Feedback & Support"}),e.jsx("p",{className:"text-gray-600 max-w-3xl mx-auto",children:"We value your feedback and are here to help! Whether you've found a bug, have a feature request, or need technical support, we'd love to hear from you. Your input helps us make PeerBooks better for everyone."})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(T,{className:"h-6 w-6 text-burgundy-500 mr-3"}),e.jsx("h2",{className:"text-xl font-semibold text-navy-800",children:"Frequently Asked Questions"})]}),e.jsx("div",{className:"space-y-4",children:D.map(((s,a)=>e.jsxs("details",{className:"group",children:[e.jsx("summary",{className:"cursor-pointer text-sm font-medium text-gray-700 hover:text-burgundy-600 transition-colors",children:s.question}),e.jsx("p",{className:"mt-2 text-sm text-gray-600 leading-relaxed",children:s.answer})]},a)))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-navy-800 mb-4",children:"Need Immediate Help?"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(P,{className:"h-5 w-5 text-burgundy-500 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Response Time"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Within 24-48 hours"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(Y,{className:"h-5 w-5 text-burgundy-500 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Email Support"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Available 24/7"})]})]})]}),e.jsxs(V,{className:"mt-4",children:[e.jsx(W,{className:"h-4 w-4"}),e.jsx(U,{className:"text-sm",children:'For urgent account issues or security concerns, please use the "Account Issues" category in the feedback form.'})]})]})]}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(C,{className:"h-6 w-6 text-burgundy-500 mr-3"}),e.jsx("h2",{className:"text-xl font-semibold text-navy-800",children:"Send Us Your Feedback"})]}),e.jsx(n,{...y,children:e.jsxs("form",{onSubmit:y.handleSubmit((async e=>{try{v(!0);const s=w();if(!s.allowed){const e=k(s.remainingTime||0);return void p({title:"Rate Limit Exceeded",description:`Please wait ${e} before submitting another feedback.`,variant:"destructive"})}await S({name:e.name,email:e.email,subject:e.subject,category:e.category,message:e.message,rating:b>0?b:void 0}),p({title:"Feedback Sent Successfully",description:"Thank you for your feedback! We will review it and get back to you within 24-48 hours.",variant:"default"}),y.reset({name:(null==r?void 0:r.displayName)||"",email:(null==r?void 0:r.email)||"",subject:"",category:void 0,message:"",rating:void 0}),f(0)}catch(s){p({title:"Error",description:s.message||"There was an error sending your feedback. Please try again later.",variant:"destructive"})}finally{v(!1)}})),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(o,{control:y.control,name:"name",render:({field:s})=>e.jsxs(c,{children:[e.jsx(d,{children:"Name *"}),e.jsx(m,{children:e.jsx(u,{placeholder:"Your full name",...s,disabled:j})}),e.jsx(x,{})]})}),e.jsx(o,{control:y.control,name:"email",render:({field:s})=>e.jsxs(c,{children:[e.jsx(d,{children:"Email *"}),e.jsx(m,{children:e.jsx(u,{type:"email",placeholder:"<EMAIL>",...s,disabled:j})}),e.jsx(x,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(o,{control:y.control,name:"subject",render:({field:s})=>e.jsxs(c,{children:[e.jsx(d,{children:"Subject *"}),e.jsx(m,{children:e.jsx(u,{placeholder:"Brief description of your feedback",...s,disabled:j})}),e.jsx(x,{})]})}),e.jsx(o,{control:y.control,name:"category",render:({field:s})=>e.jsxs(c,{children:[e.jsx(d,{children:"Category *"}),e.jsxs(F,{onValueChange:s.onChange,defaultValue:s.value,disabled:j,children:[e.jsx(m,{children:e.jsx(A,{children:e.jsx(q,{placeholder:"Select a category"})})}),e.jsxs(R,{children:[e.jsx(B,{value:"Bug Report",children:"Bug Report"}),e.jsx(B,{value:"Feature Request",children:"Feature Request"}),e.jsx(B,{value:"General Feedback",children:"General Feedback"}),e.jsx(B,{value:"Technical Support",children:"Technical Support"}),e.jsx(B,{value:"Account Issues",children:"Account Issues"})]})]}),e.jsx(x,{})]})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Overall Experience (Optional)"}),e.jsx(N,{value:b,onChange:f,disabled:j,showText:!0}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Rate your overall experience with PeerBooks"})]}),e.jsx(o,{control:y.control,name:"message",render:({field:s})=>{var a;return e.jsxs(c,{children:[e.jsx(d,{children:"Message *"}),e.jsx(m,{children:e.jsx(I,{placeholder:"Please provide detailed information about your feedback, including steps to reproduce any issues...",className:"min-h-[120px] resize-y",...s,disabled:j})}),e.jsx(x,{}),e.jsxs("p",{className:"text-xs text-gray-500",children:[(null==(a=s.value)?void 0:a.length)||0,"/2000 characters"]})]})}}),e.jsxs(V,{children:[e.jsx(E,{className:"h-4 w-4"}),e.jsxs(U,{className:"text-sm",children:[e.jsx("strong",{children:"Privacy Notice:"})," Your feedback will be used solely to improve PeerBooks. We will not share your information with third parties and will respond to your email address provided above."]})]}),e.jsx(h,{type:"submit",className:"w-full",disabled:j,children:j?e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending Feedback..."]}):e.jsxs(e.Fragment,{children:[e.jsx(G,{className:"mr-2 h-4 w-4"}),"Send Feedback"]})})]})})]})})]})]})}),e.jsx(g,{})]})};export{O as default};
