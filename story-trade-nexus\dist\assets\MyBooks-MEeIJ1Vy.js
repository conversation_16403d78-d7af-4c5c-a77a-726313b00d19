const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/EditBookModal-yEy8_Ykz.js","assets/index-BsSeXuDn.js","assets/index-D3A0I7_n.css","assets/textarea-Dh_gAZcv.js","assets/select-B_aT6wFH.js","assets/index-R-QTsfUa.js","assets/chevron-up-MGvAYXM8.js","assets/save-B8gm36Hf.js"])))=>i.map(i=>d[i]);
import{R as U,_ as z,u as D,r as l,B as i,j as e,M as J,L as w,a as h,S as Y,I as q,F as X,b as G,c as S,P as H,g as K,J as b,C as B,d as Q,e as W}from"./index-BsSeXuDn.js";import{S as A,a as C,b as R,c as k,d as r}from"./select-B_aT6wFH.js";import{P as E}from"./plus-BIQlMPjj.js";import{S as Z}from"./square-pen-GUCAQYl7.js";import"./index-R-QTsfUa.js";import"./chevron-up-MGvAYXM8.js";const $=U.lazy(()=>z(()=>import("./EditBookModal-yEy8_Ykz.js"),__vite__mapDeps([0,1,2,3,4,5,6,7])).then(t=>({default:t.EditBookModal}))),ie=()=>{var y;const{currentUser:t,userData:x}=D(),[d,p]=l.useState([]),[u,g]=l.useState([]),[P,j]=l.useState(!0),[f,v]=l.useState(null),[L,N]=l.useState(!1),[n,F]=l.useState(""),[o,M]=l.useState("all"),[c,O]=l.useState("all");l.useEffect(()=>{(async()=>{if(t)try{j(!0);const a=await K(t.uid);p(a),g(a)}catch(a){console.error("Error fetching user books:",a),b.error("Failed to load your books")}finally{j(!1)}})()},[t]),l.useEffect(()=>{let s=d;n&&(s=s.filter(a=>a.title.toLowerCase().includes(n.toLowerCase())||a.author.toLowerCase().includes(n.toLowerCase())||a.genre.some(m=>m.toLowerCase().includes(n.toLowerCase())))),o!=="all"&&(s=s.filter(a=>a.status===o)),c!=="all"&&(s=s.filter(a=>c==="pending"?a.approvalStatus===i.Pending:c==="approved"?a.approvalStatus===i.Approved||!a.approvalStatus:c==="rejected"?a.approvalStatus===i.Rejected:!0)),g(s)},[d,n,o,c]);const _=s=>{v(s),N(!0)},I=()=>{v(null),N(!1)},T=s=>{p(a=>a.map(m=>m.id===s.id?s:m)),b.success("Book updated successfully")},V=s=>{switch(s){case i.Pending:return e.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[e.jsx(W,{className:"h-3 w-3 mr-1"}),"Pending"]});case i.Approved:return e.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[e.jsx(B,{className:"h-3 w-3 mr-1"}),"Approved"]});case i.Rejected:return e.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[e.jsx(Q,{className:"h-3 w-3 mr-1"}),"Rejected"]});default:return e.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[e.jsx(B,{className:"h-3 w-3 mr-1"}),"Approved"]})}};return x!=null&&x.displayName||t!=null&&t.displayName||(y=t==null?void 0:t.email)!=null&&y.split("@")[0],e.jsx(J,{children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"My Books"}),e.jsx("p",{className:"text-gray-600",children:"Manage your book collection"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(w,{to:"/add-books",children:e.jsxs(h,{className:"flex items-center gap-2",children:[e.jsx(E,{className:"h-4 w-4"}),"Add New Book"]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"relative",children:[e.jsx(Y,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),e.jsx(q,{placeholder:"Search books...",value:n,onChange:s=>F(s.target.value),className:"pl-10"})]}),e.jsxs(A,{value:o,onValueChange:M,children:[e.jsx(C,{children:e.jsx(R,{placeholder:"Filter by status"})}),e.jsxs(k,{children:[e.jsx(r,{value:"all",children:"All Status"}),e.jsx(r,{value:"Available",children:"Available"}),e.jsx(r,{value:"Sold Out",children:"Sold Out"}),e.jsx(r,{value:"Rented Out",children:"Rented Out"})]})]}),e.jsxs(A,{value:c,onValueChange:O,children:[e.jsx(C,{children:e.jsx(R,{placeholder:"Filter by approval"})}),e.jsxs(k,{children:[e.jsx(r,{value:"all",children:"All Approvals"}),e.jsx(r,{value:"approved",children:"Approved"}),e.jsx(r,{value:"pending",children:"Pending"}),e.jsx(r,{value:"rejected",children:"Rejected"})]})]}),e.jsxs("div",{className:"text-sm text-gray-600 flex items-center",children:[e.jsx(X,{className:"h-4 w-4 mr-2"}),u.length," of ",d.length," books"]})]})]}),e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:P?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[1,2,3,4,5,6,7,8].map(s=>e.jsx("div",{className:"bg-gray-200 rounded-lg h-80 animate-pulse"},s))}):u.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:u.map(s=>e.jsxs("div",{className:"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow border",children:[e.jsxs("div",{className:"relative h-48 overflow-hidden",children:[e.jsx("img",{src:s.imageUrl,alt:s.title,className:"w-full h-full object-cover"}),e.jsxs("div",{className:"absolute top-2 right-2 flex flex-col gap-1",children:[e.jsx(G,{status:s.status,nextAvailableDate:s.nextAvailableDate}),V(s.approvalStatus)]}),e.jsx("div",{className:"absolute top-2 left-2",children:e.jsx(h,{size:"sm",variant:"outline",onClick:()=>_(s),className:"bg-white/90 hover:bg-white",children:e.jsx(Z,{className:"h-3 w-3"})})})]}),e.jsxs("div",{className:"p-4",children:[e.jsx("h3",{className:"font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1",children:s.title}),e.jsxs("p",{className:"text-gray-600 text-sm mb-2",children:["by ",s.author]}),e.jsxs("div",{className:"flex items-center justify-between text-sm mb-2",children:[e.jsxs("span",{className:"text-gray-700 flex items-center",children:[e.jsx(S,{className:"h-3.5 w-3.5 mr-1"}),s.condition]}),e.jsxs("span",{className:"text-gray-600",children:[s.genre.slice(0,2).join(", "),s.genre.length>2&&"..."]})]}),e.jsxs("div",{className:"text-sm text-gray-600",children:[s.availability,s.price&&e.jsxs("span",{className:"block text-burgundy-600 font-medium",children:["₹",s.price]}),s.rentalPrice&&e.jsxs("span",{className:"block text-burgundy-600 font-medium",children:["₹",s.rentalPrice," ",s.rentalPeriod]})]}),s.approvalStatus===i.Rejected&&s.rejectionReason&&e.jsxs("div",{className:"mt-2 p-2 bg-red-50 rounded text-xs text-red-700",children:[e.jsx("strong",{children:"Rejection reason:"})," ",s.rejectionReason]})]})]},s.id))}):e.jsxs("div",{className:"text-center py-12",children:[e.jsx(S,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:d.length===0?"No books found":"No books match your filters"}),e.jsx("p",{className:"text-gray-600 mb-4",children:d.length===0?"You haven't added any books yet. Start building your collection!":"Try adjusting your search or filter criteria."}),d.length===0&&e.jsx(w,{to:"/add-books",children:e.jsxs(h,{children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})}),f&&e.jsx(l.Suspense,{fallback:e.jsx(H,{type:"modal",message:"Loading edit form..."}),children:e.jsx($,{book:f,isOpen:L,onClose:I,onBookUpdated:T})})]})})};export{ie as default};
