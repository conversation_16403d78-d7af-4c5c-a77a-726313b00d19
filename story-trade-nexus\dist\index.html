<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PeerBooks - Share the Joy of Reading</title>
    <meta name="description" content="Rent, buy, or exchange used books directly with other readers in your community." />
    <meta name="author" content="PeerBooks" />

    <!-- Favicon and Web App Manifest -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#8B2635" />

    <meta property="og:title" content="PeerBooks - Share the Joy of Reading" />
    <meta property="og:description" content="Rent, buy, or exchange used books directly with other readers in your community." />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />

    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:site" content="@lovable_dev" />
    <meta name="twitter:image" content="https://lovable.dev/opengraph-image-p98pqg.png" />
    <script type="module" crossorigin src="/assets/index-DwRq5Uwb.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/chunk-b0HHmiEU.js">
    <link rel="modulepreload" crossorigin href="/assets/chunk-CpdlqjqK.js">
    <link rel="modulepreload" crossorigin href="/assets/chunk-Cw96wKwP.js">
    <link rel="modulepreload" crossorigin href="/assets/chunk-BGoCADfv.js">
    <link rel="modulepreload" crossorigin href="/assets/chunk-BvIisuNF.js">
    <link rel="modulepreload" crossorigin href="/assets/chunk-D1Z7_RhR.js">
    <link rel="modulepreload" crossorigin href="/assets/chunk-DgNUPAoM.js">
    <link rel="stylesheet" crossorigin href="/assets/index-BGvwYOHI.css">
  </head>

  <body>
    <div id="root"></div>
    <!-- IMPORTANT: DO NOT REMOVE THIS SCRIPT TAG OR THIS VERY COMMENT! -->
    <script src="https://cdn.gpteng.co/gptengineer.js" type="module"></script>

  </body>
</html>
