/**
 * OwnerInformationSkeleton Component
 * 
 * Loading skeleton for the Owner Information section
 * Provides visual feedback while the lazy-loaded component is loading
 */

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';

const OwnerInformationSkeleton: React.FC = () => {
  return (
    <div className="w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm">
      {/* Header */}
      <Skeleton className="h-6 w-40 mb-4" />

      {/* Grid of information cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Owner name card */}
        <div className="flex flex-col p-3 bg-gray-50 rounded-md">
          <div className="flex items-center">
            <Skeleton className="h-5 w-5 mr-3 rounded" />
            <Skeleton className="h-5 w-24" />
          </div>
        </div>

        {/* Location card */}
        <div className="flex items-center p-3 bg-gray-50 rounded-md">
          <Skeleton className="h-5 w-5 mr-3 rounded" />
          <div className="flex-1">
            <Skeleton className="h-4 w-32 mb-1" />
            <Skeleton className="h-3 w-20" />
          </div>
          <Skeleton className="h-6 w-6 ml-2 rounded" />
        </div>

        {/* Rating card */}
        <div className="flex items-center p-3 bg-gray-50 rounded-md">
          <Skeleton className="h-5 w-5 mr-3 rounded" />
          <Skeleton className="h-5 w-20" />
        </div>

        {/* Status card */}
        <div className="p-3 bg-gray-50 rounded-md">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
        </div>

        {/* Additional info cards (conditional) */}
        <div className="flex items-center p-3 bg-gray-50 rounded-md">
          <Skeleton className="h-5 w-5 mr-3 rounded" />
          <Skeleton className="h-4 w-28" />
        </div>

        <div className="flex items-center p-3 bg-gray-50 rounded-md">
          <Skeleton className="h-5 w-5 mr-3 rounded" />
          <div className="flex-1">
            <Skeleton className="h-3 w-16 mb-1" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>

      {/* Contact button */}
      <Skeleton className="w-full h-12 mt-5 rounded-md" />
    </div>
  );
};

export default OwnerInformationSkeleton;
