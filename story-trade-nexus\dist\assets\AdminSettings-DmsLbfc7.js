import{j as e,v as s,w as i,x as a,y as n,z as t,B as r}from"./chunk-CpdlqjqK.js";import{r as o}from"./chunk-b0HHmiEU.js";import{x as l,c,j as d,X as m,I as f}from"./index-DwRq5Uwb.js";import{J as u}from"./chunk-Cw96wKwP.js";import{A as h}from"./chunk-BS5eevMn.js";import{C as x,a as g,b as j,d as p,c as b}from"./chunk-D25sxu8E.js";import"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const N=o.forwardRef((({className:a,...n},t)=>e.jsx(s,{className:l("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...n,ref:t,children:e.jsx(i,{className:l("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})));N.displayName=s.displayName;const v=r,k=o.forwardRef((({className:s,...i},n)=>e.jsx(a,{ref:n,className:l("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",s),...i})));k.displayName=a.displayName;const y=o.forwardRef((({className:s,...i},a)=>e.jsx(n,{ref:a,className:l("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...i})));y.displayName=n.displayName;const w=o.forwardRef((({className:s,...i},a)=>e.jsx(t,{ref:a,className:l("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...i})));w.displayName=t.displayName;const C=()=>{const[s,i]=o.useState(!1);o.useState("general");const[a,n]=o.useState({general:{siteName:"Book Sharing Platform",contactEmail:"<EMAIL>",enableRegistration:!0,requireEmailVerification:!0},books:{requireApproval:!0,maxBooksPerUser:50,allowMultipleImages:!0,defaultBookAvailability:"Available"},notifications:{enableEmailNotifications:!0,notifyOnNewUser:!0,notifyOnBookSubmission:!0,adminEmailRecipients:"<EMAIL>"}}),t=(e,s,i)=>{n((a=>({...a,[e]:{...a[e],[s]:i}})))};return e.jsxs(h,{title:"Admin Settings",description:"Configure admin preferences and system settings",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Settings"}),e.jsx("p",{className:"text-gray-600",children:"Configure system settings and preferences"})]}),e.jsx(c,{onClick:async()=>{try{i(!0),await new Promise((e=>setTimeout(e,1e3))),u.success("Settings saved successfully!")}catch(e){u.error("Failed to save settings. Please try again.")}finally{i(!1)}},disabled:s,className:"mt-4 md:mt-0",children:s?e.jsxs(e.Fragment,{children:[e.jsx(d,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Settings"})]}),e.jsxs(v,{defaultValue:"general",className:"w-full",children:[e.jsxs(k,{className:"mb-6",children:[e.jsx(y,{value:"general",children:"General"}),e.jsx(y,{value:"books",children:"Books"}),e.jsx(y,{value:"notifications",children:"Notifications"})]}),e.jsx(w,{value:"general",children:e.jsxs(x,{children:[e.jsxs(g,{children:[e.jsx(j,{children:"General Settings"}),e.jsx(p,{children:"Configure general platform settings"})]}),e.jsxs(b,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(m,{htmlFor:"siteName",children:"Site Name"}),e.jsx(f,{id:"siteName",value:a.general.siteName,onChange:e=>t("general","siteName",e.target.value)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(m,{htmlFor:"contactEmail",children:"Contact Email"}),e.jsx(f,{id:"contactEmail",type:"email",value:a.general.contactEmail,onChange:e=>t("general","contactEmail",e.target.value)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"enableRegistration",children:"Enable User Registration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Allow new users to register on the platform"})]}),e.jsx(N,{id:"enableRegistration",checked:a.general.enableRegistration,onCheckedChange:e=>t("general","enableRegistration",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"requireEmailVerification",children:"Require Email Verification"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Users must verify their email before accessing the platform"})]}),e.jsx(N,{id:"requireEmailVerification",checked:a.general.requireEmailVerification,onCheckedChange:e=>t("general","requireEmailVerification",e)})]})]})]})}),e.jsx(w,{value:"books",children:e.jsxs(x,{children:[e.jsxs(g,{children:[e.jsx(j,{children:"Book Settings"}),e.jsx(p,{children:"Configure book-related settings"})]}),e.jsxs(b,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"requireApproval",children:"Require Book Approval"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"New book submissions require admin approval"})]}),e.jsx(N,{id:"requireApproval",checked:a.books.requireApproval,onCheckedChange:e=>t("books","requireApproval",e)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(m,{htmlFor:"maxBooksPerUser",children:"Maximum Books Per User"}),e.jsx(f,{id:"maxBooksPerUser",type:"number",value:a.books.maxBooksPerUser.toString(),onChange:e=>t("books","maxBooksPerUser",parseInt(e.target.value))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"allowMultipleImages",children:"Allow Multiple Images"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Users can upload multiple images per book"})]}),e.jsx(N,{id:"allowMultipleImages",checked:a.books.allowMultipleImages,onCheckedChange:e=>t("books","allowMultipleImages",e)})]})]})]})}),e.jsx(w,{value:"notifications",children:e.jsxs(x,{children:[e.jsxs(g,{children:[e.jsx(j,{children:"Notification Settings"}),e.jsx(p,{children:"Configure email and system notifications"})]}),e.jsxs(b,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"enableEmailNotifications",children:"Enable Email Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send email notifications for important events"})]}),e.jsx(N,{id:"enableEmailNotifications",checked:a.notifications.enableEmailNotifications,onCheckedChange:e=>t("notifications","enableEmailNotifications",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"notifyOnNewUser",children:"Notify on New User Registration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new user registers"})]}),e.jsx(N,{id:"notifyOnNewUser",checked:a.notifications.notifyOnNewUser,onCheckedChange:e=>t("notifications","notifyOnNewUser",e)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(m,{htmlFor:"notifyOnBookSubmission",children:"Notify on Book Submission"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new book is submitted"})]}),e.jsx(N,{id:"notifyOnBookSubmission",checked:a.notifications.notifyOnBookSubmission,onCheckedChange:e=>t("notifications","notifyOnBookSubmission",e)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(m,{htmlFor:"adminEmailRecipients",children:"Admin Email Recipients"}),e.jsx(f,{id:"adminEmailRecipients",value:a.notifications.adminEmailRecipients,onChange:e=>t("notifications","adminEmailRecipients",e.target.value)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Separate multiple email addresses with commas"})]})]})]})})]})]})};export{C as default};
