import{f as k,r as h,j as e,a1 as v,aC as S,D as F,E as g,aD as T,aE as A,u as C,o as R,y as I,H as q,e as B,n as E,m as P,p as Y,q as n,s as c,v as d,w as m,I as p,x as u,C as H,h as M,k as W,G as D,t as j}from"./index-BiRTTmW2.js";import{S as L,c as V,f as G,s as z}from"./StarRating-CcAiefPG.js";import{S as O,a as _,b as Q,c as $,d as x}from"./select-0Vme_KjC.js";import{T as J}from"./textarea-BZWEd9Tf.js";import{C as K}from"./circle-alert-Pl6AF7JV.js";import{S as X}from"./send-Brggmz73.js";import"./index-CKOwRU9G.js";import"./chevron-up-NKtlWnTi.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=k("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),U=S("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),b=h.forwardRef(({className:a,variant:t,...i},o)=>e.jsx("div",{ref:o,role:"alert",className:v(U({variant:t}),a),...i}));b.displayName="Alert";const ee=h.forwardRef(({className:a,...t},i)=>e.jsx("h5",{ref:i,className:v("mb-1 font-medium leading-none tracking-tight",a),...t}));ee.displayName="AlertTitle";const y=h.forwardRef(({className:a,...t},i)=>e.jsx("div",{ref:i,className:v("text-sm [&_p]:leading-relaxed",a),...t}));y.displayName="AlertDescription";const se=F({name:g().min(2,{message:"Name must be at least 2 characters"}).max(100),email:g().email({message:"Please enter a valid email address"}),subject:g().min(5,{message:"Subject must be at least 5 characters"}).max(200),category:T(["Bug Report","Feature Request","General Feedback","Technical Support","Account Issues"],{required_error:"Please select a category"}),message:g().min(10,{message:"Message must be at least 10 characters"}).max(2e3,{message:"Message must be less than 2000 characters"}),rating:A().min(1).max(5).optional()}),ae=[{question:"How do I add books to the platform?",answer:"After creating an account and verifying your email, go to 'Add Your Books' in the navigation menu. Fill out the book details form with title, author, condition, and upload photos."},{question:"How does the book exchange process work?",answer:"Browse available books, contact the owner through WhatsApp or email, arrange the exchange details, and meet safely to exchange books. Always verify the book condition before finalizing."},{question:"Is my personal information safe?",answer:"Yes, we take privacy seriously. Your email is never displayed publicly, and we only share your contact information when you initiate contact with a book owner."},{question:"How do I report inappropriate content or users?",answer:"Use the 'Bug Report' or 'General Feedback' category in this form to report any issues. We review all reports promptly and take appropriate action."},{question:"Can I edit or delete my book listings?",answer:"Yes, go to your Dashboard to manage your book listings. You can edit details, update availability, or remove books from the platform."}],me=()=>{const{currentUser:a}=C(),[t,i]=h.useState(!1),[o,f]=h.useState(0),l=R({resolver:I(se),defaultValues:{name:(a==null?void 0:a.displayName)||"",email:(a==null?void 0:a.email)||"",subject:"",category:void 0,message:"",rating:void 0}}),N=async s=>{try{i(!0);const r=V();if(!r.allowed){const w=G(r.remainingTime||0);j({title:"Rate Limit Exceeded",description:`Please wait ${w} before submitting another feedback.`,variant:"destructive"});return}await z({name:s.name,email:s.email,subject:s.subject,category:s.category,message:s.message,rating:o>0?o:void 0}),j({title:"Feedback Sent Successfully",description:"Thank you for your feedback! We will review it and get back to you within 24-48 hours.",variant:"default"}),l.reset({name:(a==null?void 0:a.displayName)||"",email:(a==null?void 0:a.email)||"",subject:"",category:void 0,message:"",rating:void 0}),f(0)}catch(r){console.error("Error submitting feedback:",r),j({title:"Error",description:r.message||"There was an error sending your feedback. Please try again later.",variant:"destructive"})}finally{i(!1)}};return e.jsxs("div",{className:"flex flex-col min-h-screen",children:[e.jsx(q,{}),e.jsx("main",{className:"flex-1 bg-gray-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-12",children:[e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Feedback & Support"}),e.jsx("p",{className:"text-gray-600 max-w-3xl mx-auto",children:"We value your feedback and are here to help! Whether you've found a bug, have a feature request, or need technical support, we'd love to hear from you. Your input helps us make PeerBooks better for everyone."})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[e.jsxs("div",{className:"lg:col-span-1",children:[e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(Z,{className:"h-6 w-6 text-burgundy-500 mr-3"}),e.jsx("h2",{className:"text-xl font-semibold text-navy-800",children:"Frequently Asked Questions"})]}),e.jsx("div",{className:"space-y-4",children:ae.map((s,r)=>e.jsxs("details",{className:"group",children:[e.jsx("summary",{className:"cursor-pointer text-sm font-medium text-gray-700 hover:text-burgundy-600 transition-colors",children:s.question}),e.jsx("p",{className:"mt-2 text-sm text-gray-600 leading-relaxed",children:s.answer})]},r))})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-navy-800 mb-4",children:"Need Immediate Help?"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(B,{className:"h-5 w-5 text-burgundy-500 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Response Time"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Within 24-48 hours"})]})]}),e.jsxs("div",{className:"flex items-center",children:[e.jsx(E,{className:"h-5 w-5 text-burgundy-500 mr-3"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-gray-700",children:"Email Support"}),e.jsx("p",{className:"text-sm text-gray-600",children:"Available 24/7"})]})]})]}),e.jsxs(b,{className:"mt-4",children:[e.jsx(K,{className:"h-4 w-4"}),e.jsx(y,{className:"text-sm",children:'For urgent account issues or security concerns, please use the "Account Issues" category in the feedback form.'})]})]})]}),e.jsx("div",{className:"lg:col-span-2",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[e.jsxs("div",{className:"flex items-center mb-6",children:[e.jsx(P,{className:"h-6 w-6 text-burgundy-500 mr-3"}),e.jsx("h2",{className:"text-xl font-semibold text-navy-800",children:"Send Us Your Feedback"})]}),e.jsx(Y,{...l,children:e.jsxs("form",{onSubmit:l.handleSubmit(N),className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(n,{control:l.control,name:"name",render:({field:s})=>e.jsxs(c,{children:[e.jsx(d,{children:"Name *"}),e.jsx(m,{children:e.jsx(p,{placeholder:"Your full name",...s,disabled:t})}),e.jsx(u,{})]})}),e.jsx(n,{control:l.control,name:"email",render:({field:s})=>e.jsxs(c,{children:[e.jsx(d,{children:"Email *"}),e.jsx(m,{children:e.jsx(p,{type:"email",placeholder:"<EMAIL>",...s,disabled:t})}),e.jsx(u,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(n,{control:l.control,name:"subject",render:({field:s})=>e.jsxs(c,{children:[e.jsx(d,{children:"Subject *"}),e.jsx(m,{children:e.jsx(p,{placeholder:"Brief description of your feedback",...s,disabled:t})}),e.jsx(u,{})]})}),e.jsx(n,{control:l.control,name:"category",render:({field:s})=>e.jsxs(c,{children:[e.jsx(d,{children:"Category *"}),e.jsxs(O,{onValueChange:s.onChange,defaultValue:s.value,disabled:t,children:[e.jsx(m,{children:e.jsx(_,{children:e.jsx(Q,{placeholder:"Select a category"})})}),e.jsxs($,{children:[e.jsx(x,{value:"Bug Report",children:"Bug Report"}),e.jsx(x,{value:"Feature Request",children:"Feature Request"}),e.jsx(x,{value:"General Feedback",children:"General Feedback"}),e.jsx(x,{value:"Technical Support",children:"Technical Support"}),e.jsx(x,{value:"Account Issues",children:"Account Issues"})]})]}),e.jsx(u,{})]})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-700 mb-2 block",children:"Overall Experience (Optional)"}),e.jsx(L,{value:o,onChange:f,disabled:t,showText:!0}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Rate your overall experience with PeerBooks"})]}),e.jsx(n,{control:l.control,name:"message",render:({field:s})=>{var r;return e.jsxs(c,{children:[e.jsx(d,{children:"Message *"}),e.jsx(m,{children:e.jsx(J,{placeholder:"Please provide detailed information about your feedback, including steps to reproduce any issues...",className:"min-h-[120px] resize-y",...s,disabled:t})}),e.jsx(u,{}),e.jsxs("p",{className:"text-xs text-gray-500",children:[((r=s.value)==null?void 0:r.length)||0,"/2000 characters"]})]})}}),e.jsxs(b,{children:[e.jsx(H,{className:"h-4 w-4"}),e.jsxs(y,{className:"text-sm",children:[e.jsx("strong",{children:"Privacy Notice:"})," Your feedback will be used solely to improve PeerBooks. We will not share your information with third parties and will respond to your email address provided above."]})]}),e.jsx(M,{type:"submit",className:"w-full",disabled:t,children:t?e.jsxs(e.Fragment,{children:[e.jsx(W,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending Feedback..."]}):e.jsxs(e.Fragment,{children:[e.jsx(X,{className:"mr-2 h-4 w-4"}),"Send Feedback"]})})]})})]})})]})]})}),e.jsx(D,{})]})};export{me as default};
