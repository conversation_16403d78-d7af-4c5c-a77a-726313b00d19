
import React from 'react';
import { ArrowR<PERSON>, BookPlus } from 'lucide-react';
import { Link } from 'react-router-dom';
import BookCard from './BookCard';
import { Book } from '../types/index';
import { Button } from './ui/button';
import { useHomepageLCPOptimization } from '@/hooks/useLCPOptimization';

interface FeaturedBooksProps {
  books: Book[];
}

const FeaturedBooks: React.FC<FeaturedBooksProps> = ({ books }) => {
  // Preload critical images for LCP optimization
  useHomepageLCPOptimization(books);
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h2 className="text-3xl font-playfair font-bold text-navy-800 mb-2">Featured Books</h2>
            <p className="text-gray-600">Explore our community's most interesting finds</p>
          </div>

          <Link to="/browse">
            <Button variant="link" className="mt-2 md:mt-0">
              View All Books
              <ArrowRight className="ml-1 h-4 w-4" />
            </Button>
          </Link>
        </div>

        {books.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {books.map((book, index) => (
              <BookCard
                key={book.id}
                book={book}
                index={index}
                priority={index < 4} // First 4 books are above the fold on most screens
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-16 bg-beige-50 rounded-lg">
            <BookPlus className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-700 mb-2">No Featured Books Available Yet</h3>
            <p className="text-gray-600 mb-6">Be the first to add books to our community!</p>
            <Link to="/add-books">
              <Button>Add Your Books</Button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
};

export default FeaturedBooks;


