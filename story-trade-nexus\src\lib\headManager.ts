/**
 * Head Manager for Dynamic Preload Optimization
 * 
 * This utility manages dynamic injection of preload links for LCP optimization
 * and provides utilities for Core Web Vitals improvement.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

// Configuration for preload optimization
export const PRELOAD_CONFIG = {
  // Maximum number of images to preload to avoid bandwidth waste
  maxPreloads: 8,
  
  // Priority levels for different image types
  priorities: {
    hero: 'high' as const,
    featured: 'high' as const,
    aboveFold: 'high' as const,
    belowFold: 'low' as const,
  },
  
  // Image size thresholds for responsive preloading
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200,
  },
  
  // Firebase Storage domain for crossorigin attribute
  firebaseStorageDomain: 'firebasestorage.googleapis.com',
} as const;

// Interface for preload link configuration
export interface PreloadLinkConfig {
  href: string;
  fetchpriority?: 'high' | 'low' | 'auto';
  crossorigin?: 'anonymous' | 'use-credentials';
  as?: 'image' | 'font' | 'script' | 'style';
  type?: string;
  media?: string;
  sizes?: string;
}

// Interface for image preload configuration
export interface ImagePreloadConfig {
  url: string;
  priority?: 'high' | 'low' | 'auto';
  sizes?: string;
  media?: string;
  isAboveFold?: boolean;
}

/**
 * Checks if a URL is from Firebase Storage and needs crossorigin attribute
 */
export const needsCrossorigin = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname.includes(PRELOAD_CONFIG.firebaseStorageDomain);
  } catch {
    return false;
  }
};

/**
 * Converts image URL to WebP format if not already WebP
 */
export const getWebPUrl = (url: string): string => {
  if (!url || url.includes('.webp')) {
    return url;
  }
  
  // For Firebase Storage URLs, replace the extension with .webp
  if (needsCrossorigin(url)) {
    return url.replace(/\.(jpg|jpeg|png)(\?|$)/i, '.webp$2');
  }
  
  return url;
};

/**
 * Generates responsive image sizes attribute for preload
 */
export const generateSizesAttribute = (isAboveFold: boolean = true): string => {
  if (isAboveFold) {
    // Above-the-fold images are typically larger
    return '(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw';
  } else {
    // Below-the-fold images are typically smaller
    return '(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw';
  }
};

/**
 * Creates a preload link element and adds it to the document head
 */
export const createPreloadLink = (config: PreloadLinkConfig): HTMLLinkElement => {
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = config.href;
  link.as = config.as || 'image';
  
  if (config.fetchpriority) {
    link.setAttribute('fetchpriority', config.fetchpriority);
  }
  
  if (config.crossorigin) {
    link.crossOrigin = config.crossorigin;
  }
  
  if (config.type) {
    link.type = config.type;
  }
  
  if (config.media) {
    link.media = config.media;
  }
  
  if (config.sizes) {
    link.setAttribute('sizes', config.sizes);
  }
  
  // Add data attribute for tracking
  link.setAttribute('data-preload-type', 'lcp-optimization');
  
  return link;
};

/**
 * Adds a preload link to the document head
 */
export const addPreloadLink = (config: PreloadLinkConfig): void => {
  const link = createPreloadLink(config);
  
  // Check if a similar preload already exists to avoid duplicates
  const existingLink = document.querySelector(`link[rel="preload"][href="${config.href}"]`);
  if (existingLink) {
    return;
  }
  
  // Insert at the beginning of head for higher priority
  const head = document.head;
  const firstChild = head.firstChild;
  head.insertBefore(link, firstChild);
};

/**
 * Preloads multiple images with priority and deduplication
 */
export const preloadImages = (images: ImagePreloadConfig[]): void => {
  // Limit the number of preloads to avoid bandwidth waste
  const imagesToPreload = images.slice(0, PRELOAD_CONFIG.maxPreloads);
  
  imagesToPreload.forEach((image, index) => {
    const webpUrl = getWebPUrl(image.url);
    const priority = image.priority || (image.isAboveFold ? 'high' : 'low');
    const sizes = image.sizes || generateSizesAttribute(image.isAboveFold);
    
    const preloadConfig: PreloadLinkConfig = {
      href: webpUrl,
      fetchpriority: priority,
      crossorigin: needsCrossorigin(webpUrl) ? 'anonymous' : undefined,
      as: 'image',
      sizes: sizes,
      media: image.media,
    };
    
    // Add a small delay for non-critical images to avoid blocking critical resources
    if (priority === 'low' && index > 3) {
      setTimeout(() => addPreloadLink(preloadConfig), 100 * (index - 3));
    } else {
      addPreloadLink(preloadConfig);
    }
  });
};

/**
 * Removes all preload links added by this utility
 */
export const clearPreloadLinks = (): void => {
  const preloadLinks = document.querySelectorAll('link[data-preload-type="lcp-optimization"]');
  preloadLinks.forEach(link => link.remove());
};

/**
 * Preloads featured books images for homepage
 */
export const preloadFeaturedBooksImages = (books: Array<{ imageUrl: string; id: string }>): void => {
  const images: ImagePreloadConfig[] = books.slice(0, 6).map((book, index) => ({
    url: book.imageUrl,
    priority: index < 3 ? 'high' : 'low',
    isAboveFold: index < 4, // First 4 books are typically above the fold
    sizes: generateSizesAttribute(index < 4),
  }));
  
  preloadImages(images);
};

/**
 * Preloads browse books page images
 */
export const preloadBrowseBooksImages = (books: Array<{ imageUrl: string; id: string }>): void => {
  const images: ImagePreloadConfig[] = books.slice(0, 8).map((book, index) => ({
    url: book.imageUrl,
    priority: index < 4 ? 'high' : 'low',
    isAboveFold: index < 6, // First 6 books are typically above the fold in grid layout
    sizes: generateSizesAttribute(index < 6),
  }));
  
  preloadImages(images);
};

/**
 * Detects if an image is likely to be the LCP element
 */
export const isLikelyLCPImage = (element: HTMLImageElement): boolean => {
  const rect = element.getBoundingClientRect();
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;
  
  // Check if image is in the viewport
  const isInViewport = rect.top < viewportHeight && rect.bottom > 0 && 
                      rect.left < viewportWidth && rect.right > 0;
  
  // Check if image is large enough to be LCP candidate
  const isLargeEnough = rect.width * rect.height > (viewportWidth * viewportHeight * 0.1);
  
  // Check if image is above the fold
  const isAboveFold = rect.top < viewportHeight;
  
  return isInViewport && isLargeEnough && isAboveFold;
};

/**
 * Monitors LCP and provides feedback on preload effectiveness
 */
export const monitorLCPPerformance = (): void => {
  if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
    return;
  }
  
  try {
    const observer = new PerformanceObserver((list) => {
      const entries = list.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      if (lastEntry) {
        console.log(`[LCP Optimization] LCP: ${lastEntry.startTime.toFixed(2)}ms`, {
          element: lastEntry.element,
          url: lastEntry.url,
          size: lastEntry.size,
        });
        
        // Check if LCP element is an image that was preloaded
        if (lastEntry.element && lastEntry.element.tagName === 'IMG') {
          const imgSrc = (lastEntry.element as HTMLImageElement).src;
          const wasPreloaded = document.querySelector(`link[rel="preload"][href="${imgSrc}"]`);
          
          console.log(`[LCP Optimization] Image was ${wasPreloaded ? 'preloaded' : 'not preloaded'}`);
        }
      }
    });
    
    observer.observe({ entryTypes: ['largest-contentful-paint'] });
  } catch (error) {
    console.warn('[LCP Optimization] Failed to monitor LCP:', error);
  }
};

/**
 * Initializes LCP optimization for the current page
 */
export const initializeLCPOptimization = (): void => {
  // Start monitoring LCP performance
  monitorLCPPerformance();
  
  // Clean up any existing preload links on page navigation
  clearPreloadLinks();
};
