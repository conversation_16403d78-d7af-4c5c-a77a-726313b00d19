import{f as m,j as e,a9 as x,ae as r,h as a,am as o,b as u}from"./index-YryEuZxD.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=m("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]),g=({book:s,distance:l,userLocation:d,ownerPincode:t,locationPermission:n,onContactOwner:c,onRequestLocation:i})=>e.jsxs("div",{className:"w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4 text-lg",children:"Owner Information"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx("div",{className:"flex flex-col p-3 bg-gray-50 rounded-md",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(x,{className:"h-5 w-5 mr-3 text-navy-400"}),e.jsx("span",{className:"font-medium",children:s.ownerName})]})}),s.ownerCoordinates&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(r,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("a",{href:`https://www.google.com/maps?q=${s.ownerCoordinates.latitude},${s.ownerCoordinates.longitude}`,target:"_blank",rel:"noopener noreferrer",className:"text-burgundy-600 hover:underline font-medium block",children:l!==null?`${l.toFixed(1)} km away from you`:s.distance?`${typeof s.distance=="number"?s.distance.toFixed(1):s.distance} km away from you`:"View on map"}),s.ownerCommunity&&e.jsx("div",{className:"flex items-center mt-1",children:e.jsx("span",{className:"text-sm text-blue-600 font-medium",children:s.ownerCommunity})})]}),d&&e.jsx(a,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:i,title:"Refresh distance calculation",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:e.jsx("path",{d:"M21.5 2v6h-6M2.5 22v-6h6M2 11.5a10 10 0 0 1 18.8-4.3M22 12.5a10 10 0 0 1-18.8 4.2"})})})]}),!s.ownerCoordinates&&(t||s.ownerPincode||s.ownerPincode)&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(r,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("span",{className:"font-medium",children:["Location: Pincode ",t||s.ownerPincode||s.ownerPincode]}),s.ownerCommunity&&e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx("div",{className:"h-1.5 w-1.5 bg-blue-500 rounded-full mr-2"}),e.jsx("span",{className:"text-sm text-blue-600 font-medium",children:s.ownerCommunity})]})]}),!n||n==="unknown"?e.jsx(a,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:i,title:"Get your location",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("circle",{cx:"12",cy:"12",r:"10"}),e.jsx("circle",{cx:"12",cy:"12",r:"1"})]})}):null]}),(t||s.ownerPincode||s.ownerPincode)&&s.ownerCoordinates&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-3 text-navy-400",children:[e.jsx("rect",{x:"3",y:"8",width:"18",height:"12",rx:"2"}),e.jsx("path",{d:"M7 12h10"}),e.jsx("path",{d:"M7 16h10"}),e.jsx("path",{d:"M11 8V4H8"})]}),e.jsxs("span",{className:"font-medium",children:["Pincode: ",t||s.ownerPincode||s.ownerPincode]})]}),e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(o,{className:"h-5 w-5 mr-3 text-yellow-500"}),e.jsxs("span",{className:"font-medium",children:[s.ownerRating,"/5 Rating"]})]}),e.jsxs("div",{className:"p-3 bg-gray-50 rounded-md",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Book Status"}),e.jsx(u,{status:s.status,nextAvailableDate:s.nextAvailableDate,className:"text-xs"})]}),s.status==="Rented Out"&&s.nextAvailableDate&&e.jsxs("div",{className:"mt-2 text-xs text-gray-500",children:["Expected return: ",s.nextAvailableDate.toLocaleDateString("en-IN",{weekday:"short",year:"numeric",month:"short",day:"numeric"})]})]}),!s.ownerCoordinates&&!(t||s.ownerPincode||s.ownerPincode)&&(s.ownerCommunity?e.jsxs("div",{className:"flex items-center p-3 bg-blue-50 rounded-md",children:[e.jsx("div",{className:"h-5 w-5 mr-3 flex items-center justify-center",children:e.jsx("div",{className:"h-3 w-3 bg-blue-500 rounded-full"})}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-blue-600",children:"Community"}),e.jsx("div",{className:"font-medium text-blue-700",children:s.ownerCommunity})]})]}):s.ownerLocation&&s.ownerLocation!=="Unknown Location"?e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(r,{className:"h-5 w-5 mr-3 text-gray-400"}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-600",children:"Location"}),e.jsx("div",{className:"font-medium text-gray-700",children:s.ownerLocation})]})]}):null)]}),!s.ownerCoordinates&&!t&&!s.ownerPincode&&!s.ownerPincode&&e.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[e.jsx(r,{className:"h-5 w-5 mr-3 text-burgundy-400"}),e.jsx("span",{className:"font-medium text-gray-600",children:"Location information unavailable"}),!n||n==="unknown"?e.jsx(a,{variant:"ghost",size:"icon",className:"h-6 w-6 ml-2 text-gray-500 hover:text-burgundy-600",onClick:i,title:"Get your location",children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[e.jsx("circle",{cx:"12",cy:"12",r:"10"}),e.jsx("circle",{cx:"12",cy:"12",r:"1"})]})}):null]}),e.jsxs(a,{onClick:c,className:"w-full mt-5",size:"lg",disabled:s.status==="Sold Out",variant:s.status==="Sold Out"?"outline":"default",children:[e.jsx(h,{className:"h-5 w-5 mr-2"}),s.status==="Sold Out"?"Book Sold Out":"Contact Owner"]})]});export{g as default};
