const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/EditBookModal-CPdJna-5.js","assets/chunk-CpdlqjqK.js","assets/chunk-b0HHmiEU.js","assets/index-DwRq5Uwb.js","assets/chunk-Cw96wKwP.js","assets/chunk-BGoCADfv.js","assets/chunk-BvIisuNF.js","assets/chunk-D1Z7_RhR.js","assets/chunk-DgNUPAoM.js","assets/index-BGvwYOHI.css","assets/chunk-DQKmWaJS.js","assets/chunk-BpJWUrWd.js"])))=>i.map(i=>d[i]);
import{_ as e,u as s,B as a,M as l,a as t,I as r,b as d,P as i,g as n}from"./index-DwRq5Uwb.js";import{j as o}from"./chunk-CpdlqjqK.js";import{R as c,r as m,L as x}from"./chunk-b0HHmiEU.js";import{S as u,a as h,b as j,c as p,d as g}from"./chunk-BpJWUrWd.js";import{J as v}from"./chunk-Cw96wKwP.js";import{P as f,S as b,F as N,a as y,B as w,C as k,b as S,c as A}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const C=c.lazy((()=>e((()=>import("./EditBookModal-CPdJna-5.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11])).then((e=>({default:e.EditBookModal}))))),R=()=>{var e;const{currentUser:c,userData:R}=s(),[P,B]=m.useState([]),[L,E]=m.useState([]),[O,_]=m.useState(!0),[F,M]=m.useState(null),[D,U]=m.useState(!1),[V,z]=m.useState(""),[I,T]=m.useState("all"),[Y,J]=m.useState("all");m.useEffect((()=>{(async()=>{if(c)try{_(!0);const e=await n(c.uid);B(e),E(e)}catch(e){v.error("Failed to load your books")}finally{_(!1)}})()}),[c]),m.useEffect((()=>{let e=P;V&&(e=e.filter((e=>e.title.toLowerCase().includes(V.toLowerCase())||e.author.toLowerCase().includes(V.toLowerCase())||e.genre.some((e=>e.toLowerCase().includes(V.toLowerCase())))))),"all"!==I&&(e=e.filter((e=>e.status===I))),"all"!==Y&&(e=e.filter((e=>"pending"===Y?e.approvalStatus===a.Pending:"approved"===Y?e.approvalStatus===a.Approved||!e.approvalStatus:"rejected"!==Y||e.approvalStatus===a.Rejected))),E(e)}),[P,V,I,Y]);const q=e=>{switch(e){case a.Pending:return o.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[o.jsx(A,{className:"h-3 w-3 mr-1"}),"Pending"]});case a.Approved:return o.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[o.jsx(k,{className:"h-3 w-3 mr-1"}),"Approved"]});case a.Rejected:return o.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[o.jsx(S,{className:"h-3 w-3 mr-1"}),"Rejected"]});default:return o.jsxs("span",{className:"inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[o.jsx(k,{className:"h-3 w-3 mr-1"}),"Approved"]})}};return(null==R?void 0:R.displayName)||(null==c?void 0:c.displayName)||null==(e=null==c?void 0:c.email)||e.split("@")[0],o.jsx(l,{children:o.jsxs("div",{className:"container mx-auto px-4 py-8",children:[o.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[o.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[o.jsxs("div",{children:[o.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"My Books"}),o.jsx("p",{className:"text-gray-600",children:"Manage your book collection"})]}),o.jsx("div",{className:"mt-4 md:mt-0",children:o.jsx(x,{to:"/add-books",children:o.jsxs(t,{className:"flex items-center gap-2",children:[o.jsx(f,{className:"h-4 w-4"}),"Add New Book"]})})})]}),o.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[o.jsxs("div",{className:"relative",children:[o.jsx(b,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),o.jsx(r,{placeholder:"Search books...",value:V,onChange:e=>z(e.target.value),className:"pl-10"})]}),o.jsxs(u,{value:I,onValueChange:T,children:[o.jsx(h,{children:o.jsx(j,{placeholder:"Filter by status"})}),o.jsxs(p,{children:[o.jsx(g,{value:"all",children:"All Status"}),o.jsx(g,{value:"Available",children:"Available"}),o.jsx(g,{value:"Sold Out",children:"Sold Out"}),o.jsx(g,{value:"Rented Out",children:"Rented Out"})]})]}),o.jsxs(u,{value:Y,onValueChange:J,children:[o.jsx(h,{children:o.jsx(j,{placeholder:"Filter by approval"})}),o.jsxs(p,{children:[o.jsx(g,{value:"all",children:"All Approvals"}),o.jsx(g,{value:"approved",children:"Approved"}),o.jsx(g,{value:"pending",children:"Pending"}),o.jsx(g,{value:"rejected",children:"Rejected"})]})]}),o.jsxs("div",{className:"text-sm text-gray-600 flex items-center",children:[o.jsx(N,{className:"h-4 w-4 mr-2"}),L.length," of ",P.length," books"]})]})]}),o.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:O?o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[1,2,3,4,5,6,7,8].map((e=>o.jsx("div",{className:"bg-gray-200 rounded-lg h-80 animate-pulse"},e)))}):L.length>0?o.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:L.map((e=>o.jsxs("div",{className:"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-shadow border",children:[o.jsxs("div",{className:"relative h-48 overflow-hidden",children:[o.jsx("img",{src:e.imageUrl,alt:e.title,className:"w-full h-full object-cover"}),o.jsxs("div",{className:"absolute top-2 right-2 flex flex-col gap-1",children:[o.jsx(d,{status:e.status,nextAvailableDate:e.nextAvailableDate}),q(e.approvalStatus)]}),o.jsx("div",{className:"absolute top-2 left-2",children:o.jsx(t,{size:"sm",variant:"outline",onClick:()=>(e=>{M(e),U(!0)})(e),className:"bg-white/90 hover:bg-white",children:o.jsx(y,{className:"h-3 w-3"})})})]}),o.jsxs("div",{className:"p-4",children:[o.jsx("h3",{className:"font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1",children:e.title}),o.jsxs("p",{className:"text-gray-600 text-sm mb-2",children:["by ",e.author]}),o.jsxs("div",{className:"flex items-center justify-between text-sm mb-2",children:[o.jsxs("span",{className:"text-gray-700 flex items-center",children:[o.jsx(w,{className:"h-3.5 w-3.5 mr-1"}),e.condition]}),o.jsxs("span",{className:"text-gray-600",children:[e.genre.slice(0,2).join(", "),e.genre.length>2&&"..."]})]}),o.jsxs("div",{className:"text-sm text-gray-600",children:[e.availability,e.price&&o.jsxs("span",{className:"block text-burgundy-600 font-medium",children:["₹",e.price]}),e.rentalPrice&&o.jsxs("span",{className:"block text-burgundy-600 font-medium",children:["₹",e.rentalPrice," ",e.rentalPeriod]})]}),e.approvalStatus===a.Rejected&&e.rejectionReason&&o.jsxs("div",{className:"mt-2 p-2 bg-red-50 rounded text-xs text-red-700",children:[o.jsx("strong",{children:"Rejection reason:"})," ",e.rejectionReason]})]})]},e.id)))}):o.jsxs("div",{className:"text-center py-12",children:[o.jsx(w,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),o.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:0===P.length?"No books found":"No books match your filters"}),o.jsx("p",{className:"text-gray-600 mb-4",children:0===P.length?"You haven't added any books yet. Start building your collection!":"Try adjusting your search or filter criteria."}),0===P.length&&o.jsx(x,{to:"/add-books",children:o.jsxs(t,{children:[o.jsx(f,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})}),F&&o.jsx(m.Suspense,{fallback:o.jsx(i,{type:"modal",message:"Loading edit form..."}),children:o.jsx(C,{book:F,isOpen:D,onClose:()=>{M(null),U(!1)},onBookUpdated:e=>{B((s=>s.map((s=>s.id===e.id?e:s)))),v.success("Book updated successfully")}})})]})})};export{R as default};
