const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-ehpEbksy.js","assets/index.esm2017-H7c5Bkvh.js","assets/index-BiRTTmW2.js","assets/index-D3A0I7_n.css"])))=>i.map(i=>d[i]);
import{f as le,aU as ae,z as y,A as ce,r as U,u as de,o as ue,j as e,H as me,p as ge,q as j,s as v,v as w,w as k,I as T,x as I,U as Y,X as pe,ae as he,a as O,aV as xe,G as fe,J as N,y as be,_ as Q,K as ye}from"./index-BiRTTmW2.js";import{T as je}from"./textarea-BZWEd9Tf.js";import{ref as ve,uploadBytes as we,getDownloadURL as ke}from"./index.esm-BEM7nnuS.js";import{getCurrentPosition as Ne}from"./geolocationUtils-DGx5pdrF.js";import{C as Se}from"./circle-alert-Pl6AF7JV.js";import"./index.esm2017-H7c5Bkvh.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=le("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),A={maxWidth:1200,maxHeight:1200,webpQuality:.85,jpegQuality:.85,maxFileSize:5*1024*1024,targetFileSize:200*1024,supportedFormats:["image/jpeg","image/jpg","image/png","image/webp"],outputFormat:"webp",fallbackFormat:"jpeg"};function Ae(){return new Promise(t=>{const i=new Image;i.onload=i.onerror=()=>{t(i.height===2)},i.src="data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA"})}function J(t){return A.supportedFormats.includes(t.type)?t.size>A.maxFileSize?{valid:!1,error:`File size too large. Maximum size: ${A.maxFileSize/1024/1024}MB`}:{valid:!0}:{valid:!1,error:`Unsupported file format. Supported formats: ${A.supportedFormats.join(", ")}`}}function Ee(t,i){const s=document.createElement("canvas");return s.width=t,s.height=i,s}function Fe(t,i,s=A.maxWidth,r=A.maxHeight){let{width:d,height:c}={width:t,height:i};const p=s/d,u=r/c,l=Math.min(p,u,1);return d=Math.round(d*l),c=Math.round(c*l),{width:d,height:c}}function Pe(t){return new Promise((i,s)=>{const r=new Image;r.onload=()=>i(r),r.onerror=()=>s(new Error("Failed to load image")),r.src=URL.createObjectURL(t)})}function V(t,i="webp",s=.85){return new Promise((r,d)=>{t.toBlob(c=>{c?r(c):d(new Error("Failed to convert canvas to blob"))},`image/${i}`,s)})}async function Ce(t,i={}){const s=J(t);if(!s.valid)throw new Error(s.error);const{maxWidth:r=A.maxWidth,maxHeight:d=A.maxHeight,quality:c=A.webpQuality,format:p=await Ae()?"webp":A.fallbackFormat,targetSize:u=A.targetFileSize}=i;try{const l=await Pe(t),{width:a,height:E}=Fe(l.naturalWidth,l.naturalHeight,r,d),D=Ee(a,E),F=D.getContext("2d");if(!F)throw new Error("Failed to get canvas context");F.imageSmoothingEnabled=!0,F.imageSmoothingQuality="high",F.drawImage(l,0,0,a,E),URL.revokeObjectURL(l.src);let B=await V(D,p,c),z=c;for(;B.size>u&&z>.3;)z-=.1,B=await V(D,p,z);const H=new File([B],`${t.name.split(".")[0]}.${p}`,{type:`image/${p}`});return{file:H,originalSize:t.size,processedSize:H.size,format:p}}catch(l){throw console.error("Image processing failed:",l),new Error(`Image processing failed: ${l instanceof Error?l.message:"Unknown error"}`)}}const L="https://via.placeholder.com/150?text=No+Image",Re=async(t,i,s,r={})=>{try{if(console.log(`Uploading image to path: ${i}`),console.log(`Image file details: name=${t.name}, size=${t.size}, type=${t.type}`),!t)return console.error("No image file provided"),L;if(!i)return console.error("No path provided"),L;const d=J(t);if(!d.valid)return console.error("Image validation failed:",d.error),L;let c=t,p=i;if(!r.skipProcessing)try{console.log("Processing image for optimization..."),s&&s(10);const a=await Ce(t,{maxWidth:r.maxWidth,maxHeight:r.maxHeight,quality:r.quality});c=a.file,a.format!==t.type.split("/")[1]&&(p=i.replace(/\.[^.]+$/,`.${a.format}`)),console.log(`Image processed: ${t.size} bytes -> ${a.file.size} bytes (${a.format})`),s&&s(30)}catch(a){console.warn("Image processing failed, uploading original:",a)}const u=ve(ae,p);console.log(`Storage reference created: ${u.fullPath}`);const l={contentType:c.type,customMetadata:{originalName:t.name,originalSize:t.size.toString(),processedSize:c.size.toString(),uploadedAt:new Date().toISOString()}};console.log("Metadata:",l),console.log("Starting file upload..."),s&&s(50);try{const a=await we(u,c,l);console.log("Image uploaded successfully"),console.log("Upload snapshot:",{bytesTransferred:a.bytesTransferred,totalBytes:a.totalBytes,state:a.state}),s&&s(90),console.log("Getting download URL...");const E=await ke(a.ref);return console.log("Image download URL:",E),s&&(console.log("Calling progress callback with 100%"),s(100)),E}catch(a){throw console.error("Error in uploadBytes:",a),a instanceof Error&&console.error("Upload error details:",{message:a.message,stack:a.stack}),a}}catch(d){return console.error("Error uploading image:",d),d instanceof Error&&console.error("Error details:",{message:d.message,stack:d.stack}),L}},Ue=async(t,i,s)=>{try{if(console.log(`Uploading ${t.length} images`),console.log("Image files:",t.map(l=>({name:l.name,size:l.size,type:l.type}))),console.log("User ID:",i),!t||t.length===0)return console.error("No image files provided"),[L];if(!i)return console.error("No user ID provided"),[L];const r=[],d=t.length;let c=0;for(const l of t){console.log(`Processing file: ${l.name} (${l.size} bytes)`);const a=Le(i,l.name);console.log(`Generated path: ${a}`);const E=Re(l,a,D=>{if(console.log(`File ${l.name} progress: ${D}%`),D===100&&(c++,s)){const F=Math.round(c/d*100);console.log(`Overall progress: ${F}%`),s(F)}});r.push(E)}console.log(`Created ${r.length} upload promises`);const p=await Promise.all(r);console.log("All uploads completed. Results:",p);const u=p.filter(l=>l!==L);return console.log(`Successful uploads: ${u.length}/${p.length}`),u.length===0&&p.length>0?(console.log("All uploads failed, returning default URL"),[L]):(console.log("Final URLs:",u),u)}catch(r){return console.error("Error uploading multiple images:",r),r instanceof Error&&console.error("Error details:",{message:r.message,stack:r.stack}),[L]}},Le=(t,i)=>{const s=new Date().getTime(),r=Math.random().toString(36).substring(2,6);return`book-images/${t}/${s}-${r}.webp`},De=y.object({title:y.string().min(1,{message:"Book title is required"}),author:y.string().min(1,{message:"Author name is required"}),isbn:y.string().optional(),genre:y.string().min(1,{message:"Please select at least one genre"}),condition:y.string().min(1,{message:"Please select a condition"}),description:y.string().min(10,{message:"Description should be at least 10 characters"}),availability:y.string().min(1,{message:"Please select availability option"}),price:y.string().optional(),rentalPrice:y.string().optional(),rentalPeriod:y.string().optional(),securityDepositRequired:y.boolean().optional().default(!1),securityDepositAmount:y.string().optional()}),Ge=()=>{const t=ce(),[i,s]=U.useState(!1);U.useState("");const[r,d]=U.useState([]),[c,p]=U.useState([]),[u,l]=U.useState(0),[a,E]=U.useState(0),[D,F]=U.useState(!1),[B,z]=U.useState(null),[H,X]=U.useState(null),{currentUser:f,userData:R}=de(),K=o=>{const n=o.target.files;if(!n)return;if(r.length+n.length>4){N.error("Maximum 4 images allowed");return}const b=Array.from(n),x=[...r,...b];d(x);const g=b.map(C=>URL.createObjectURL(C)),P=[...c,...g];p(P),r.length===0&&b.length>0&&l(0)},Z=o=>{const n=[...r],b=[...c];n.splice(o,1),b.splice(o,1),d(n),p(b),o===u?(n.length>0,l(0)):o<u&&l(u-1)},ee=o=>{l(o)},oe=async()=>{F(!0),z(null);try{console.log("Attempting to capture GPS location...");const o=await Ne({enableHighAccuracy:!0,timeout:15e3,maximumAge:6e4});return console.log("GPS location captured successfully:",o),X(o),o}catch(o){console.error("Error capturing GPS location:",o);let n="Unable to get your current location.";return o instanceof Error&&(o.message.includes("permission")?n="Location permission denied. Using your registered address instead.":o.message.includes("timeout")?n="Location request timed out. Using your registered address instead.":o.message.includes("unavailable")&&(n="Location service unavailable. Using your registered address instead.")),z(n),null}finally{F(!1)}},te=async()=>{if(!(R!=null&&R.pincode))return console.log("No pincode available for fallback location"),null;try{const n={500001:{latitude:17.385,longitude:78.4867},500032:{latitude:17.4399,longitude:78.3489},500081:{latitude:17.4485,longitude:78.3908},400001:{latitude:18.9322,longitude:72.8264},400051:{latitude:19.0596,longitude:72.8295},110001:{latitude:28.6139,longitude:77.209},110016:{latitude:28.5494,longitude:77.2001},560001:{latitude:12.9716,longitude:77.5946},560066:{latitude:12.9698,longitude:77.75},600001:{latitude:13.0827,longitude:80.2707},600028:{latitude:13.0569,longitude:80.2091}}[R.pincode];if(n)return console.log("Fallback location from pincode mapping:",n),n;const b=R.pincode.substring(0,2),g={50:{latitude:17.385,longitude:78.4867},40:{latitude:19.076,longitude:72.8777},11:{latitude:28.7041,longitude:77.1025},56:{latitude:12.9716,longitude:77.5946},60:{latitude:13.0827,longitude:80.2707},70:{latitude:22.5726,longitude:88.3639},30:{latitude:26.9124,longitude:75.7873},22:{latitude:26.8467,longitude:80.9462}}[b];return g?(console.log("Fallback location from state mapping:",g),g):(console.log("No fallback location available for pincode:",R.pincode),null)}catch(o){return console.error("Error getting fallback location from pincode:",o),null}},ie=async()=>{if(!(f!=null&&f.uid))return console.log("No current user available for community retrieval"),null;try{console.log("Retrieving user community from Firestore..."),await ye();const{doc:o,getDoc:n,getFirestore:b}=await Q(async()=>{const{doc:C,getDoc:S,getFirestore:M}=await import("./index.esm-ehpEbksy.js");return{doc:C,getDoc:S,getFirestore:M}},__vite__mapDeps([0,1])),x=b(),g=o(x,"users",f.uid),P=await n(g);if(P.exists()){const S=P.data().community;return S&&typeof S=="string"&&S.trim()!==""?(console.log("User community retrieved successfully:",S),S.trim()):(console.log("User community is empty or not set"),null)}else return console.log("User document not found in Firestore"),null}catch(o){return console.error("Error retrieving user community:",o),null}},m=ue({resolver:be(De),defaultValues:{title:"",author:"",isbn:"",genre:"",condition:"",description:"",availability:"",price:"",rentalPrice:"",rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:""}}),re=async o=>{var n,b;s(!0);try{console.log("Adding book:",o);const{createBook:x}=await Q(async()=>{const{createBook:h}=await import("./index-BiRTTmW2.js").then(ne=>ne.bf);return{createBook:h}},__vite__mapDeps([2,3]));if(!f){N.error("You must be signed in to add a book"),s(!1);return}console.log("Capturing location for book listing...");let g=null;g=await oe(),g||(console.log("GPS capture failed, trying fallback location..."),g=await te()),g?console.log("Location captured for book:",g):console.log("No location could be determined for book"),console.log("Retrieving user community for book listing...");let P=null;try{P=await ie(),P?(console.log("✅ User community retrieved successfully for book:",P),N.info(`Community "${P}" will be added to your book listing`)):(console.warn("⚠️ No community information available for user - book will be created without community data"),N.warning("No community information found in your profile. Consider updating your profile to help others find books in your area."))}catch(h){console.error("❌ Error retrieving user community:",h),console.error("Community error details:",{message:h.message,stack:h.stack,currentUser:f==null?void 0:f.uid,timestamp:new Date().toISOString()}),N.error("Failed to retrieve community information. Your book will be listed without community data.")}let C=[];try{C=o.genre.split(",").map(h=>h.trim()).filter(h=>h.length>0),C.length===0&&(C=[o.genre.trim()])}catch(h){console.error("Error processing genre:",h),C=[o.genre.trim()]}let S=null;if(o.price&&(S=Number(o.price),isNaN(S))){N.error("Invalid price value. Please enter a valid number."),s(!1);return}let M=null;if(o.rentalPrice&&(M=Number(o.rentalPrice),isNaN(M))){N.error("Invalid rental price value. Please enter a valid number."),s(!1);return}let q=o.rentalPeriod;M||(q=null);let G=null;if(o.securityDepositRequired&&o.securityDepositAmount&&(G=Number(o.securityDepositAmount),isNaN(G))){N.error("Invalid security deposit amount. Please enter a valid number."),s(!1);return}let $=[],W="https://via.placeholder.com/150?text=No+Image";if(r.length>0)try{N.info("Uploading images..."),$=await Ue(r,f.uid,h=>{E(h),console.log(`Upload progress: ${h}%`)}),console.log("Uploaded image URLs:",$),$.length>0&&(W=$[u]||$[0])}catch(h){console.error("Error uploading images:",h),N.error("Failed to upload images. Using default image instead.")}const _={title:o.title.trim(),author:o.author.trim(),isbn:((n=o.isbn)==null?void 0:n.trim())||null,genre:C,condition:o.condition,description:o.description.trim(),imageUrl:W,imageUrls:$.length>0?$:void 0,displayImageIndex:$.length>0?u:void 0,availability:o.availability,price:S,rentalPrice:M,rentalPeriod:q,securityDepositRequired:o.securityDepositRequired,securityDepositAmount:G,ownerId:f.uid,ownerName:f.displayName||((b=f.email)==null?void 0:b.split("@")[0])||"Unknown",ownerEmail:f.email||void 0,ownerCommunity:P||void 0,ownerCoordinates:g,ownerPincode:(R==null?void 0:R.pincode)||void 0,ownerRating:0,perceivedValue:5};console.log("Prepared book data:",_);const se=await x(_);console.log("Book created successfully with ID:",se),N.success("Book added successfully! It will be visible after admin approval."),t("/browse")}catch(x){console.error("Error adding book:",x);let g="Failed to add book. Please try again.";x instanceof Error&&(g=`Error: ${x.message}`,console.error("Error details:",x.message),x.message.includes("permission-denied")?g="You don't have permission to add books. Please check your account.":x.message.includes("network")?g="Network error. Please check your internet connection and try again.":x.message.includes("quota-exceeded")&&(g="Database quota exceeded. Please try again later.")),N.error(g)}finally{s(!1),E(0)}};return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(me,{}),e.jsx("main",{className:"flex-grow",children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Add Your Book"}),e.jsx("p",{className:"text-gray-600",children:"Share your book with the community"})]}),e.jsx(ge,{...m,children:e.jsxs("form",{onSubmit:m.handleSubmit(re),className:"space-y-6",children:[e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(j,{control:m.control,name:"title",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Book Title*"}),e.jsx(k,{children:e.jsx(T,{placeholder:"Enter book title",disabled:i,...o})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"author",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Author*"}),e.jsx(k,{children:e.jsx(T,{placeholder:"Enter author name",disabled:i,...o})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"isbn",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"ISBN (Optional)"}),e.jsx(k,{children:e.jsx(T,{placeholder:"Enter ISBN",disabled:i,...o})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"genre",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Genre*"}),e.jsx(k,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:i,...o,children:[e.jsx("option",{value:"Fantasy",children:"Fantasy"}),e.jsx("option",{value:"Science Fiction",children:"Science Fiction"}),e.jsx("option",{value:"Mystery",children:"Mystery"}),e.jsx("option",{value:"Thriller",children:"Thriller"}),e.jsx("option",{value:"Horror",children:"Horror"}),e.jsx("option",{value:"Romance",children:"Romance"}),e.jsx("option",{value:"Comedy",children:"Comedy"}),e.jsx("option",{value:"Drama",children:"Drama"}),e.jsx("option",{value:"Historical Fiction",children:"Historical Fiction"}),e.jsx("option",{value:"Paranormal",children:"Paranormal"}),e.jsx("option",{value:"Adventure",children:"Adventure"}),e.jsx("option",{value:"Action",children:"Action"}),e.jsx("option",{value:"Western",children:"Western"}),e.jsx("option",{value:"Literary Fiction",children:"Literary Fiction"}),e.jsx("option",{value:"Dystopian",children:"Dystopian"}),e.jsx("option",{value:"Coming-of-Age",children:"Coming-of-Age"}),e.jsx("option",{value:"Young Adult (YA)",children:"Young Adult (YA)"}),e.jsx("option",{value:"Children’s",children:"Children’s"}),e.jsx("option",{value:"Biography",children:"Biography"}),e.jsx("option",{value:"Memoir",children:"Memoir"}),e.jsx("option",{value:"Self-Help",children:"Self-Help"}),e.jsx("option",{value:"Psychology",children:"Psychology"}),e.jsx("option",{value:"Philosophy",children:"Philosophy"}),e.jsx("option",{value:"Business",children:"Business"}),e.jsx("option",{value:"Finance",children:"Finance"}),e.jsx("option",{value:"Leadership",children:"Leadership"}),e.jsx("option",{value:"Science",children:"Science"}),e.jsx("option",{value:"Technology",children:"Technology"}),e.jsx("option",{value:"History",children:"History"}),e.jsx("option",{value:"Politics",children:"Politics"}),e.jsx("option",{value:"Cooking",children:"Cooking"}),e.jsx("option",{value:"Travel",children:"Travel"}),e.jsx("option",{value:"Health & Wellness",children:"Health & Wellness"}),e.jsx("option",{value:"Religion",children:"Religion"}),e.jsx("option",{value:"Spirituality",children:"Spirituality"}),e.jsx("option",{value:"Parenting",children:"Parenting"}),e.jsx("option",{value:"Home & Garden",children:"Home & Garden"}),e.jsx("option",{value:"Art & Design",children:"Art & Design"}),e.jsx("option",{value:"Graphic Novel",children:"Graphic Novel"}),e.jsx("option",{value:"Comic Book",children:"Comic Book"}),e.jsx("option",{value:"Manga",children:"Manga"}),e.jsx("option",{value:"Classic",children:"Classic"}),e.jsx("option",{value:"Poetry",children:"Poetry"}),e.jsx("option",{value:"Essays",children:"Essays"}),e.jsx("option",{value:"Anthology",children:"Anthology"}),e.jsx("option",{value:"Short Stories",children:"Short Stories"}),e.jsx("option",{value:"Education",children:"Education"}),e.jsx("option",{value:"Reference",children:"Reference"}),e.jsx("option",{value:"True Crime",children:"True Crime"}),e.jsx("option",{value:"Inspirational",children:"Inspirational"})]})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"condition",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Condition*"}),e.jsx(k,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:i,...o,children:[e.jsx("option",{value:"",children:"Select Condition"}),e.jsx("option",{value:"New",children:"New"}),e.jsx("option",{value:"Like New",children:"Like New"}),e.jsx("option",{value:"Good",children:"Good"}),e.jsx("option",{value:"Fair",children:"Fair"})]})}),e.jsx(I,{})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Book Images"}),e.jsx("p",{className:"text-sm text-gray-500 mb-2 text-center",children:"Upload up to 4 images of your book. The first image will be the display image."}),e.jsxs("div",{className:"flex items-center justify-center mb-2",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 mr-2",children:[r.length,"/4 images"]}),a>0&&a<100&&e.jsx("div",{className:"w-24 h-2 bg-gray-200 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-burgundy-500",style:{width:`${a}%`}})})]})]}),c.length>0&&e.jsx("div",{className:"grid grid-cols-2 gap-4 mb-4",children:c.map((o,n)=>e.jsxs("div",{className:`relative border rounded-md overflow-hidden ${n===u?"ring-2 ring-burgundy-500":""}`,children:[e.jsx("img",{src:o,alt:`Book image ${n+1}`,className:"w-full h-32 object-contain"}),e.jsxs("div",{className:"absolute top-0 right-0 p-1 flex space-x-1",children:[n!==u&&e.jsx("button",{type:"button",className:"bg-burgundy-500 text-white p-1 rounded-full hover:bg-burgundy-600",onClick:()=>ee(n),title:"Set as display image",children:e.jsx(Y,{className:"h-4 w-4"})}),e.jsx("button",{type:"button",className:"bg-gray-700 text-white p-1 rounded-full hover:bg-gray-800",onClick:()=>Z(n),title:"Remove image",children:e.jsx(pe,{className:"h-4 w-4"})})]}),n===u&&e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-burgundy-500 text-white text-xs py-1 text-center",children:"Display Image"})]},n))}),r.length<4&&e.jsxs("div",{className:"flex justify-center",children:[e.jsx("input",{type:"file",id:"image-upload",className:"hidden",accept:"image/*",multiple:r.length<3,onChange:K,disabled:i}),e.jsxs("label",{htmlFor:"image-upload",className:`flex items-center justify-center text-sm bg-burgundy-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-burgundy-600 ${i?"opacity-50 cursor-not-allowed":""}`,children:[e.jsx(Ie,{className:"h-4 w-4 mr-2"}),r.length===0?"Upload Images":"Add More Images"]})]})]}),e.jsx(j,{control:m.control,name:"availability",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Availability*"}),e.jsx(k,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:i,onChange:n=>{o.onChange(n)},value:o.value,children:[e.jsx("option",{value:"",children:"Select Availability"}),e.jsx("option",{value:"For Exchange",children:"For Exchange"}),e.jsx("option",{value:"For Sale",children:"For Sale"}),e.jsx("option",{value:"For Rent",children:"For Rent"}),e.jsx("option",{value:"For Sale & Exchange",children:"For Sale & Exchange"}),e.jsx("option",{value:"For Rent & Exchange",children:"For Rent & Exchange"}),e.jsx("option",{value:"For Rent & Sale",children:"For Rent & Sale"}),e.jsx("option",{value:"For Rent, Sale & Exchange",children:"For Rent, Sale & Exchange"})]})}),e.jsx(I,{})]})}),m.watch("availability")&&(m.watch("availability").includes("Sale")||m.watch("availability").includes("Rent"))&&e.jsxs("div",{className:"space-y-6",children:[m.watch("availability").includes("Sale")&&e.jsx(j,{control:m.control,name:"price",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Sale Price (₹)"}),e.jsx(k,{children:e.jsx(T,{type:"number",placeholder:"Enter price",disabled:i,...o})}),e.jsx(I,{})]})}),m.watch("availability").includes("Rent")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(j,{control:m.control,name:"rentalPrice",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Rental Price (₹)"}),e.jsx(k,{children:e.jsx(T,{type:"number",placeholder:"Enter rental price",disabled:i,...o})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"rentalPeriod",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Period"}),e.jsx(k,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:i,...o,children:[e.jsx("option",{value:"per day",children:"Per Day"}),e.jsx("option",{value:"per week",children:"Per Week"}),e.jsx("option",{value:"per month",children:"Per Month"})]})}),e.jsx(I,{})]})})]}),e.jsx("div",{className:"mt-4",children:e.jsx(j,{control:m.control,name:"securityDepositRequired",render:({field:o})=>e.jsxs(v,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(k,{children:e.jsx("input",{type:"checkbox",className:"h-4 w-4 mt-1",checked:o.value,onChange:n=>o.onChange(n.target.checked),disabled:i})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(w,{children:"Security Deposit Required"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Require a security deposit for renting this book"})]})]})})}),m.watch("securityDepositRequired")&&e.jsx("div",{className:"mt-4",children:e.jsx(j,{control:m.control,name:"securityDepositAmount",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Security Deposit Amount (₹)"}),e.jsx(k,{children:e.jsx(T,{type:"number",placeholder:"Enter security deposit amount",disabled:i,...o})}),e.jsx(I,{})]})})})]})]})]})]}),e.jsx(j,{control:m.control,name:"description",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Description*"}),e.jsx(k,{children:e.jsx(je,{placeholder:"Describe the book, its condition, and any other details...",className:"min-h-[120px]",disabled:i,...o})}),e.jsx(I,{})]})}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(he,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Location Information"}),e.jsx("p",{className:"text-sm text-blue-800 mb-2",children:"When you submit this book listing, we will automatically capture your current GPS location to help other users find books near them. This location data will be used solely for:"}),e.jsxs("ul",{className:"text-sm text-blue-800 list-disc list-inside space-y-1 mb-3",children:[e.jsx("li",{children:"Calculating and displaying distance to other users browsing books"}),e.jsx("li",{children:"Helping users find books in their local area"}),e.jsx("li",{children:"Improving the book discovery experience"})]}),e.jsx("p",{className:"text-sm text-blue-800",children:"If GPS location cannot be accessed, we'll use your registered address as a fallback. Your exact location will not be displayed to other users - only the calculated distance."}),D&&e.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-blue-700",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),e.jsx("span",{children:"Capturing your location..."})]}),H&&e.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-green-700",children:[e.jsx(Y,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{children:"Location captured successfully"})]}),B&&e.jsxs("div",{className:"mt-3 flex items-start space-x-2 text-sm text-amber-700",children:[e.jsx(Se,{className:"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:B})]})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(O,{type:"button",variant:"outline",disabled:i,onClick:()=>t("/"),children:"Cancel"}),e.jsxs(O,{type:"submit",disabled:i,className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Add Book"]})]})]})})]})})}),e.jsx(fe,{})]})};export{Ge as default};
