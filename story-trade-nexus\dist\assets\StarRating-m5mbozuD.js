const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-ehpEbksy.js","assets/index.esm2017-H7c5Bkvh.js"])))=>i.map(i=>d[i]);
import{K as y,_ as R,N as x,r as k,j as u,am as I}from"./index-BsSeXuDn.js";const b="peerbooks_feedback_submissions",p=60*60*1e3,j=3,N=()=>{try{const e=localStorage.getItem(b);if(!e)return{allowed:!0};const t=JSON.parse(e),s=Date.now(),a=t.filter(o=>s-o<p);if(a.length>=j){const o=Math.min(...a);return{allowed:!1,remainingTime:p-(s-o)}}return{allowed:!0}}catch(e){return console.warn("Error checking rate limit:",e),{allowed:!0}}},F=()=>{try{const e=localStorage.getItem(b),t=e?JSON.parse(e):[],s=Date.now();t.push(s);const a=t.filter(o=>s-o<p);localStorage.setItem(b,JSON.stringify(a))}catch(e){console.warn("Error recording submission:",e)}},h=e=>e.trim().replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"").replace(/<[^>]*>/g,"").replace(/javascript:/gi,"").replace(/on\w+\s*=/gi,"").substring(0,5e3),L=async e=>{try{const t=N();if(!t.allowed){const m=Math.ceil((t.remainingTime||0)/6e4);throw new Error(`Rate limit exceeded. Please wait ${m} minutes before submitting again.`)}await y();const{collection:s,addDoc:a,serverTimestamp:o}=await R(async()=>{const{collection:m,addDoc:g,serverTimestamp:f}=await import("./index.esm-ehpEbksy.js");return{collection:m,addDoc:g,serverTimestamp:f}},__vite__mapDeps([0,1]));console.log("Submitting feedback:",{...e,message:"[REDACTED]"});const n={...e,name:h(e.name),email:h(e.email),subject:h(e.subject),message:h(e.message)};if(!n.name||!n.email||!n.subject||!n.message)throw new Error("All required fields must be filled");if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n.email))throw new Error("Please enter a valid email address");const c=s(x,"feedback"),d={...n,userAgent:navigator.userAgent,createdAt:o(),isRead:!1},r=await a(c,d);return console.log("Feedback submitted successfully with ID:",r.id),F(),console.log("Email notification would be sent to admin for feedback:",r.id),r.id}catch(t){throw console.error("Error submitting feedback:",t),t}},M=async()=>{try{await y();const{collection:e,query:t,orderBy:s,getDocs:a}=await R(async()=>{const{collection:d,query:r,orderBy:m,getDocs:g}=await import("./index.esm-ehpEbksy.js");return{collection:d,query:r,orderBy:m,getDocs:g}},__vite__mapDeps([0,1])),o=e(x,"feedback"),n=t(o,s("createdAt","desc")),i=await a(n),c=[];return i.forEach(d=>{const r=d.data();c.push({id:d.id,name:r.name||"",email:r.email||"",subject:r.subject||"",category:r.category||"General Feedback",message:r.message||"",rating:r.rating,userAgent:r.userAgent,createdAt:r.createdAt,isRead:r.isRead||!1,readAt:r.readAt||null,adminResponse:r.adminResponse,respondedAt:r.respondedAt})}),console.log(`Fetched ${c.length} feedback submissions`),c}catch(e){throw console.error("Error fetching feedback:",e),e}},O=async e=>{try{await y();const{doc:t,updateDoc:s,serverTimestamp:a}=await R(async()=>{const{doc:n,updateDoc:i,serverTimestamp:c}=await import("./index.esm-ehpEbksy.js");return{doc:n,updateDoc:i,serverTimestamp:c}},__vite__mapDeps([0,1])),o=t(x,"feedback",e);await s(o,{isRead:!0,readAt:a()}),console.log("Feedback marked as read:",e)}catch(t){throw console.error("Error marking feedback as read:",t),t}},P=async()=>{try{const e=await M(),t={totalSubmissions:e.length,averageRating:0,categoryBreakdown:{},unreadCount:0};let s=0,a=0;return e.forEach(o=>{t.categoryBreakdown[o.category]=(t.categoryBreakdown[o.category]||0)+1,o.isRead||t.unreadCount++,o.rating&&(s+=o.rating,a++)}),a>0&&(t.averageRating=Math.round(s/a*10)/10),t}catch(e){throw console.error("Error getting feedback stats:",e),e}},B=e=>{const t=Math.ceil(e/6e4);if(t<60)return`${t} minute${t!==1?"s":""}`;const s=Math.ceil(t/60);return`${s} hour${s!==1?"s":""}`},C=({value:e=0,onChange:t,readonly:s=!1,size:a="md",showText:o=!0,className:n="",disabled:i=!1})=>{const[c,d]=k.useState(0),[r,m]=k.useState(!1),g={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},f={sm:"text-xs",md:"text-sm",lg:"text-base"},E={0:"No rating",1:"Poor",2:"Fair",3:"Good",4:"Very Good",5:"Excellent"},_=l=>{s||i||t==null||t(l)},S=l=>{s||i||(d(l),m(!0))},v=()=>{s||i||(m(!1),d(0))},w=r?c:e,A=r?E[c]:E[e];return u.jsxs("div",{className:`flex items-center gap-2 ${n}`,children:[u.jsx("div",{className:"flex items-center gap-1",onMouseLeave:v,children:[1,2,3,4,5].map(l=>{const T=l<=w,$=!s&&!i;return u.jsx("button",{type:"button",className:`
                ${g[a]}
                ${$?"cursor-pointer hover:scale-110 transition-transform":"cursor-default"}
                ${i?"opacity-50":""}
                focus:outline-none focus:ring-2 focus:ring-burgundy-500 focus:ring-offset-1 rounded
              `,onClick:()=>_(l),onMouseEnter:()=>S(l),disabled:i||s,"aria-label":`Rate ${l} star${l!==1?"s":""}`,children:u.jsx(I,{className:`
                  ${g[a]}
                  transition-colors duration-150
                  ${T?"fill-yellow-400 text-yellow-400":"fill-none text-gray-300 hover:text-yellow-400"}
                `})},l)})}),o&&u.jsxs("div",{className:"flex items-center gap-2",children:[u.jsx("span",{className:`${f[a]} text-gray-600 font-medium`,children:w>0?`${w}/5`:""}),w>0&&u.jsxs("span",{className:`${f[a]} text-gray-500`,children:["(",A,")"]})]})]})};export{C as S,P as a,N as c,B as f,M as g,O as m,L as s};
