import{j as e,m as s,n as a,o as l,p as r,A as t,q as i,r as n,s as d}from"./chunk-CpdlqjqK.js";import{r as c}from"./chunk-b0HHmiEU.js";import{x as o,N as m,O as x,c as h,j,U as p,F as u,l as f,m as g,n as N,o as y,I as b,p as v,Q as w,R as k,E as C,T as A,q as P,V as S,W as R}from"./index-DwRq5Uwb.js";import{z as U,J as z}from"./chunk-Cw96wKwP.js";import{D as F,a as D,b as M,c as O,d as T,e as q}from"./chunk-BjtUNXro.js";import{S as E,a as V,b as L,c as $,d as J}from"./chunk-BpJWUrWd.js";import{u as H}from"./chunk-DgNUPAoM.js";import{A as I}from"./chunk-BS5eevMn.js";import{N as Q,R as W,U as Z,O as _,q as B,T as G,x as K,E as X,m as Y}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";const ee=n,se=d,ae=c.forwardRef((({className:a,...l},r)=>e.jsx(s,{className:o("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l,ref:r})));ae.displayName=s.displayName;const le=c.forwardRef((({className:s,...l},r)=>e.jsxs(se,{children:[e.jsx(ae,{}),e.jsx(a,{ref:r,className:o("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",s),...l})]})));le.displayName=a.displayName;const re=({className:s,...a})=>e.jsx("div",{className:o("flex flex-col space-y-2 text-center sm:text-left",s),...a});re.displayName="AlertDialogHeader";const te=({className:s,...a})=>e.jsx("div",{className:o("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...a});te.displayName="AlertDialogFooter";const ie=c.forwardRef((({className:s,...a},r)=>e.jsx(l,{ref:r,className:o("text-lg font-semibold",s),...a})));ie.displayName=l.displayName;const ne=c.forwardRef((({className:s,...a},l)=>e.jsx(r,{ref:l,className:o("text-sm text-muted-foreground",s),...a})));ne.displayName=r.displayName;const de=c.forwardRef((({className:s,...a},l)=>e.jsx(t,{ref:l,className:o(m(),s),...a})));de.displayName=t.displayName;const ce=c.forwardRef((({className:s,...a},l)=>e.jsx(i,{ref:l,className:o(m({variant:"outline"}),"mt-2 sm:mt-0",s),...a})));ce.displayName=i.displayName;const oe=U.object({email:U.string().email({message:"Please enter a valid email address"}),password:U.string().min(8,{message:"Password must be at least 8 characters"}).refine((e=>{const s=/[A-Z]/.test(e),a=/[a-z]/.test(e),l=/[0-9]/.test(e),r=/[!@#$%^&*(),.?":{}|<>]/.test(e);return s&&a&&l&&r}),{message:"Password must include uppercase, lowercase, number, and special character"}),displayName:U.string().min(2,{message:"Display name must be at least 2 characters"}),phone:U.string().min(10,{message:"Please enter a valid phone number"}).max(15).optional(),address:U.string().optional(),apartment:U.string().optional(),city:U.string().optional(),state:U.string().optional(),pincode:U.string().min(6,{message:"Please enter a valid pincode"}).max(6).optional(),role:U.enum(["user","admin"],{required_error:"Please select a role"})}),me=()=>{const[s,a]=c.useState([]),[l,r]=c.useState(!0),[t,i]=c.useState(null),[n,d]=c.useState(null),[o,m]=c.useState(!1),[U,se]=c.useState(!1),[ae,me]=c.useState(!1),[xe,he]=c.useState(!1),[je,pe]=c.useState(!1),[ue,fe]=c.useState(!1),[ge,Ne]=c.useState(!1),[ye,be]=c.useState(!1);c.useEffect((()=>{ve()}),[]);const ve=async()=>{try{r(!0),i(null);const e=await x();a(e)}catch(e){i("Failed to load users. Please try again.")}finally{r(!1)}},we=H({resolver:P(oe),defaultValues:{email:"",password:"",displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",role:"user"}});return e.jsxs(I,{title:"User Management",description:"Manage users and their permissions",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"User Management"}),e.jsx("p",{className:"text-gray-600",children:"Manage users and their permissions"})]}),e.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[e.jsxs(h,{variant:"default",onClick:()=>me(!0),className:"flex items-center",children:[e.jsx(Q,{className:"h-4 w-4 mr-2"}),"Create New User"]}),e.jsx(h,{variant:"outline",onClick:ve,disabled:l,children:l?e.jsxs(e.Fragment,{children:[e.jsx(W,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(W,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),t&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:t})}),l?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(j,{size:"lg"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading users..."})]}):s.length>0?e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[e.jsx("thead",{className:"bg-gray-50",children:e.jsxs("tr",{children:[e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"User"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Email"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Role"}),e.jsx("th",{scope:"col",className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),e.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map((s=>e.jsxs("tr",{children:[e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:s.photoURL?e.jsx("img",{className:"h-10 w-10 rounded-full",src:s.photoURL,alt:""}):e.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center",children:e.jsx(Z,{className:"h-6 w-6 text-gray-500"})})}),e.jsxs("div",{className:"ml-4",children:[e.jsx("div",{className:"text-sm font-medium text-gray-900",children:s.displayName||"No Name"}),e.jsx("div",{className:"text-sm text-gray-500",children:s.phone||"No Phone"})]})]})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:e.jsx("div",{className:"text-sm text-gray-900",children:s.email})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.role===p.Admin?e.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-burgundy-100 text-burgundy-800",children:"Admin"}):e.jsx("span",{className:"px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800",children:"User"})}),e.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:e.jsxs("div",{className:"flex gap-2",children:[s.role===p.Admin?e.jsxs(h,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>(e=>{d(e),se(!0)})(s),children:[e.jsx(_,{className:"h-4 w-4"}),"Remove Admin"]}):e.jsxs(h,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>(e=>{d(e),m(!0)})(s),children:[e.jsx(B,{className:"h-4 w-4"}),"Make Admin"]}),e.jsxs(h,{variant:"outline",size:"sm",className:"flex items-center gap-1 text-red-600 hover:text-red-800 hover:bg-red-50",onClick:()=>(e=>{d(e),he(!0)})(s),children:[e.jsx(G,{className:"h-4 w-4"}),"Delete"]})]})})]},s.uid)))})]})}):e.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No users found"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"There are no users in the system."})]}),e.jsx(F,{open:o,onOpenChange:m,children:e.jsxs(D,{children:[e.jsxs(M,{children:[e.jsx(O,{children:"Promote to Admin"}),e.jsxs(T,{children:["Are you sure you want to promote ",(null==n?void 0:n.displayName)||(null==n?void 0:n.email)," to admin? This will give them full access to the admin dashboard and all administrative functions."]})]}),e.jsxs(q,{children:[e.jsx(h,{variant:"outline",onClick:()=>m(!1),children:"Cancel"}),e.jsx(h,{variant:"default",onClick:async()=>{if(n)try{pe(!0),await C(n.uid),a(s.map((e=>e.uid===n.uid?{...e,role:p.Admin}:e))),z.success(`${n.displayName||n.email} has been promoted to admin`),m(!1)}catch(e){z.error("Failed to promote user. Please try again.")}finally{pe(!1)}},disabled:je,children:je?e.jsxs(e.Fragment,{children:[e.jsx(j,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Promotion"})]})]})}),e.jsx(F,{open:U,onOpenChange:se,children:e.jsxs(D,{children:[e.jsxs(M,{children:[e.jsx(O,{children:"Remove Admin Role"}),e.jsxs(T,{children:["Are you sure you want to remove admin privileges from ",(null==n?void 0:n.displayName)||(null==n?void 0:n.email),"? They will no longer have access to the admin dashboard and administrative functions."]})]}),e.jsxs(q,{children:[e.jsx(h,{variant:"outline",onClick:()=>se(!1),children:"Cancel"}),e.jsx(h,{variant:"destructive",onClick:async()=>{if(n)try{pe(!0),await A(n.uid),a(s.map((e=>e.uid===n.uid?{...e,role:p.User}:e))),z.success(`Admin role removed from ${n.displayName||n.email}`),se(!1)}catch(e){z.error("Failed to remove admin role. Please try again.")}finally{pe(!1)}},disabled:je,children:je?e.jsxs(e.Fragment,{children:[e.jsx(j,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm Removal"})]})]})}),e.jsx(ee,{open:xe,onOpenChange:he,children:e.jsxs(le,{children:[e.jsxs(re,{children:[e.jsxs(ie,{className:"text-red-600 flex items-center gap-2",children:[e.jsx(K,{className:"h-5 w-5"}),"Delete User Account"]}),e.jsxs(ne,{children:["Are you sure you want to delete the account for ",e.jsx("strong",{children:(null==n?void 0:n.displayName)||(null==n?void 0:n.email)}),"?",e.jsx("br",{}),e.jsx("br",{}),"This action is permanent and cannot be undone. All user data will be removed from the system."]})]}),e.jsxs(te,{children:[e.jsx(ce,{disabled:ge,children:"Cancel"}),e.jsx(de,{onClick:async()=>{if(n)try{Ne(!0);const e=await R(n.uid);e.success?(a(s.filter((e=>e.uid!==n.uid))),z.success(e.message)):z.error(e.message),he(!1)}catch(e){z.error("Failed to delete user. Please try again.")}finally{Ne(!1)}},disabled:ge,className:"bg-red-600 hover:bg-red-700 text-white",children:ge?e.jsxs(e.Fragment,{children:[e.jsx(j,{size:"sm",className:"mr-2"}),"Deleting..."]}):"Delete User"})]})]})}),e.jsx(F,{open:ae,onOpenChange:e=>{me(e),e||(we.reset(),be(!1))},children:e.jsxs(D,{className:"sm:max-w-[600px] max-h-[90vh] overflow-y-auto",children:[e.jsxs(M,{children:[e.jsx(O,{children:"Create New User"}),e.jsx(T,{children:"Fill in the details to create a new user account. All fields marked with * are required."})]}),e.jsx(u,{...we,children:e.jsxs("form",{onSubmit:we.handleSubmit((async e=>{try{fe(!0);const s=await S(e.email,e.password,{displayName:e.displayName,phone:e.phone,address:e.address,apartment:e.apartment,city:e.city,state:e.state,pincode:e.pincode,role:e.role});s.success?(z.success(s.message),me(!1),we.reset(),ve()):z.error(s.message)}catch(s){z.error("Failed to create user. Please try again.")}finally{fe(!1)}})),className:"space-y-4 py-2",children:[e.jsx(f,{control:we.control,name:"email",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"Email *"}),e.jsx(y,{children:e.jsx(b,{placeholder:"<EMAIL>",...s,disabled:ue})}),e.jsx(v,{})]})}),e.jsx(f,{control:we.control,name:"password",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"Password *"}),e.jsxs("div",{className:"relative",children:[e.jsx(y,{children:e.jsx(b,{type:ye?"text":"password",placeholder:"••••••••",...s,disabled:ue})}),e.jsx(h,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3",onClick:()=>be(!ye),children:ye?e.jsx(X,{className:"h-4 w-4"}):e.jsx(Y,{className:"h-4 w-4"})})]}),e.jsx(w,{children:"Password must be at least 8 characters and include uppercase, lowercase, number, and special character."}),e.jsx(v,{})]})}),e.jsx(f,{control:we.control,name:"displayName",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"Display Name *"}),e.jsx(y,{children:e.jsx(b,{placeholder:"John Doe",...s,disabled:ue})}),e.jsx(v,{})]})}),e.jsx(f,{control:we.control,name:"phone",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"Phone Number"}),e.jsx(y,{children:e.jsx(b,{placeholder:"9876543210",...s,disabled:ue})}),e.jsx(v,{})]})}),e.jsx(f,{control:we.control,name:"role",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"User Role *"}),e.jsxs(E,{onValueChange:s.onChange,defaultValue:s.value,disabled:ue,children:[e.jsx(y,{children:e.jsx(V,{children:e.jsx(L,{placeholder:"Select a role"})})}),e.jsxs($,{children:[e.jsx(J,{value:"user",children:"Regular User"}),e.jsx(J,{value:"admin",children:"Administrator"})]})]}),e.jsx(v,{})]})}),e.jsx(f,{control:we.control,name:"address",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"Address"}),e.jsx(y,{children:e.jsx(b,{placeholder:"123 Main St",...s,disabled:ue})}),e.jsx(v,{})]})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(f,{control:we.control,name:"city",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"City"}),e.jsx(y,{children:e.jsx(b,{placeholder:"Mumbai",...s,disabled:ue})}),e.jsx(v,{})]})}),e.jsx(f,{control:we.control,name:"state",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"State"}),e.jsxs(E,{onValueChange:s.onChange,value:s.value,disabled:ue,children:[e.jsx(y,{children:e.jsx(V,{children:e.jsx(L,{placeholder:"Select a state"})})}),e.jsx($,{children:k.map((s=>e.jsx(J,{value:s,children:s},s)))})]}),e.jsx(v,{})]})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(f,{control:we.control,name:"pincode",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"Pincode"}),e.jsx(y,{children:e.jsx(b,{placeholder:"400001",...s,disabled:ue})}),e.jsx(v,{})]})}),e.jsx(f,{control:we.control,name:"apartment",render:({field:s})=>e.jsxs(g,{children:[e.jsx(N,{children:"Apartment"}),e.jsx(y,{children:e.jsx(b,{placeholder:"Apartment name",...s,disabled:ue})}),e.jsx(v,{})]})})]}),e.jsxs(q,{className:"pt-4",children:[e.jsx(h,{variant:"outline",type:"button",onClick:()=>me(!1),disabled:ue,children:"Cancel"}),e.jsx(h,{type:"submit",disabled:ue,children:ue?e.jsxs(e.Fragment,{children:[e.jsx(j,{size:"sm",className:"mr-2"}),"Creating User..."]}):"Create User"})]})]})})]})})]})};export{me as default};
