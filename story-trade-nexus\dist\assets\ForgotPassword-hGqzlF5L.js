import{j as e}from"./chunk-CpdlqjqK.js";import{r as s,L as a}from"./chunk-b0HHmiEU.js";import{u as r,M as t,a as n,F as l,l as i,m as c,n as o,o as m,I as d,p as x,q as h}from"./index-DwRq5Uwb.js";import{z as j,J as u}from"./chunk-Cw96wKwP.js";import{u as p}from"./chunk-DgNUPAoM.js";import{M as f}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";const y=j.object({email:j.string().email({message:"Please enter a valid email address"})}),g=()=>{const[j,g]=s.useState(!1),[b,w]=s.useState(!1),{resetPassword:N}=r(),k=p({resolver:h(y),defaultValues:{email:""}});return b?e.jsx(t,{children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:e.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto bg-green-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:e.jsx(f,{className:"h-8 w-8 text-green-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Check Your Email"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"We've sent a password reset link to your email address. Please check your inbox and follow the instructions."}),e.jsx(a,{to:"/signin",children:e.jsx(n,{variant:"link",children:"Back to Sign In"})})]})})})}):e.jsx(t,{children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Forgot Password"}),e.jsx("p",{className:"text-gray-600",children:"Enter your email to receive a password reset link"})]}),e.jsx(l,{...k,children:e.jsxs("form",{onSubmit:k.handleSubmit((async e=>{g(!0);try{await N(e.email),u.success("Password reset link sent! Check your email inbox."),w(!0)}catch(s){const e=s instanceof Error?s.message:"Failed to send reset link";u.error(e)}finally{g(!1)}})),className:"space-y-6",children:[e.jsx(i,{control:k.control,name:"email",render:({field:s})=>e.jsxs(c,{children:[e.jsx(o,{children:"Email"}),e.jsx(m,{children:e.jsx(d,{placeholder:"<EMAIL>",type:"email",disabled:j,...s})}),e.jsx(x,{})]})}),e.jsxs(n,{type:"submit",className:"w-full flex items-center justify-center gap-2",disabled:j,children:[e.jsx(f,{className:"h-4 w-4"}),"Send Reset Link"]})]})}),e.jsx("div",{className:"text-center mt-6",children:e.jsxs("p",{className:"text-gray-600",children:["Remember your password? "," ",e.jsx(a,{to:"/signin",className:"text-burgundy-500 hover:underline font-medium",children:"Sign In"})]})})]})})})};export{g as default};
