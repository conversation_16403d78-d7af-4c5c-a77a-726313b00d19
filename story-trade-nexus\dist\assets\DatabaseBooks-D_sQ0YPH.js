import{r as l,Q as v,J as y,j as e,H as b,h as n,l as o,T as a,L as h,c as w,G as k,B as j,U as A,V as B,X as S}from"./index-BsSeXuDn.js";const D=()=>{const[i,p]=l.useState([]),[t,d]=l.useState(!0),[c,x]=l.useState(null),[r,g]=l.useState(!0);l.useEffect(()=>{m()},[r]);const m=async()=>{try{d(!0),x(null),console.log("Fetching all books from database...");const s=await v(r);p(s),console.log(`Found ${s.length} books in the database`)}catch(s){console.error("Error fetching books:",s),x("Failed to fetch books from the database"),y.error("Failed to fetch books")}finally{d(!1)}},f=s=>!s||s===j.Approved?e.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:[e.jsx(A,{className:"w-3 h-3 mr-1"}),"Approved"]}):s===j.Pending?e.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:[e.jsx(B,{className:"w-3 h-3 mr-1"}),"Pending"]}):e.jsxs("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800",children:[e.jsx(S,{className:"w-3 h-3 mr-1"}),"Rejected"]}),u=s=>new Date(s).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(b,{}),e.jsx("main",{className:"flex-grow",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Database Books"}),e.jsx("p",{className:"text-gray-600",children:"View all books in the database"})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 mt-4 md:mt-0",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("input",{type:"checkbox",id:"includeUnapproved",checked:r,onChange:s=>g(s.target.checked),className:"mr-2 h-4 w-4"}),e.jsx("label",{htmlFor:"includeUnapproved",className:"text-sm text-gray-700",children:"Include unapproved books"})]}),e.jsx(n,{variant:"outline",onClick:m,disabled:t,className:"text-sm",children:t?e.jsxs(e.Fragment,{children:[e.jsx(o,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(o,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),c&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:c})}),t?e.jsx("div",{className:"space-y-4",children:[...Array(5)].map((s,N)=>e.jsx("div",{className:"bg-white rounded-lg shadow-md p-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx(a,{className:"h-32 w-32 rounded-md"}),e.jsxs("div",{className:"flex-1",children:[e.jsx(a,{className:"h-6 w-3/4 mb-2"}),e.jsx(a,{className:"h-4 w-1/2 mb-4"}),e.jsx(a,{className:"h-4 w-full mb-2"}),e.jsx(a,{className:"h-4 w-full mb-2"}),e.jsx(a,{className:"h-4 w-3/4"})]})]})},N))}):i.length>0?e.jsx("div",{className:"space-y-4",children:i.map(s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md p-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"w-32 h-32 flex-shrink-0",children:e.jsx("img",{src:s.imageUrl||"https://via.placeholder.com/150?text=No+Image",alt:s.title,className:"w-full h-full object-cover rounded-md"})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-start",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"text-xl font-medium text-navy-800",children:s.title}),e.jsxs("p",{className:"text-gray-600 mb-2",children:["by ",s.author]})]}),e.jsx("div",{className:"mb-2 sm:mb-0",children:f(s.approvalStatus)})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 mb-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"ID:"})," ",s.id]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Owner:"})," ",s.ownerName," (",s.ownerId,")"]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Genre:"})," ",Array.isArray(s.genre)?s.genre.join(", "):s.genre]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Condition:"})," ",s.condition]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Availability:"})," ",s.availability]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Created:"})," ",u(s.createdAt)]})]}),e.jsx("p",{className:"text-sm text-gray-700 mb-3 line-clamp-2",children:s.description}),e.jsx("div",{className:"flex gap-2",children:e.jsx(h,{to:`/books/${s.id}`,children:e.jsxs(n,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[e.jsx(w,{className:"h-4 w-4"}),"View Details"]})})})]})]})},s.id))}):e.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found in the database"}),e.jsx("p",{className:"text-burgundy-500 mb-4",children:"Try seeding the database with sample books"}),e.jsx(h,{to:"/seed-books",children:e.jsx(n,{children:"Seed Database"})})]})]})}),e.jsx(k,{})]})};export{D as default};
