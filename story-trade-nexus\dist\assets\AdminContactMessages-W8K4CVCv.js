import{u as A,r as t,t as l,j as e,i as M,h as c,k as u,l as C,m as D,n as E}from"./index-BsSeXuDn.js";import{g as F,m as S,a as P}from"./contactMessageService-BxQvzioh.js";import{D as L,a as O,b as U,c as T,d as B}from"./dialog-Br8RT3vT.js";import{A as V}from"./AdminLayout-BrYjZikF.js";import{P as $}from"./phone-DFNQbOdN.js";import{E as j}from"./eye-off-BTt5hPar.js";import{E as f}from"./eye-Dw66r16I.js";import{f as q}from"./format-cwXK75ha.js";import"./users-C6U-K-0N.js";const Z=()=>{A();const[i,o]=t.useState([]),[m,p]=t.useState(!0),[a,n]=t.useState(null),[R,N]=t.useState(!1),[x,d]=t.useState(!1);t.useEffect(()=>{v()},[]);const v=async()=>{try{p(!0);const s=await F();o(s)}catch(s){console.error("Error loading contact messages:",s),l({title:"Error",description:"Failed to load contact messages. Please try again.",variant:"destructive"})}finally{p(!1)}},h=async s=>{try{d(!0),await S(s),o(i.map(r=>r.id===s?{...r,isRead:!0,readAt:new Date}:r)),a&&a.id===s&&n({...a,isRead:!0,readAt:new Date}),l({title:"Success",description:"Message marked as read.",variant:"default"})}catch(r){console.error("Error marking message as read:",r),l({title:"Error",description:"Failed to mark message as read. Please try again.",variant:"destructive"})}finally{d(!1)}},y=async s=>{try{d(!0),await P(s),o(i.map(r=>r.id===s?{...r,isRead:!1,readAt:null}:r)),a&&a.id===s&&n({...a,isRead:!1,readAt:null}),l({title:"Success",description:"Message marked as unread.",variant:"default"})}catch(r){console.error("Error marking message as unread:",r),l({title:"Error",description:"Failed to mark message as unread. Please try again.",variant:"destructive"})}finally{d(!1)}},k=s=>{n(s),N(!0),!s.isRead&&s.id&&h(s.id)},w=()=>{N(!1),n(null)},g=s=>{if(!s)return"N/A";const r=s.toDate?s.toDate():new Date(s);return q(r,"MMM d, yyyy h:mm a")},b=i.filter(s=>!s.isRead).length;return e.jsxs(V,{title:"Contact Messages",description:"View and manage contact messages from users",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Contact Messages"}),e.jsxs("p",{className:"text-gray-600",children:["View and respond to messages from users",b>0&&e.jsxs(M,{variant:"destructive",className:"ml-2",children:[b," unread"]})]})]}),e.jsx(c,{onClick:v,variant:"outline",className:"mt-4 md:mt-0",disabled:m,children:m?e.jsxs(e.Fragment,{children:[e.jsx(u,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(C,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),m?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(u,{className:"h-8 w-8 animate-spin text-burgundy-500"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading messages..."})]}):i.length===0?e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[e.jsx(D,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Messages Yet"}),e.jsx("p",{className:"text-gray-600",children:"There are no contact messages from users yet. Messages will appear here when users send them through the Contact Us page."})]}):e.jsx("div",{className:"space-y-4",children:i.map(s=>e.jsx("div",{className:`bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer ${s.isRead?"":"border-l-4 border-burgundy-500"}`,onClick:()=>k(s),children:e.jsxs("div",{className:"flex flex-col md:flex-row justify-between",children:[e.jsxs("div",{className:"flex-1",children:[e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx(E,{className:"h-4 w-4 text-gray-500 mr-2"}),e.jsx("span",{className:"text-navy-800 font-medium",children:s.email}),!s.isRead&&e.jsx(M,{variant:"default",className:"ml-2",children:"New"})]}),e.jsxs("div",{className:"flex items-center mb-2",children:[e.jsx($,{className:"h-4 w-4 text-gray-500 mr-2"}),e.jsx("span",{className:"text-gray-600",children:s.phone})]}),e.jsx("p",{className:"text-gray-700 line-clamp-2 mb-2",children:s.message}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Received: ",g(s.createdAt)]})]}),e.jsx("div",{className:"mt-4 md:mt-0 md:ml-4 flex items-center",children:e.jsx(c,{variant:"ghost",size:"sm",onClick:r=>{r.stopPropagation(),s.id&&(s.isRead?y(s.id):h(s.id))},disabled:x,children:s.isRead?e.jsxs(e.Fragment,{children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})})]})},s.id))}),e.jsx(L,{open:R,onOpenChange:w,children:e.jsxs(O,{className:"sm:max-w-lg",children:[e.jsxs(U,{children:[e.jsx(T,{children:"Message Details"}),e.jsx(B,{children:"Contact message from user"})]}),a&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"From:"}),e.jsx("div",{className:"col-span-2",children:a.email})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Phone:"}),e.jsx("div",{className:"col-span-2",children:a.phone})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Received:"}),e.jsx("div",{className:"col-span-2",children:g(a.createdAt)})]}),e.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[e.jsx("div",{className:"font-medium",children:"Status:"}),e.jsx("div",{className:"col-span-2",children:a.isRead?e.jsxs("span",{className:"text-green-600 flex items-center",children:[e.jsx(f,{className:"h-4 w-4 mr-1"}),"Read ",a.readAt&&`(${g(a.readAt)})`]}):e.jsxs("span",{className:"text-burgundy-600 flex items-center",children:[e.jsx(j,{className:"h-4 w-4 mr-1"}),"Unread"]})})]}),e.jsxs("div",{className:"py-2",children:[e.jsx("div",{className:"font-medium mb-2",children:"Message:"}),e.jsx("div",{className:"bg-gray-50 p-4 rounded-md whitespace-pre-wrap",children:a.message})]}),e.jsxs("div",{className:"flex justify-end space-x-2",children:[e.jsx(c,{variant:"outline",onClick:w,children:"Close"}),e.jsx(c,{variant:a.isRead?"outline":"default",onClick:()=>{a.id&&(a.isRead?y(a.id):h(a.id))},disabled:x,children:x?e.jsx(u,{className:"h-4 w-4 animate-spin"}):a.isRead?e.jsxs(e.Fragment,{children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):e.jsxs(e.Fragment,{children:[e.jsx(f,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})]})]})]})})]})};export{Z as default};
