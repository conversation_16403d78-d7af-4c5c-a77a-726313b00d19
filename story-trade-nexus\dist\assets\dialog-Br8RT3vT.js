import{an as U,r as i,ao as y,j as n,ap as x,aq as m,ar as C,as as X,at as Y,au as Z,av as J,aw as Q,ax as ee,ay as te,az as oe,aA as N,aB as ae,a1 as p,X as ne}from"./index-BsSeXuDn.js";var R="Dialog",[E,he]=U(R),[se,d]=E(R),b=e=>{const{__scopeDialog:t,children:o,open:s,defaultOpen:r,onOpenChange:a,modal:l=!0}=e,c=i.useRef(null),u=i.useRef(null),[g=!1,v]=oe({prop:s,defaultProp:r,onChange:a});return n.jsx(se,{scope:t,triggerRef:c,contentRef:u,contentId:N(),titleId:N(),descriptionId:N(),open:g,onOpenChange:v,onOpenToggle:i.useCallback(()=>v(K=>!K),[v]),modal:l,children:o})};b.displayName=R;var P="DialogTrigger",O=i.forwardRef((e,t)=>{const{__scopeDialog:o,...s}=e,r=d(P,o),a=y(t,r.triggerRef);return n.jsx(x.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":j(r.open),...s,ref:a,onClick:m(e.onClick,r.onOpenToggle)})});O.displayName=P;var h="DialogPortal",[re,I]=E(h,{forceMount:void 0}),w=e=>{const{__scopeDialog:t,forceMount:o,children:s,container:r}=e,a=d(h,t);return n.jsx(re,{scope:t,forceMount:o,children:i.Children.map(s,l=>n.jsx(C,{present:o||a.open,children:n.jsx(ae,{asChild:!0,container:r,children:l})}))})};w.displayName=h;var D="DialogOverlay",A=i.forwardRef((e,t)=>{const o=I(D,e.__scopeDialog),{forceMount:s=o.forceMount,...r}=e,a=d(D,e.__scopeDialog);return a.modal?n.jsx(C,{present:s||a.open,children:n.jsx(ie,{...r,ref:t})}):null});A.displayName=D;var ie=i.forwardRef((e,t)=>{const{__scopeDialog:o,...s}=e,r=d(D,o);return n.jsx(X,{as:Y,allowPinchZoom:!0,shards:[r.contentRef],children:n.jsx(x.div,{"data-state":j(r.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),f="DialogContent",T=i.forwardRef((e,t)=>{const o=I(f,e.__scopeDialog),{forceMount:s=o.forceMount,...r}=e,a=d(f,e.__scopeDialog);return n.jsx(C,{present:s||a.open,children:a.modal?n.jsx(le,{...r,ref:t}):n.jsx(ce,{...r,ref:t})})});T.displayName=f;var le=i.forwardRef((e,t)=>{const o=d(f,e.__scopeDialog),s=i.useRef(null),r=y(t,o.contentRef,s);return i.useEffect(()=>{const a=s.current;if(a)return Z(a)},[]),n.jsx(M,{...e,ref:r,trapFocus:o.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:m(e.onCloseAutoFocus,a=>{var l;a.preventDefault(),(l=o.triggerRef.current)==null||l.focus()}),onPointerDownOutside:m(e.onPointerDownOutside,a=>{const l=a.detail.originalEvent,c=l.button===0&&l.ctrlKey===!0;(l.button===2||c)&&a.preventDefault()}),onFocusOutside:m(e.onFocusOutside,a=>a.preventDefault())})}),ce=i.forwardRef((e,t)=>{const o=d(f,e.__scopeDialog),s=i.useRef(!1),r=i.useRef(!1);return n.jsx(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var l,c;(l=e.onCloseAutoFocus)==null||l.call(e,a),a.defaultPrevented||(s.current||(c=o.triggerRef.current)==null||c.focus(),a.preventDefault()),s.current=!1,r.current=!1},onInteractOutside:a=>{var u,g;(u=e.onInteractOutside)==null||u.call(e,a),a.defaultPrevented||(s.current=!0,a.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const l=a.target;((g=o.triggerRef.current)==null?void 0:g.contains(l))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&r.current&&a.preventDefault()}})}),M=i.forwardRef((e,t)=>{const{__scopeDialog:o,trapFocus:s,onOpenAutoFocus:r,onCloseAutoFocus:a,...l}=e,c=d(f,o),u=i.useRef(null),g=y(t,u);return J(),n.jsxs(n.Fragment,{children:[n.jsx(Q,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:r,onUnmountAutoFocus:a,children:n.jsx(ee,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":j(c.open),...l,ref:g,onDismiss:()=>c.onOpenChange(!1)})}),n.jsxs(n.Fragment,{children:[n.jsx(de,{titleId:c.titleId}),n.jsx(fe,{contentRef:u,descriptionId:c.descriptionId})]})]})}),_="DialogTitle",F=i.forwardRef((e,t)=>{const{__scopeDialog:o,...s}=e,r=d(_,o);return n.jsx(x.h2,{id:r.titleId,...s,ref:t})});F.displayName=_;var S="DialogDescription",W=i.forwardRef((e,t)=>{const{__scopeDialog:o,...s}=e,r=d(S,o);return n.jsx(x.p,{id:r.descriptionId,...s,ref:t})});W.displayName=S;var k="DialogClose",$=i.forwardRef((e,t)=>{const{__scopeDialog:o,...s}=e,r=d(k,o);return n.jsx(x.button,{type:"button",...s,ref:t,onClick:m(e.onClick,()=>r.onOpenChange(!1))})});$.displayName=k;function j(e){return e?"open":"closed"}var G="DialogTitleWarning",[_e,L]=te(G,{contentName:f,titleName:_,docsSlug:"dialog"}),de=({titleId:e})=>{const t=L(G),o=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return i.useEffect(()=>{e&&(document.getElementById(e)||console.error(o))},[o,e]),null},ue="DialogDescriptionWarning",fe=({contentRef:e,descriptionId:t})=>{const s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${L(ue).contentName}}.`;return i.useEffect(()=>{var a;const r=(a=e.current)==null?void 0:a.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(s))},[s,e,t]),null},ge=b,je=O,pe=w,z=A,B=T,H=F,q=W,me=$;const Ee=ge,xe=pe,V=i.forwardRef(({className:e,...t},o)=>n.jsx(z,{ref:o,className:p("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));V.displayName=z.displayName;const De=i.forwardRef(({className:e,children:t,...o},s)=>n.jsxs(xe,{children:[n.jsx(V,{}),n.jsxs(B,{ref:s,className:p("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...o,children:[t,n.jsxs(me,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[n.jsx(ne,{className:"h-4 w-4"}),n.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));De.displayName=B.displayName;const ve=({className:e,...t})=>n.jsx("div",{className:p("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});ve.displayName="DialogHeader";const Ne=({className:e,...t})=>n.jsx("div",{className:p("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});Ne.displayName="DialogFooter";const ye=i.forwardRef(({className:e,...t},o)=>n.jsx(H,{ref:o,className:p("text-lg font-semibold leading-none tracking-tight",e),...t}));ye.displayName=H.displayName;const Ce=i.forwardRef(({className:e,...t},o)=>n.jsx(q,{ref:o,className:p("text-sm text-muted-foreground",e),...t}));Ce.displayName=q.displayName;export{B as C,Ee as D,z as O,pe as P,ge as R,je as T,_e as W,De as a,ve as b,ye as c,Ce as d,Ne as e,he as f,H as g,q as h,me as i};
