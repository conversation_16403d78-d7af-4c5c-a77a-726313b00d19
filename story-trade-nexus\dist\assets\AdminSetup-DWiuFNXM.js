import{j as e}from"./chunk-CpdlqjqK.js";import{r as s,L as r}from"./chunk-b0HHmiEU.js";import{z as t,H as a,j as n,c as l,r as c}from"./index-DwRq5Uwb.js";import{J as i}from"./chunk-Cw96wKwP.js";import{q as o,w as m,R as d}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const x=()=>{const[x,u]=s.useState(!1),[j,h]=s.useState(!1),[p,f]=s.useState(null),[g,y]=s.useState(""),[b,N]=s.useState([]);s.useEffect((()=>{v()}),[]),s.useEffect((()=>{const e=console.log,s=console.error;return console.log=(...s)=>{e(...s),N((e=>[...e,`LOG: ${s.map((e=>String(e))).join(" ")}`]))},console.error=(...e)=>{s(...e),N((s=>[...s,`ERROR: ${e.map((e=>String(e))).join(" ")}`]))},()=>{console.log=e,console.error=s}}),[]);const v=async()=>{if(!x)try{u(!0),f(null),y(""),N([]);const e=await t();e.success?(h(!0),y(e.message),i.success("Admin user set up successfully!")):(f(e.message),i.error("Failed to set up admin"))}catch(e){f(`Failed to set up admin: ${e instanceof Error?e.message:"Unknown error"}`),i.error("Failed to set up admin")}finally{u(!1)}};return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(a,{}),e.jsx("main",{className:"flex-grow flex items-center justify-center py-8",children:e.jsxs("div",{className:"max-w-3xl w-full mx-auto p-8 bg-white rounded-lg shadow-lg",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx(o,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Setup"}),e.jsx("p",{className:"text-gray-600",children:"<NAME_EMAIL> as an admin user."})]}),p&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{className:"font-semibold",children:"Error:"}),e.jsx("p",{children:p})]}),j&&e.jsxs("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6 flex items-center justify-center",children:[e.jsx(m,{className:"h-5 w-5 mr-2"}),e.jsx("p",{children:g||"Admin user set up successfully!"})]}),x?e.jsxs("div",{className:"flex justify-center items-center py-4 mb-6",children:[e.jsx(n,{size:"md"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Setting up admin user..."})]}):e.jsx("div",{className:"flex justify-center mb-6",children:e.jsxs(l,{onClick:v,className:"flex items-center",children:[e.jsx(d,{className:"h-4 w-4 mr-2"}),j?"Try Again":"Set Up Admin"]})}),e.jsxs("div",{className:"mt-8",children:[e.jsx("h2",{className:"text-lg font-semibold mb-2",children:"Process Logs:"}),e.jsx("div",{className:"bg-gray-50 border border-gray-200 rounded p-4 max-h-60 overflow-y-auto text-sm font-mono",children:b.length>0?b.map(((s,r)=>e.jsx("div",{className:"py-1 "+(s.startsWith("ERROR")?"text-red-600":"text-gray-700"),children:s},r))):e.jsx("p",{className:"text-gray-500 italic",children:"No logs available yet..."})})]}),e.jsxs("div",{className:"flex justify-center mt-8",children:[e.jsx(r,{to:"/",children:e.jsx(l,{variant:"outline",className:"mr-4",children:"Return to Home"})}),e.jsx(r,{to:"/admin",children:e.jsx(l,{children:"Go to Admin Dashboard"})})]})]})}),e.jsx(c,{})]})};export{x as default};
