const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-ehpEbksy.js","assets/index.esm2017-H7c5Bkvh.js","assets/index-CdSwtf9D.js","assets/index-Cu1Q6uRi.css"])))=>i.map(i=>d[i]);
import{f as ne,aU as le,z as y,A as ce,r as C,u as de,o as ue,j as e,H as me,p as ge,q as j,s as v,v as w,w as N,I as q,x as I,U as O,X as pe,ae as he,a as Y,aV as xe,G as fe,J as k,y as be,_ as Q,K as ye}from"./index-CdSwtf9D.js";import{T as je}from"./textarea-BLV1TdQF.js";import{ref as ve,uploadBytes as we,getDownloadURL as Ne}from"./index.esm-BEM7nnuS.js";import{getCurrentPosition as ke}from"./geolocationUtils-DGx5pdrF.js";import{C as Se}from"./circle-alert-DarIGqXM.js";import"./index.esm2017-H7c5Bkvh.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=ne("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),E={maxWidth:1200,maxHeight:1200,webpQuality:.85,jpegQuality:.85,maxFileSize:5*1024*1024,targetFileSize:200*1024,supportedFormats:["image/jpeg","image/jpg","image/png","image/webp"],outputFormat:"webp",fallbackFormat:"jpeg"};function Ee(){return new Promise(t=>{const r=new Image;r.onload=r.onerror=()=>{t(r.height===2)},r.src="data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA"})}function J(t){return E.supportedFormats.includes(t.type)?t.size>E.maxFileSize?{valid:!1,error:`File size too large. Maximum size: ${E.maxFileSize/1024/1024}MB`}:{valid:!0}:{valid:!1,error:`Unsupported file format. Supported formats: ${E.supportedFormats.join(", ")}`}}function Fe(t,r){const s=document.createElement("canvas");return s.width=t,s.height=r,s}function Ae(t,r,s=E.maxWidth,i=E.maxHeight){let{width:d,height:c}={width:t,height:r};const p=s/d,u=i/c,n=Math.min(p,u,1);return d=Math.round(d*n),c=Math.round(c*n),{width:d,height:c}}function Ue(t){return new Promise((r,s)=>{const i=new Image;i.onload=()=>r(i),i.onerror=()=>s(new Error("Failed to load image")),i.src=URL.createObjectURL(t)})}function V(t,r="webp",s=.85){return new Promise((i,d)=>{t.toBlob(c=>{c?i(c):d(new Error("Failed to convert canvas to blob"))},`image/${r}`,s)})}async function Pe(t,r={}){const s=J(t);if(!s.valid)throw new Error(s.error);const{maxWidth:i=E.maxWidth,maxHeight:d=E.maxHeight,quality:c=E.webpQuality,format:p=await Ee()?"webp":E.fallbackFormat,targetSize:u=E.targetFileSize}=r;try{const n=await Ue(t),{width:l,height:F}=Ae(n.naturalWidth,n.naturalHeight,i,d),$=Fe(l,F),A=$.getContext("2d");if(!A)throw new Error("Failed to get canvas context");A.imageSmoothingEnabled=!0,A.imageSmoothingQuality="high",A.drawImage(n,0,0,l,F),URL.revokeObjectURL(n.src);let z=await V($,p,c),B=c;for(;z.size>u&&B>.3;)B-=.1,z=await V($,p,B);const G=new File([z],`${t.name.split(".")[0]}.${p}`,{type:`image/${p}`});return{file:G,originalSize:t.size,processedSize:G.size,format:p}}catch(n){throw console.error("Image processing failed:",n),new Error(`Image processing failed: ${n instanceof Error?n.message:"Unknown error"}`)}}const L="https://via.placeholder.com/150?text=No+Image",Re=async(t,r,s,i={})=>{try{if(console.log(`Uploading image to path: ${r}`),console.log(`Image file details: name=${t.name}, size=${t.size}, type=${t.type}`),!t)return console.error("No image file provided"),L;if(!r)return console.error("No path provided"),L;const d=J(t);if(!d.valid)return console.error("Image validation failed:",d.error),L;let c=t,p=r;if(!i.skipProcessing)try{console.log("Processing image for optimization..."),s&&s(10);const l=await Pe(t,{maxWidth:i.maxWidth,maxHeight:i.maxHeight,quality:i.quality});c=l.file,l.format!==t.type.split("/")[1]&&(p=r.replace(/\.[^.]+$/,`.${l.format}`)),console.log(`Image processed: ${t.size} bytes -> ${l.file.size} bytes (${l.format})`),s&&s(30)}catch(l){console.warn("Image processing failed, uploading original:",l)}const u=ve(le,p);console.log(`Storage reference created: ${u.fullPath}`);const n={contentType:c.type,customMetadata:{originalName:t.name,originalSize:t.size.toString(),processedSize:c.size.toString(),uploadedAt:new Date().toISOString()}};console.log("Metadata:",n),console.log("Starting file upload..."),s&&s(50);try{const l=await we(u,c,n);console.log("Image uploaded successfully"),console.log("Upload snapshot:",{bytesTransferred:l.bytesTransferred,totalBytes:l.totalBytes,state:l.state}),s&&s(90),console.log("Getting download URL...");const F=await Ne(l.ref);return console.log("Image download URL:",F),s&&(console.log("Calling progress callback with 100%"),s(100)),F}catch(l){throw console.error("Error in uploadBytes:",l),l instanceof Error&&console.error("Upload error details:",{message:l.message,stack:l.stack}),l}}catch(d){return console.error("Error uploading image:",d),d instanceof Error&&console.error("Error details:",{message:d.message,stack:d.stack}),L}},Ce=async(t,r,s)=>{try{if(console.log(`Uploading ${t.length} images`),console.log("Image files:",t.map(n=>({name:n.name,size:n.size,type:n.type}))),console.log("User ID:",r),!t||t.length===0)return console.error("No image files provided"),[L];if(!r)return console.error("No user ID provided"),[L];const i=[],d=t.length;let c=0;for(const n of t){console.log(`Processing file: ${n.name} (${n.size} bytes)`);const l=Le(r,n.name);console.log(`Generated path: ${l}`);const F=Re(n,l,$=>{if(console.log(`File ${n.name} progress: ${$}%`),$===100&&(c++,s)){const A=Math.round(c/d*100);console.log(`Overall progress: ${A}%`),s(A)}});i.push(F)}console.log(`Created ${i.length} upload promises`);const p=await Promise.all(i);console.log("All uploads completed. Results:",p);const u=p.filter(n=>n!==L);return console.log(`Successful uploads: ${u.length}/${p.length}`),u.length===0&&p.length>0?(console.log("All uploads failed, returning default URL"),[L]):(console.log("Final URLs:",u),u)}catch(i){return console.error("Error uploading multiple images:",i),i instanceof Error&&console.error("Error details:",{message:i.message,stack:i.stack}),[L]}},Le=(t,r)=>{const s=new Date().getTime(),i=Math.random().toString(36).substring(2,6);return`book-images/${t}/${s}-${i}.webp`},$e=y.object({title:y.string().min(1,{message:"Book title is required"}),author:y.string().min(1,{message:"Author name is required"}),isbn:y.string().optional(),genre:y.string().min(1,{message:"Please select at least one genre"}),condition:y.string().min(1,{message:"Please select a condition"}),description:y.string().min(10,{message:"Description should be at least 10 characters"}),availability:y.string().min(1,{message:"Please select availability option"}),price:y.string().optional(),rentalPrice:y.string().optional(),rentalPeriod:y.string().optional(),securityDepositRequired:y.boolean().optional().default(!1),securityDepositAmount:y.string().optional()}),Te=()=>{const t=ce(),[r,s]=C.useState(!1);C.useState("");const[i,d]=C.useState([]),[c,p]=C.useState([]),[u,n]=C.useState(0),[l,F]=C.useState(0),[$,A]=C.useState(!1),[z,B]=C.useState(null),[G,X]=C.useState(null),{currentUser:f,userData:R}=de(),K=o=>{const a=o.target.files;if(!a)return;if(i.length+a.length>4){k.error("Maximum 4 images allowed");return}const b=Array.from(a),x=[...i,...b];d(x);const g=b.map(P=>URL.createObjectURL(P)),U=[...c,...g];p(U),i.length===0&&b.length>0&&n(0)},Z=o=>{const a=[...i],b=[...c];a.splice(o,1),b.splice(o,1),d(a),p(b),o===u?(a.length>0,n(0)):o<u&&n(u-1)},ee=o=>{n(o)},oe=async()=>{A(!0),B(null);try{console.log("Attempting to capture GPS location...");const o=await ke({enableHighAccuracy:!0,timeout:15e3,maximumAge:6e4});return console.log("GPS location captured successfully:",o),X(o),o}catch(o){console.error("Error capturing GPS location:",o);let a="Unable to get your current location.";return o instanceof Error&&(o.message.includes("permission")?a="Location permission denied. Using your registered address instead.":o.message.includes("timeout")?a="Location request timed out. Using your registered address instead.":o.message.includes("unavailable")&&(a="Location service unavailable. Using your registered address instead.")),B(a),null}finally{A(!1)}},te=async()=>{if(!(R!=null&&R.pincode))return console.log("No pincode available for fallback location"),null;try{const a={500001:{latitude:17.385,longitude:78.4867},500032:{latitude:17.4399,longitude:78.3489},500081:{latitude:17.4485,longitude:78.3908},400001:{latitude:18.9322,longitude:72.8264},400051:{latitude:19.0596,longitude:72.8295},110001:{latitude:28.6139,longitude:77.209},110016:{latitude:28.5494,longitude:77.2001},560001:{latitude:12.9716,longitude:77.5946},560066:{latitude:12.9698,longitude:77.75},600001:{latitude:13.0827,longitude:80.2707},600028:{latitude:13.0569,longitude:80.2091}}[R.pincode];if(a)return console.log("Fallback location from pincode mapping:",a),a;const b=R.pincode.substring(0,2),g={50:{latitude:17.385,longitude:78.4867},40:{latitude:19.076,longitude:72.8777},11:{latitude:28.7041,longitude:77.1025},56:{latitude:12.9716,longitude:77.5946},60:{latitude:13.0827,longitude:80.2707},70:{latitude:22.5726,longitude:88.3639},30:{latitude:26.9124,longitude:75.7873},22:{latitude:26.8467,longitude:80.9462}}[b];return g?(console.log("Fallback location from state mapping:",g),g):(console.log("No fallback location available for pincode:",R.pincode),null)}catch(o){return console.error("Error getting fallback location from pincode:",o),null}},re=async()=>{if(!(f!=null&&f.uid))return console.log("No current user available for community retrieval"),null;try{console.log("Retrieving user community from Firestore..."),await ye();const{doc:o,getDoc:a,getFirestore:b}=await Q(async()=>{const{doc:P,getDoc:S,getFirestore:M}=await import("./index.esm-ehpEbksy.js");return{doc:P,getDoc:S,getFirestore:M}},__vite__mapDeps([0,1])),x=b(),g=o(x,"users",f.uid),U=await a(g);if(U.exists()){const S=U.data().community;return S&&typeof S=="string"&&S.trim()!==""?(console.log("User community retrieved successfully:",S),S.trim()):(console.log("User community is empty or not set"),null)}else return console.log("User document not found in Firestore"),null}catch(o){return console.error("Error retrieving user community:",o),null}},m=ue({resolver:be($e),defaultValues:{title:"",author:"",isbn:"",genre:"",condition:"",description:"",availability:"",price:"",rentalPrice:"",rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:""}}),ie=async o=>{var a,b;s(!0);try{console.log("Adding book:",o);const{createBook:x}=await Q(async()=>{const{createBook:h}=await import("./index-CdSwtf9D.js").then(ae=>ae.bd);return{createBook:h}},__vite__mapDeps([2,3]));if(!f){k.error("You must be signed in to add a book"),s(!1);return}console.log("Capturing location for book listing...");let g=null;g=await oe(),g||(console.log("GPS capture failed, trying fallback location..."),g=await te()),g?console.log("Location captured for book:",g):console.log("No location could be determined for book"),console.log("Retrieving user community for book listing...");let U=null;try{U=await re(),U?(console.log("✅ User community retrieved successfully for book:",U),k.info(`Community "${U}" will be added to your book listing`)):(console.warn("⚠️ No community information available for user - book will be created without community data"),k.warning("No community information found in your profile. Consider updating your profile to help others find books in your area."))}catch(h){console.error("❌ Error retrieving user community:",h),console.error("Community error details:",{message:h.message,stack:h.stack,currentUser:f==null?void 0:f.uid,timestamp:new Date().toISOString()}),k.error("Failed to retrieve community information. Your book will be listed without community data.")}let P=[];try{P=o.genre.split(",").map(h=>h.trim()).filter(h=>h.length>0),P.length===0&&(P=[o.genre.trim()])}catch(h){console.error("Error processing genre:",h),P=[o.genre.trim()]}let S=null;if(o.price&&(S=Number(o.price),isNaN(S))){k.error("Invalid price value. Please enter a valid number."),s(!1);return}let M=null;if(o.rentalPrice&&(M=Number(o.rentalPrice),isNaN(M))){k.error("Invalid rental price value. Please enter a valid number."),s(!1);return}let _=o.rentalPeriod;M||(_=null);let T=null;if(o.securityDepositRequired&&o.securityDepositAmount&&(T=Number(o.securityDepositAmount),isNaN(T))){k.error("Invalid security deposit amount. Please enter a valid number."),s(!1);return}let D=[],H="https://via.placeholder.com/150?text=No+Image";if(i.length>0)try{k.info("Uploading images..."),D=await Ce(i,f.uid,h=>{F(h),console.log(`Upload progress: ${h}%`)}),console.log("Uploaded image URLs:",D),D.length>0&&(H=D[u]||D[0])}catch(h){console.error("Error uploading images:",h),k.error("Failed to upload images. Using default image instead.")}const W={title:o.title.trim(),author:o.author.trim(),isbn:((a=o.isbn)==null?void 0:a.trim())||null,genre:P,condition:o.condition,description:o.description.trim(),imageUrl:H,imageUrls:D.length>0?D:void 0,displayImageIndex:D.length>0?u:void 0,availability:o.availability,price:S,rentalPrice:M,rentalPeriod:_,securityDepositRequired:o.securityDepositRequired,securityDepositAmount:T,ownerId:f.uid,ownerName:f.displayName||((b=f.email)==null?void 0:b.split("@")[0])||"Unknown",ownerEmail:f.email||void 0,ownerCommunity:U||void 0,ownerCoordinates:g,ownerPincode:(R==null?void 0:R.pincode)||void 0,ownerRating:0,perceivedValue:5};console.log("Prepared book data:",W);const se=await x(W);console.log("Book created successfully with ID:",se),k.success("Book added successfully! It will be visible after admin approval."),t("/browse")}catch(x){console.error("Error adding book:",x);let g="Failed to add book. Please try again.";x instanceof Error&&(g=`Error: ${x.message}`,console.error("Error details:",x.message),x.message.includes("permission-denied")?g="You don't have permission to add books. Please check your account.":x.message.includes("network")?g="Network error. Please check your internet connection and try again.":x.message.includes("quota-exceeded")&&(g="Database quota exceeded. Please try again later.")),k.error(g)}finally{s(!1),F(0)}};return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(me,{}),e.jsx("main",{className:"flex-grow",children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-2xl",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Add Your Book"}),e.jsx("p",{className:"text-gray-600",children:"Share your book with the community"})]}),e.jsx(ge,{...m,children:e.jsxs("form",{onSubmit:m.handleSubmit(ie),className:"space-y-6",children:[e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-6",children:[e.jsx(j,{control:m.control,name:"title",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Book Title*"}),e.jsx(N,{children:e.jsx(q,{placeholder:"Enter book title",disabled:r,...o})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"author",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Author*"}),e.jsx(N,{children:e.jsx(q,{placeholder:"Enter author name",disabled:r,...o})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"isbn",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"ISBN (Optional)"}),e.jsx(N,{children:e.jsx(q,{placeholder:"Enter ISBN",disabled:r,...o})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"genre",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Genre*"}),e.jsx(N,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:r,...o,children:[e.jsx("option",{value:"",children:"Select Genre"}),e.jsx("option",{value:"Fiction",children:"Fiction"}),e.jsx("option",{value:"Non-Fiction",children:"Non-Fiction"}),e.jsx("option",{value:"Classics",children:"Classics"}),e.jsx("option",{value:"Fantasy",children:"Fantasy"}),e.jsx("option",{value:"Mystery",children:"Mystery"}),e.jsx("option",{value:"Romance",children:"Romance"}),e.jsx("option",{value:"Science Fiction",children:"Science Fiction"}),e.jsx("option",{value:"History",children:"History"}),e.jsx("option",{value:"Comic",children:"Comic"})]})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"condition",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Condition*"}),e.jsx(N,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:r,...o,children:[e.jsx("option",{value:"",children:"Select Condition"}),e.jsx("option",{value:"New",children:"New"}),e.jsx("option",{value:"Like New",children:"Like New"}),e.jsx("option",{value:"Good",children:"Good"}),e.jsx("option",{value:"Fair",children:"Fair"})]})}),e.jsx(I,{})]})})]}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6",children:[e.jsxs("div",{className:"flex flex-col items-center justify-center mb-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-700 mb-2",children:"Book Images"}),e.jsx("p",{className:"text-sm text-gray-500 mb-2 text-center",children:"Upload up to 4 images of your book. The first image will be the display image."}),e.jsxs("div",{className:"flex items-center justify-center mb-2",children:[e.jsxs("span",{className:"text-sm font-medium text-gray-700 mr-2",children:[i.length,"/4 images"]}),l>0&&l<100&&e.jsx("div",{className:"w-24 h-2 bg-gray-200 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-burgundy-500",style:{width:`${l}%`}})})]})]}),c.length>0&&e.jsx("div",{className:"grid grid-cols-2 gap-4 mb-4",children:c.map((o,a)=>e.jsxs("div",{className:`relative border rounded-md overflow-hidden ${a===u?"ring-2 ring-burgundy-500":""}`,children:[e.jsx("img",{src:o,alt:`Book image ${a+1}`,className:"w-full h-32 object-contain"}),e.jsxs("div",{className:"absolute top-0 right-0 p-1 flex space-x-1",children:[a!==u&&e.jsx("button",{type:"button",className:"bg-burgundy-500 text-white p-1 rounded-full hover:bg-burgundy-600",onClick:()=>ee(a),title:"Set as display image",children:e.jsx(O,{className:"h-4 w-4"})}),e.jsx("button",{type:"button",className:"bg-gray-700 text-white p-1 rounded-full hover:bg-gray-800",onClick:()=>Z(a),title:"Remove image",children:e.jsx(pe,{className:"h-4 w-4"})})]}),a===u&&e.jsx("div",{className:"absolute bottom-0 left-0 right-0 bg-burgundy-500 text-white text-xs py-1 text-center",children:"Display Image"})]},a))}),i.length<4&&e.jsxs("div",{className:"flex justify-center",children:[e.jsx("input",{type:"file",id:"image-upload",className:"hidden",accept:"image/*",multiple:i.length<3,onChange:K,disabled:r}),e.jsxs("label",{htmlFor:"image-upload",className:`flex items-center justify-center text-sm bg-burgundy-500 text-white px-4 py-2 rounded-md cursor-pointer hover:bg-burgundy-600 ${r?"opacity-50 cursor-not-allowed":""}`,children:[e.jsx(Ie,{className:"h-4 w-4 mr-2"}),i.length===0?"Upload Images":"Add More Images"]})]})]}),e.jsx(j,{control:m.control,name:"availability",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Availability*"}),e.jsx(N,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:r,onChange:a=>{o.onChange(a)},value:o.value,children:[e.jsx("option",{value:"",children:"Select Availability"}),e.jsx("option",{value:"For Exchange",children:"For Exchange"}),e.jsx("option",{value:"For Sale",children:"For Sale"}),e.jsx("option",{value:"For Rent",children:"For Rent"}),e.jsx("option",{value:"For Sale & Exchange",children:"For Sale & Exchange"}),e.jsx("option",{value:"For Rent & Exchange",children:"For Rent & Exchange"}),e.jsx("option",{value:"For Rent & Sale",children:"For Rent & Sale"}),e.jsx("option",{value:"For Rent, Sale & Exchange",children:"For Rent, Sale & Exchange"})]})}),e.jsx(I,{})]})}),m.watch("availability")&&(m.watch("availability").includes("Sale")||m.watch("availability").includes("Rent"))&&e.jsxs("div",{className:"space-y-6",children:[m.watch("availability").includes("Sale")&&e.jsx(j,{control:m.control,name:"price",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Sale Price (₹)"}),e.jsx(N,{children:e.jsx(q,{type:"number",placeholder:"Enter price",disabled:r,...o})}),e.jsx(I,{})]})}),m.watch("availability").includes("Rent")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx(j,{control:m.control,name:"rentalPrice",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Rental Price (₹)"}),e.jsx(N,{children:e.jsx(q,{type:"number",placeholder:"Enter rental price",disabled:r,...o})}),e.jsx(I,{})]})}),e.jsx(j,{control:m.control,name:"rentalPeriod",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Period"}),e.jsx(N,{children:e.jsxs("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",disabled:r,...o,children:[e.jsx("option",{value:"per day",children:"Per Day"}),e.jsx("option",{value:"per week",children:"Per Week"}),e.jsx("option",{value:"per month",children:"Per Month"})]})}),e.jsx(I,{})]})})]}),e.jsx("div",{className:"mt-4",children:e.jsx(j,{control:m.control,name:"securityDepositRequired",render:({field:o})=>e.jsxs(v,{className:"flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4",children:[e.jsx(N,{children:e.jsx("input",{type:"checkbox",className:"h-4 w-4 mt-1",checked:o.value,onChange:a=>o.onChange(a.target.checked),disabled:r})}),e.jsxs("div",{className:"space-y-1 leading-none",children:[e.jsx(w,{children:"Security Deposit Required"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Require a security deposit for renting this book"})]})]})})}),m.watch("securityDepositRequired")&&e.jsx("div",{className:"mt-4",children:e.jsx(j,{control:m.control,name:"securityDepositAmount",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Security Deposit Amount (₹)"}),e.jsx(N,{children:e.jsx(q,{type:"number",placeholder:"Enter security deposit amount",disabled:r,...o})}),e.jsx(I,{})]})})})]})]})]})]}),e.jsx(j,{control:m.control,name:"description",render:({field:o})=>e.jsxs(v,{children:[e.jsx(w,{children:"Description*"}),e.jsx(N,{children:e.jsx(je,{placeholder:"Describe the book, its condition, and any other details...",className:"min-h-[120px]",disabled:r,...o})}),e.jsx(I,{})]})}),e.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(he,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h3",{className:"text-sm font-medium text-blue-900 mb-2",children:"Location Information"}),e.jsx("p",{className:"text-sm text-blue-800 mb-2",children:"When you submit this book listing, we will automatically capture your current GPS location to help other users find books near them. This location data will be used solely for:"}),e.jsxs("ul",{className:"text-sm text-blue-800 list-disc list-inside space-y-1 mb-3",children:[e.jsx("li",{children:"Calculating and displaying distance to other users browsing books"}),e.jsx("li",{children:"Helping users find books in their local area"}),e.jsx("li",{children:"Improving the book discovery experience"})]}),e.jsx("p",{className:"text-sm text-blue-800",children:"If GPS location cannot be accessed, we'll use your registered address as a fallback. Your exact location will not be displayed to other users - only the calculated distance."}),$&&e.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-blue-700",children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),e.jsx("span",{children:"Capturing your location..."})]}),G&&e.jsxs("div",{className:"mt-3 flex items-center space-x-2 text-sm text-green-700",children:[e.jsx(O,{className:"h-4 w-4 text-green-600"}),e.jsx("span",{children:"Location captured successfully"})]}),z&&e.jsxs("div",{className:"mt-3 flex items-start space-x-2 text-sm text-amber-700",children:[e.jsx(Se,{className:"h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0"}),e.jsx("span",{children:z})]})]})]})}),e.jsxs("div",{className:"flex justify-end space-x-4",children:[e.jsx(Y,{type:"button",variant:"outline",disabled:r,onClick:()=>t("/"),children:"Cancel"}),e.jsxs(Y,{type:"submit",disabled:r,className:"flex items-center gap-2",children:[e.jsx(xe,{className:"h-4 w-4"}),"Add Book"]})]})]})})]})})}),e.jsx(fe,{})]})};export{Te as default};
