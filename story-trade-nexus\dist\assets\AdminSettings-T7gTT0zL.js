import{an as _,r as c,ao as ce,az as q,j as e,ap as x,aq as y,aR as le,a1 as b,b4 as U,aO as de,aA as ue,b5 as me,b6 as fe,ar as he,h as ge,Y as pe,aS as g,I as N,J as M}from"./index-BsSeXuDn.js";import{A as xe}from"./AdminLayout-BrYjZikF.js";import{u as be}from"./index-R-QTsfUa.js";import{C as S,a as E,b as R,d as T,c as A}from"./card-DS9vE5tt.js";import"./users-C6U-K-0N.js";var I="Switch",[ve,Be]=_(I),[je,Ne]=ve(I),O=c.forwardRef((a,i)=>{const{__scopeSwitch:t,name:o,checked:r,defaultChecked:n,required:s,disabled:l,value:m="on",onCheckedChange:f,form:u,...d}=a,[h,ie]=c.useState(null),ne=ce(i,j=>ie(j)),C=c.useRef(!1),F=h?u||!!h.closest("form"):!0,[v=!1,oe]=q({prop:r,defaultProp:n,onChange:f});return e.jsxs(je,{scope:t,checked:v,disabled:l,children:[e.jsx(x.button,{type:"button",role:"switch","aria-checked":v,"aria-required":s,"data-state":D(v),"data-disabled":l?"":void 0,disabled:l,value:m,...d,ref:ne,onClick:y(a.onClick,j=>{oe(re=>!re),F&&(C.current=j.isPropagationStopped(),C.current||j.stopPropagation())})}),F&&e.jsx(ye,{control:h,bubbles:!C.current,name:o,value:m,checked:v,required:s,disabled:l,form:u,style:{transform:"translateX(-100%)"}})]})});O.displayName=I;var V="SwitchThumb",$=c.forwardRef((a,i)=>{const{__scopeSwitch:t,...o}=a,r=Ne(V,t);return e.jsx(x.span,{"data-state":D(r.checked),"data-disabled":r.disabled?"":void 0,...o,ref:i})});$.displayName=V;var ye=a=>{const{control:i,checked:t,bubbles:o=!0,...r}=a,n=c.useRef(null),s=be(t),l=le(i);return c.useEffect(()=>{const m=n.current,f=window.HTMLInputElement.prototype,d=Object.getOwnPropertyDescriptor(f,"checked").set;if(s!==t&&d){const h=new Event("click",{bubbles:o});d.call(m,t),m.dispatchEvent(h)}},[s,t,o]),e.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...r,tabIndex:-1,ref:n,style:{...a.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function D(a){return a?"checked":"unchecked"}var G=O,ke=$;const p=c.forwardRef(({className:a,...i},t)=>e.jsx(G,{className:b("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",a),...i,ref:t,children:e.jsx(ke,{className:b("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));p.displayName=G.displayName;var P="Tabs",[we,Fe]=_(P,[U]),L=U(),[Ce,B]=we(P),z=c.forwardRef((a,i)=>{const{__scopeTabs:t,value:o,onValueChange:r,defaultValue:n,orientation:s="horizontal",dir:l,activationMode:m="automatic",...f}=a,u=de(l),[d,h]=q({prop:o,onChange:r,defaultProp:n});return e.jsx(Ce,{scope:t,baseId:ue(),value:d,onValueChange:h,orientation:s,dir:u,activationMode:m,children:e.jsx(x.div,{dir:u,"data-orientation":s,...f,ref:i})})});z.displayName=P;var H="TabsList",K=c.forwardRef((a,i)=>{const{__scopeTabs:t,loop:o=!0,...r}=a,n=B(H,t),s=L(t);return e.jsx(me,{asChild:!0,...s,orientation:n.orientation,dir:n.dir,loop:o,children:e.jsx(x.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:i})})});K.displayName=H;var J="TabsTrigger",W=c.forwardRef((a,i)=>{const{__scopeTabs:t,value:o,disabled:r=!1,...n}=a,s=B(J,t),l=L(t),m=Q(s.baseId,o),f=Z(s.baseId,o),u=o===s.value;return e.jsx(fe,{asChild:!0,...l,focusable:!r,active:u,children:e.jsx(x.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":f,"data-state":u?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:m,...n,ref:i,onMouseDown:y(a.onMouseDown,d=>{!r&&d.button===0&&d.ctrlKey===!1?s.onValueChange(o):d.preventDefault()}),onKeyDown:y(a.onKeyDown,d=>{[" ","Enter"].includes(d.key)&&s.onValueChange(o)}),onFocus:y(a.onFocus,()=>{const d=s.activationMode!=="manual";!u&&!r&&d&&s.onValueChange(o)})})})});W.displayName=J;var X="TabsContent",Y=c.forwardRef((a,i)=>{const{__scopeTabs:t,value:o,forceMount:r,children:n,...s}=a,l=B(X,t),m=Q(l.baseId,o),f=Z(l.baseId,o),u=o===l.value,d=c.useRef(u);return c.useEffect(()=>{const h=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(h)},[]),e.jsx(he,{present:r||u,children:({present:h})=>e.jsx(x.div,{"data-state":u?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":m,hidden:!h,id:f,tabIndex:0,...s,ref:i,style:{...a.style,animationDuration:d.current?"0s":void 0},children:h&&n})})});Y.displayName=X;function Q(a,i){return`${a}-trigger-${i}`}function Z(a,i){return`${a}-content-${i}`}var Se=z,ee=K,se=W,te=Y;const Ee=Se,ae=c.forwardRef(({className:a,...i},t)=>e.jsx(ee,{ref:t,className:b("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...i}));ae.displayName=ee.displayName;const k=c.forwardRef(({className:a,...i},t)=>e.jsx(se,{ref:t,className:b("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...i}));k.displayName=se.displayName;const w=c.forwardRef(({className:a,...i},t)=>e.jsx(te,{ref:t,className:b("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...i}));w.displayName=te.displayName;const Me=()=>{const[a,i]=c.useState(!1);c.useState("general");const[t,o]=c.useState({general:{siteName:"Book Sharing Platform",contactEmail:"<EMAIL>",enableRegistration:!0,requireEmailVerification:!0},books:{requireApproval:!0,maxBooksPerUser:50,allowMultipleImages:!0,defaultBookAvailability:"Available"},notifications:{enableEmailNotifications:!0,notifyOnNewUser:!0,notifyOnBookSubmission:!0,adminEmailRecipients:"<EMAIL>"}}),r=async()=>{try{i(!0),await new Promise(s=>setTimeout(s,1e3)),M.success("Settings saved successfully!")}catch(s){console.error("Error saving settings:",s),M.error("Failed to save settings. Please try again.")}finally{i(!1)}},n=(s,l,m)=>{o(f=>({...f,[s]:{...f[s],[l]:m}}))};return e.jsxs(xe,{title:"Admin Settings",description:"Configure admin preferences and system settings",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Settings"}),e.jsx("p",{className:"text-gray-600",children:"Configure system settings and preferences"})]}),e.jsx(ge,{onClick:r,disabled:a,className:"mt-4 md:mt-0",children:a?e.jsxs(e.Fragment,{children:[e.jsx(pe,{size:"sm",className:"mr-2"}),"Saving..."]}):"Save Settings"})]}),e.jsxs(Ee,{defaultValue:"general",className:"w-full",children:[e.jsxs(ae,{className:"mb-6",children:[e.jsx(k,{value:"general",children:"General"}),e.jsx(k,{value:"books",children:"Books"}),e.jsx(k,{value:"notifications",children:"Notifications"})]}),e.jsx(w,{value:"general",children:e.jsxs(S,{children:[e.jsxs(E,{children:[e.jsx(R,{children:"General Settings"}),e.jsx(T,{children:"Configure general platform settings"})]}),e.jsxs(A,{className:"space-y-4",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(g,{htmlFor:"siteName",children:"Site Name"}),e.jsx(N,{id:"siteName",value:t.general.siteName,onChange:s=>n("general","siteName",s.target.value)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(g,{htmlFor:"contactEmail",children:"Contact Email"}),e.jsx(N,{id:"contactEmail",type:"email",value:t.general.contactEmail,onChange:s=>n("general","contactEmail",s.target.value)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(g,{htmlFor:"enableRegistration",children:"Enable User Registration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Allow new users to register on the platform"})]}),e.jsx(p,{id:"enableRegistration",checked:t.general.enableRegistration,onCheckedChange:s=>n("general","enableRegistration",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(g,{htmlFor:"requireEmailVerification",children:"Require Email Verification"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Users must verify their email before accessing the platform"})]}),e.jsx(p,{id:"requireEmailVerification",checked:t.general.requireEmailVerification,onCheckedChange:s=>n("general","requireEmailVerification",s)})]})]})]})}),e.jsx(w,{value:"books",children:e.jsxs(S,{children:[e.jsxs(E,{children:[e.jsx(R,{children:"Book Settings"}),e.jsx(T,{children:"Configure book-related settings"})]}),e.jsxs(A,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(g,{htmlFor:"requireApproval",children:"Require Book Approval"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"New book submissions require admin approval"})]}),e.jsx(p,{id:"requireApproval",checked:t.books.requireApproval,onCheckedChange:s=>n("books","requireApproval",s)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(g,{htmlFor:"maxBooksPerUser",children:"Maximum Books Per User"}),e.jsx(N,{id:"maxBooksPerUser",type:"number",value:t.books.maxBooksPerUser.toString(),onChange:s=>n("books","maxBooksPerUser",parseInt(s.target.value))})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(g,{htmlFor:"allowMultipleImages",children:"Allow Multiple Images"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Users can upload multiple images per book"})]}),e.jsx(p,{id:"allowMultipleImages",checked:t.books.allowMultipleImages,onCheckedChange:s=>n("books","allowMultipleImages",s)})]})]})]})}),e.jsx(w,{value:"notifications",children:e.jsxs(S,{children:[e.jsxs(E,{children:[e.jsx(R,{children:"Notification Settings"}),e.jsx(T,{children:"Configure email and system notifications"})]}),e.jsxs(A,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(g,{htmlFor:"enableEmailNotifications",children:"Enable Email Notifications"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send email notifications for important events"})]}),e.jsx(p,{id:"enableEmailNotifications",checked:t.notifications.enableEmailNotifications,onCheckedChange:s=>n("notifications","enableEmailNotifications",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(g,{htmlFor:"notifyOnNewUser",children:"Notify on New User Registration"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new user registers"})]}),e.jsx(p,{id:"notifyOnNewUser",checked:t.notifications.notifyOnNewUser,onCheckedChange:s=>n("notifications","notifyOnNewUser",s)})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsx(g,{htmlFor:"notifyOnBookSubmission",children:"Notify on Book Submission"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send notification when a new book is submitted"})]}),e.jsx(p,{id:"notifyOnBookSubmission",checked:t.notifications.notifyOnBookSubmission,onCheckedChange:s=>n("notifications","notifyOnBookSubmission",s)})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(g,{htmlFor:"adminEmailRecipients",children:"Admin Email Recipients"}),e.jsx(N,{id:"adminEmailRecipients",value:t.notifications.adminEmailRecipients,onChange:s=>n("notifications","adminEmailRecipients",s.target.value)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Separate multiple email addresses with commas"})]})]})]})})]})]})};export{Me as default};
