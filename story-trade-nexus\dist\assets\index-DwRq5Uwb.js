const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-B1ZgTNj8.js","assets/chunk-CpdlqjqK.js","assets/chunk-b0HHmiEU.js","assets/index.esm-CptSyBEO.js","assets/BookDetail-BTCE1QP9.js","assets/chunk-Cw96wKwP.js","assets/geolocationUtils-DEQwKbhP.js","assets/chunk-BGoCADfv.js","assets/chunk-BvIisuNF.js","assets/chunk-D1Z7_RhR.js","assets/chunk-DgNUPAoM.js","assets/MyBooks-Dos5Zzm6.js","assets/chunk-BpJWUrWd.js","assets/AddBooks-CR18lQrp.js","assets/chunk-DQKmWaJS.js","assets/BrowseBooks--qi3dfRY.js","assets/UserAccount-C57ZbS7H.js","assets/AdminDashboard-B0LoSn53.js","assets/chunk-BS5eevMn.js","assets/AdminBookApprovals-CWuE8Tls.js","assets/chunk-BjtUNXro.js","assets/AdminUsers-CoQc7clh.js","assets/AdminUtilities-CIRlSmt8.js","assets/AdminSettings-DmsLbfc7.js","assets/chunk-D25sxu8E.js","assets/AdminContactMessages-CN10jo9h.js","assets/chunk-jAw1jmB0.js","assets/ForgotPassword-hGqzlF5L.js","assets/VerifyEmail-BPCEGmGb.js","assets/HowItWorks-ChAd511g.js","assets/FAQ-Bli8LLd2.js","assets/ContactUs-BVUjPO1Z.js","assets/Feedback-CrK9zX1t.js","assets/chunk-JuOXOFq2.js","assets/PrivacyPolicy-CM8G7fgj.js","assets/Terms-W54UmMdW.js","assets/DataDeletion-CS_xpOM9.js","assets/SeedBooks-DzqWdzsT.js","assets/DatabaseBooks-UqhiqPl-.js","assets/AdminSetup-DWiuFNXM.js","assets/AdminDiagnostic-Ct7qQys2.js","assets/AdminFeedback-C29VQn79.js"])))=>i.map(i=>d[i]);
var e,a=Object.defineProperty,s=(e,s,t)=>((e,s,t)=>s in e?a(e,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[s]=t)(e,"symbol"!=typeof s?s+"":s,t);import{j as t,E as r,F as i,G as n,H as o,J as l,K as c,M as d,N as m,Q as u,U as h,W as x,X as p,Y as g,Z as f,$ as y,a0 as j,a1 as w,a2 as b,a3 as v,a4 as N,a5 as k,a6 as A,a7 as E,a8 as _,a9 as D,aa as P,ab as S,ac as C,ad as R,ae as I,af as L,ag as T,ah as V}from"./chunk-CpdlqjqK.js";import{a as B,r as O,R as z,L as U,c as F,N as M,u as K,B as q,f as H,g as G}from"./chunk-b0HHmiEU.js";import{t as $,a as W,c as J,T as Y,J as Q,z as Z}from"./chunk-Cw96wKwP.js";import{X,x as ee,M as ae,R as se,H as te,B as re,_ as ie,w as ne,a6 as oe,C as le,h as ce,a7 as de,c as me,a2 as ue,L as he,U as xe,d as pe,e as ge,l as fe,G as ye,W as je,a8 as we,a9 as be,aa as ve,S as Ne,F as ke,y as Ae,b as Ee,a0 as _e,i as De,A as Pe,ab as Se,ac as Ce,k as Re,N as Ie,ad as Le}from"./chunk-BGoCADfv.js";import{j as Te}from"./chunk-BvIisuNF.js";import{Q as Ve,a as Be}from"./chunk-D1Z7_RhR.js";import{F as Oe,C as ze,a as Ue,o as Fe,r as Me,b as Ke,u as qe,c as He}from"./chunk-DgNUPAoM.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))a(e);new MutationObserver((e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&a(e)})).observe(document,{childList:!0,subtree:!0})}function a(e){if(e.ep)return;e.ep=!0;const a=function(e){const a={};return e.integrity&&(a.integrity=e.integrity),e.referrerPolicy&&(a.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?a.credentials="include":"anonymous"===e.crossOrigin?a.credentials="omit":a.credentials="same-origin",a}(e);fetch(e.href,a)}}();var Ge=B;e=Ge.createRoot,Ge.hydrateRoot;const $e={},We=function(e,a,s){let t=Promise.resolve();if(a&&a.length>0){document.getElementsByTagName("link");const e=document.querySelector("meta[property=csp-nonce]"),s=(null==e?void 0:e.nonce)||(null==e?void 0:e.getAttribute("nonce"));t=Promise.allSettled(a.map((e=>{if((e=function(e){return"/"+e}(e))in $e)return;$e[e]=!0;const a=e.endsWith(".css"),t=a?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${t}`))return;const r=document.createElement("link");return r.rel=a?"stylesheet":"modulepreload",a||(r.as="script"),r.crossOrigin="",r.href=e,s&&r.setAttribute("nonce",s),document.head.appendChild(r),a?new Promise(((a,s)=>{r.addEventListener("load",a),r.addEventListener("error",(()=>s(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function r(e){const a=new Event("vite:preloadError",{cancelable:!0});if(a.payload=e,window.dispatchEvent(a),!a.defaultPrevented)throw e}return t.then((a=>{for(const e of a||[])"rejected"===e.status&&r(e.reason);return e().catch(r)}))};let Je=0;const Ye=new Map,Qe=e=>{if(Ye.has(e))return;const a=setTimeout((()=>{Ye.delete(e),aa({type:"REMOVE_TOAST",toastId:e})}),1e6);Ye.set(e,a)},Ze=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map((e=>e.id===a.toast.id?{...e,...a.toast}:e))};case"DISMISS_TOAST":{const{toastId:s}=a;return s?Qe(s):e.toasts.forEach((e=>{Qe(e.id)})),{...e,toasts:e.toasts.map((e=>e.id===s||void 0===s?{...e,open:!1}:e))}}case"REMOVE_TOAST":return void 0===a.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter((e=>e.id!==a.toastId))}}},Xe=[];let ea={toasts:[]};function aa(e){ea=Ze(ea,e),Xe.forEach((e=>{e(ea)}))}function sa({...e}){const a=(Je=(Je+1)%Number.MAX_SAFE_INTEGER,Je.toString()),s=()=>aa({type:"DISMISS_TOAST",toastId:a});return aa({type:"ADD_TOAST",toast:{...e,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>aa({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function ta(...e){return $(W(e))}const ra=d,ia=O.forwardRef((({className:e,...a},s)=>t.jsx(r,{ref:s,className:ta("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...a})));ia.displayName=r.displayName;const na=J("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),oa=O.forwardRef((({className:e,variant:a,...s},r)=>t.jsx(i,{ref:r,className:ta(na({variant:a}),e),...s})));oa.displayName=i.displayName;O.forwardRef((({className:e,...a},s)=>t.jsx(n,{ref:s,className:ta("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...a}))).displayName=n.displayName;const la=O.forwardRef((({className:e,...a},s)=>t.jsx(o,{ref:s,className:ta("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...a,children:t.jsx(X,{className:"h-4 w-4"})})));la.displayName=o.displayName;const ca=O.forwardRef((({className:e,...a},s)=>t.jsx(l,{ref:s,className:ta("text-sm font-semibold",e),...a})));ca.displayName=l.displayName;const da=O.forwardRef((({className:e,...a},s)=>t.jsx(c,{ref:s,className:ta("text-sm opacity-90",e),...a})));function ma(){const{toasts:e}=function(){const[e,a]=O.useState(ea);return O.useEffect((()=>(Xe.push(a),()=>{const e=Xe.indexOf(a);e>-1&&Xe.splice(e,1)})),[e]),{...e,toast:sa,dismiss:e=>aa({type:"DISMISS_TOAST",toastId:e})}}();return t.jsxs(ra,{children:[e.map((function({id:e,title:a,description:s,action:r,...i}){return t.jsxs(oa,{...i,children:[t.jsxs("div",{className:"grid gap-1",children:[a&&t.jsx(ca,{children:a}),s&&t.jsx(da,{children:s})]}),r,t.jsx(la,{})]},e)})),t.jsx(ia,{})]})}da.displayName=c.displayName;const ua=({...e})=>{const{theme:a="system"}=Te();return t.jsx(Y,{theme:a,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})},ha=u,xa=h,pa=x,ga=O.forwardRef((({className:e,sideOffset:a=4,...s},r)=>t.jsx(m,{ref:r,sideOffset:a,className:ta("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})));ga.displayName=m.displayName;const fa={apiKey:"AIzaSyAgXbuXyBEa9aEPVB4JtLugCbRWK39Vj_c",authDomain:"book-share-98f6a.firebaseapp.com",projectId:"book-share-98f6a",storageBucket:"book-share-98f6a.firebasestorage.app",messagingSenderId:"216941059965",appId:"1:216941059965:web:2e0528a8a018ff959c7614",measurementId:"G-NYSPR3K1PY"};let ya,ja,wa,ba,va=null;const Na=async()=>{try{const{initializeApp:e}=await We((async()=>{const{initializeApp:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.h));return{initializeApp:e}}),__vite__mapDeps([0,1,2])),{getAuth:a}=await We((async()=>{const{getAuth:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{getAuth:e}}),__vite__mapDeps([0,1,2])),{getFirestore:s}=await We((async()=>{const{getFirestore:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{getFirestore:e}}),__vite__mapDeps([0,1,2])),{getStorage:t}=await We((async()=>{const{getStorage:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.l));return{getStorage:e}}),__vite__mapDeps([0,1,2]));if(ya=e(fa),ja=a(ya),wa=s(ya),ba=t(ya),"undefined"!=typeof window){const{getAnalytics:e}=await We((async()=>{const{getAnalytics:e}=await import("./index.esm-CptSyBEO.js");return{getAnalytics:e}}),__vite__mapDeps([3,0,1,2]));va=e(ya)}return{app:ya,auth:ja,db:wa,storage:ba,analytics:va}}catch(e){throw e}};Na().catch((e=>{}));var ka=(e=>(e.User="user",e.Admin="admin",e))(ka||{}),Aa=(e=>(e.Pending="pending",e.Approved="approved",e.Rejected="rejected",e))(Aa||{});const Ea=async(e,a)=>{if(e)try{await Na();const{doc:r,getDoc:i,setDoc:n,updateDoc:o,serverTimestamp:l}=await We((async()=>{const{doc:e,getDoc:a,setDoc:s,updateDoc:t,serverTimestamp:r}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,getDoc:a,setDoc:s,updateDoc:t,serverTimestamp:r}}),__vite__mapDeps([0,1,2])),c=r(wa,"users",e.uid);if((await i(c)).exists())try{const e={updatedAt:l(),...a||{}};await o(c,e)}catch(s){throw s}else{const{email:s,displayName:r,photoURL:i}=e,o={uid:e.uid,email:s,displayName:r||(null==s?void 0:s.split("@")[0])||"User",photoURL:i||null,phone:(null==a?void 0:a.phone)||"",address:(null==a?void 0:a.address)||"",apartment:(null==a?void 0:a.apartment)||"",city:(null==a?void 0:a.city)||"",state:(null==a?void 0:a.state)||"",pincode:(null==a?void 0:a.pincode)||"",community:(null==a?void 0:a.community)||"",gpsCoordinates:(null==a?void 0:a.gpsCoordinates)||null,createdAt:l(),updatedAt:l(),wishlist:[],ownedBooks:[],role:ka.User};try{await n(c,o)}catch(t){throw t}}}catch(t){throw t}},_a=async e=>{if(!e)return null;try{await Na();const{doc:a,getDoc:s}=await We((async()=>{const{doc:e,getDoc:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,getDoc:a}}),__vite__mapDeps([0,1,2])),t=a(wa,"users",e),r=await s(t);return r.exists()?r.data():null}catch(a){return null}},Da=async(e,a)=>{if(!e)throw new Error("User ID is required");try{await Na();const{doc:s,updateDoc:t,serverTimestamp:r}=await We((async()=>{const{doc:e,updateDoc:a,serverTimestamp:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,updateDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2])),i=s(wa,"users",e),n={...a,updatedAt:r()};await t(i,n)}catch(s){throw s}},Pa=async()=>{try{await Na();const{collection:e,getDocs:a}=await We((async()=>{const{collection:e,getDocs:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,getDocs:a}}),__vite__mapDeps([0,1,2])),s=e(wa,"users"),t=await a(s),r=[];return t.forEach((e=>{r.push(e.data())})),r}catch(e){return[]}},Sa=async e=>{try{await Na();const{doc:a,updateDoc:s}=await We((async()=>{const{doc:e,updateDoc:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,updateDoc:a}}),__vite__mapDeps([0,1,2])),t=a(wa,"users",e);await s(t,{role:ka.Admin})}catch(a){throw a}},Ca=async e=>{try{await Na();const{doc:a,updateDoc:s}=await We((async()=>{const{doc:e,updateDoc:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,updateDoc:a}}),__vite__mapDeps([0,1,2])),t=a(wa,"users",e);await s(t,{role:ka.User})}catch(a){throw a}},Ra=async(e,a,s)=>{try{await Na();const{createUserWithEmailAndPassword:r,updateProfile:i,sendEmailVerification:n}=await We((async()=>{const{createUserWithEmailAndPassword:e,updateProfile:a,sendEmailVerification:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{createUserWithEmailAndPassword:e,updateProfile:a,sendEmailVerification:s}}),__vite__mapDeps([0,1,2])),{doc:o,setDoc:l,serverTimestamp:c}=await We((async()=>{const{doc:e,setDoc:a,serverTimestamp:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,setDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2])),d=(await r(auth,e,a)).user;s.displayName&&await i(d,{displayName:s.displayName});try{await n(d)}catch(t){}const m=o(wa,"users",d.uid),u={uid:d.uid,email:e,displayName:s.displayName||e.split("@")[0],phone:s.phone||"",address:s.address||"",apartment:s.apartment||"",city:s.city||"",state:s.state||"",pincode:s.pincode||"",community:s.community||"",photoURL:null,createdAt:c(),updatedAt:c(),wishlist:[],ownedBooks:[],role:s.role||ka.User};return await l(m,u),{success:!0,message:`User ${e} created successfully`,uid:d.uid}}catch(r){let e="Unknown error occurred";return r instanceof Error&&(e=r.message,e.includes("email-already-in-use")?e="This email is already in use. Please use a different email.":e.includes("weak-password")?e="The password is too weak. Please use a stronger password.":e.includes("invalid-email")&&(e="The email address is not valid.")),{success:!1,message:e}}},Ia=async e=>{if(!e)return{success:!1,message:"User ID is required"};try{await Na();const{doc:a,deleteDoc:s,getDoc:t}=await We((async()=>{const{doc:e,deleteDoc:a,getDoc:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,deleteDoc:a,getDoc:s}}),__vite__mapDeps([0,1,2])),r=a(wa,"users",e),i=await t(r);if(!i.exists())return{success:!1,message:"User not found"};const n=i.data();return await s(r),{success:!0,message:`User ${n.displayName||n.email} deleted successfully`}}catch(a){return{success:!1,message:`Failed to delete user: ${a instanceof Error?a.message:"Unknown error"}`}}},La=async()=>{try{await Na();const{collection:s,query:t,where:r,getDocs:i,doc:n,setDoc:o,getDoc:l}=await We((async()=>{const{collection:e,query:a,where:s,getDocs:t,doc:r,setDoc:i,getDoc:n}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,query:a,where:s,getDocs:t,doc:r,setDoc:i,getDoc:n}}),__vite__mapDeps([0,1,2])),c="<EMAIL>",d=t(s(wa,"users"),r("email","==",c)),m=await i(d);let u="",h=null;if(m.empty)try{u=c.replace(/[^a-zA-Z0-9]/g,"");const e=n(wa,"users",u),a=await l(e);a.exists()?h=a.data():(h={uid:u,email:c,displayName:"Admin User",role:ka.User,createdAt:(new Date).toISOString()},await o(e,h))}catch(e){return{success:!1,message:`Failed to create user document: ${e instanceof Error?e.message:"Unknown error"}`}}else{const e=m.docs[0];u=e.id,h=e.data()}if(h.role===ka.Admin)return{success:!0,message:"User is already an admin."};try{const e=n(wa,"users",u);return await o(e,{role:ka.Admin},{merge:!0}),{success:!0,message:`Successfully set ${c} as an admin!`}}catch(a){return{success:!1,message:`Failed to update user role: ${a instanceof Error?a.message:"Unknown error"}`}}}catch(s){return{success:!1,message:`Error in setupDirectAdmin: ${s instanceof Error?s.message:"Unknown error"}`}}},Ta=O.createContext(void 0),Va=()=>{const e=O.useContext(Ta);if(void 0===e)throw new Error("useAuth must be used within an AuthProvider");return e},Ba=({children:e})=>{const[a,s]=O.useState(null),[r,i]=O.useState(null),[n,o]=O.useState(!0),[l,c]=O.useState(!1),[d,m]=O.useState(!1),u=async()=>{if(!a)return m(!1),!1;try{if("<EMAIL>"===a.email){try{const{doc:e,setDoc:s,getDoc:t}=await We((async()=>{const{doc:e,setDoc:a,getDoc:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,setDoc:a,getDoc:s}}),__vite__mapDeps([0,1,2])),r=e(db,"users",a.uid),n=await t(r);if(n.exists()){n.data().role!==ka.Admin&&await s(r,{role:ka.Admin},{merge:!0})}else await s(r,{uid:a.uid,email:a.email,displayName:a.displayName||"Admin User",role:ka.Admin,createdAt:(new Date).toISOString()});const o=await _a(a.uid);i(o)}catch(e){}return m(!0),!0}const s=await _a(a.uid);if(s){const e=s.role===ka.Admin;return m(e),e}const t=await(async e=>{try{const a=await _a(e);return(null==a?void 0:a.role)===ka.Admin}catch(a){return!1}})(a.uid);return m(t),t}catch(s){return m(!1),!1}};O.useEffect((()=>{let e=()=>{};return(async()=>{try{await Na();const{onAuthStateChanged:a}=await We((async()=>{const{onAuthStateChanged:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{onAuthStateChanged:e}}),__vite__mapDeps([0,1,2]));e=a(ja,(async e=>{if(s(e),e){if(c(e.emailVerified),"<EMAIL>"===e.email)return m(!0),o(!1),void(async()=>{try{await u();const a=await _a(e.uid);i(a)}catch(a){}})();try{const s=await _a(e.uid);if(i(s),!s)try{await Ea(e);const a=await _a(e.uid);i(a)}catch(a){}const t=(null==s?void 0:s.role)===ka.Admin;m(t)}catch(t){}}else i(null),c(!1),m(!1);o(!1)}))}catch(a){o(!1)}})(),()=>e()}),[]);const h={currentUser:a,userData:r,loading:n,emailVerified:l,isAdmin:d,signUp:async(e,a,s,t)=>{try{await Na();const{createUserWithEmailAndPassword:o,updateProfile:l,sendEmailVerification:c}=await We((async()=>{const{createUserWithEmailAndPassword:e,updateProfile:a,sendEmailVerification:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{createUserWithEmailAndPassword:e,updateProfile:a,sendEmailVerification:s}}),__vite__mapDeps([0,1,2])),d=await o(ja,e,a);try{await l(d.user,{displayName:s});try{await c(d.user)}catch(r){}try{await Ea(d.user,t)}catch(i){}}catch(n){}return d.user}catch(o){throw o}},signIn:async(e,a)=>{try{await Na();const{signInWithEmailAndPassword:r,reload:n}=await We((async()=>{const{signInWithEmailAndPassword:e,reload:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{signInWithEmailAndPassword:e,reload:a}}),__vite__mapDeps([0,1,2])),o=await r(ja,e,a);try{await n(o.user)}catch(s){}o.user.emailVerified?c(!0):c(!1);try{const e=await _a(o.user.uid);i(e)}catch(t){}return o.user}catch(r){throw r}},signOut:async()=>{try{await Na();const{signOut:e}=await We((async()=>{const{signOut:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{signOut:e}}),__vite__mapDeps([0,1,2]));await e(ja),i(null),c(!1)}catch(e){throw e}},sendVerificationEmail:async()=>{if(!a)throw new Error("No user is currently signed in");try{await Na();const{sendEmailVerification:e}=await We((async()=>{const{sendEmailVerification:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{sendEmailVerification:e}}),__vite__mapDeps([0,1,2]));await e(a)}catch(e){throw e}},resetPassword:async e=>{try{await Na();const{sendPasswordResetEmail:a}=await We((async()=>{const{sendPasswordResetEmail:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{sendPasswordResetEmail:e}}),__vite__mapDeps([0,1,2]));await a(ja,e)}catch(a){throw a}},reloadUser:async()=>{if(!a)return!1;try{await Na();const{reload:e}=await We((async()=>{const{reload:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.j));return{reload:e}}),__vite__mapDeps([0,1,2]));await e(a);const s=ja.currentUser;if(!s)return c(!1),!1;const t=s.emailVerified;return c(t),t}catch(e){return c(!1),!1}},checkAdminStatus:u,refreshUserData:async()=>{if(!a)return null;try{const e=await _a(a.uid);return i(e),e}catch(e){return null}}};return t.jsx(Ta.Provider,{value:h,children:!n&&e})},Oa=J("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed",{variants:{variant:{default:"bg-burgundy-500 text-white hover:bg-burgundy-600 shadow-md",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"text-gray-700 hover:bg-burgundy-50 hover:text-burgundy-700",link:"text-burgundy-500 underline-offset-4 hover:underline hover:text-burgundy-600",navy:"bg-navy-500 text-white hover:bg-navy-600 shadow-md"},size:{default:"h-10 px-4 py-2",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}});function za({className:e,variant:a,size:s,...r}){return t.jsx("button",{className:ta(Oa({variant:a,size:s}),e),...r})}const Ua=({featureName:e,message:a})=>{const{currentUser:s,sendVerificationEmail:r}=Va(),[i,n]=z.useState(!1);return t.jsx("div",{className:"bg-white rounded-lg shadow-md p-8 max-w-md mx-auto",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto bg-amber-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:t.jsx(ee,{className:"h-8 w-8 text-amber-600"})}),t.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Email Verification Required"}),t.jsx("p",{className:"text-gray-600 mb-4",children:a||`You need to verify your email address before you can ${e}.`}),t.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:[t.jsxs("p",{className:"text-blue-800 text-sm",children:[t.jsx("strong",{children:"Why verify?"})," Email verification helps ensure the security of your account and allows us to contact you about your books and transactions."]}),t.jsxs("p",{className:"text-blue-700 text-sm mt-2",children:[t.jsx("strong",{children:"While waiting for verification:"})," You can still browse books, view book details, and access public pages, but you won't be able to add books or access your dashboard until your email is verified."]})]}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs(za,{onClick:async()=>{if(s){n(!0);try{await r(),Q.success("Verification email sent! Please check your inbox.")}catch(e){const a=e instanceof Error?e.message:"Failed to send verification email";Q.error(a)}finally{n(!1)}}},disabled:i,className:"w-full flex items-center justify-center gap-2",children:[t.jsx(ae,{className:"h-4 w-4"}),i?"Sending...":"Resend Verification Email"]}),t.jsx(U,{to:"/verify-email",children:t.jsx(za,{variant:"outline",className:"w-full",children:"Go to Verification Page"})})]}),t.jsx("div",{className:"mt-6 text-sm text-gray-500",children:t.jsx("p",{children:"Already verified your email? Try refreshing the page or signing out and signing back in."})})]})})},Fa=({children:e,redirectTo:a="/signin",requireVerification:s=!0,showVerificationUI:r=!1,featureName:i="access this feature",verificationMessage:n})=>{const{currentUser:o,loading:l,emailVerified:c}=Va(),d=F();if(l)return t.jsx("div",{className:"flex items-center justify-center min-h-screen",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-burgundy-500"})});if(!o)return t.jsx(M,{to:a,state:{from:d},replace:!0});if(s){if(!c)return r?t.jsx(Ua,{featureName:i,message:n}):t.jsx(M,{to:"/verify-email",state:{from:d},replace:!0})}return t.jsx(t.Fragment,{children:e})},Ma=({size:e="md",className:a})=>t.jsx("div",{className:ta("animate-spin rounded-full border-solid border-burgundy-500 border-t-transparent",{sm:"h-4 w-4 border-2",md:"h-8 w-8 border-3",lg:"h-12 w-12 border-4"}[e],a)}),Ka=({children:e})=>{const{currentUser:a,isAdmin:s,loading:r}=Va(),[i,n]=O.useState(!0);return O.useEffect((()=>{if(!r){const e=setTimeout((()=>{n(!1)}),500);return()=>clearTimeout(e)}}),[r]),r||i?t.jsx("div",{className:"flex justify-center items-center min-h-screen bg-gray-50",children:t.jsxs("div",{className:"text-center",children:[t.jsx(Ma,{size:"lg"}),t.jsx("p",{className:"mt-4 text-gray-600",children:"Loading admin portal..."})]})}):a?"<EMAIL>"===a.email||s?t.jsx(t.Fragment,{children:e}):t.jsx(M,{to:"/unauthorized",replace:!0}):t.jsx(M,{to:"/signin",replace:!0})},qa=J("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),Ha=O.forwardRef((({className:e,variant:a,size:s,asChild:r=!1,...i},n)=>{const o=r?p:"button";return t.jsx(o,{className:ta(qa({variant:a,size:s,className:e})),ref:n,...i})}));Ha.displayName="Button";class Ga extends O.Component{constructor(){super(...arguments),s(this,"state",{hasError:!1}),s(this,"handleRefresh",(()=>{window.location.reload()})),s(this,"handleGoHome",(()=>{window.location.href="/"}))}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,a){this.setState({error:e,errorInfo:a})}render(){return this.state.hasError?this.props.fallback?this.props.fallback:t.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:t.jsxs("div",{className:"max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-lg text-center",children:[t.jsx(ee,{className:"h-16 w-16 text-red-500 mx-auto mb-4"}),t.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"We're sorry, but something unexpected happened. Please try refreshing the page or go back to the home page."}),!1,t.jsxs("div",{className:"flex flex-col space-y-2",children:[t.jsxs(Ha,{onClick:this.handleRefresh,className:"w-full",children:[t.jsx(se,{className:"h-4 w-4 mr-2"}),"Refresh Page"]}),t.jsxs(Ha,{variant:"outline",onClick:this.handleGoHome,className:"w-full",children:[t.jsx(te,{className:"h-4 w-4 mr-2"}),"Go to Home"]})]})]})}):this.props.children}}function $a({className:e,...a}){return t.jsx("div",{className:ta("animate-pulse rounded-md bg-muted",e),...a})}const Wa=({type:e="page",message:a="Loading..."})=>"modal"===e?t.jsx("div",{className:"flex items-center justify-center p-8",children:t.jsxs("div",{className:"text-center",children:[t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-burgundy-600 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-600",children:a})]})}):"card"===e?t.jsxs("div",{className:"bg-white rounded-lg overflow-hidden shadow-md",children:[t.jsx($a,{className:"h-48 w-full"}),t.jsxs("div",{className:"p-4 space-y-2",children:[t.jsx($a,{className:"h-4 w-3/4"}),t.jsx($a,{className:"h-3 w-1/2"}),t.jsx($a,{className:"h-3 w-2/3"})]})]}):t.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsxs("div",{className:"relative mb-8",children:[t.jsx(re,{className:"h-16 w-16 text-burgundy-600 mx-auto animate-pulse"}),t.jsx("div",{className:"absolute inset-0 bg-burgundy-600 opacity-20 rounded-full animate-ping"})]}),t.jsx("h2",{className:"text-xl font-playfair font-semibold text-navy-800 mb-2",children:"PeerBooks"}),t.jsx("p",{className:"text-gray-600 mb-4",children:a}),t.jsxs("div",{className:"flex justify-center space-x-1",children:[t.jsx("div",{className:"w-2 h-2 bg-burgundy-600 rounded-full animate-bounce"}),t.jsx("div",{className:"w-2 h-2 bg-burgundy-600 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),t.jsx("div",{className:"w-2 h-2 bg-burgundy-600 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]})]})}),Ja=()=>t.jsx("div",{className:"min-h-screen bg-gray-50",children:t.jsx("div",{className:"container mx-auto px-4 py-8",children:t.jsxs("div",{className:"grid md:grid-cols-2 gap-8 lg:gap-12",children:[t.jsx("div",{className:"flex flex-col items-center md:items-start",children:t.jsx($a,{className:"w-full h-96 rounded-lg mb-6"})}),t.jsxs("div",{className:"space-y-6",children:[t.jsxs("div",{children:[t.jsx($a,{className:"h-8 w-3/4 mb-2"}),t.jsx($a,{className:"h-6 w-1/2 mb-4"}),t.jsxs("div",{className:"flex gap-2 mb-4",children:[t.jsx($a,{className:"h-6 w-16"}),t.jsx($a,{className:"h-6 w-20"}),t.jsx($a,{className:"h-6 w-18"})]})]}),t.jsxs("div",{className:"space-y-3",children:[t.jsx($a,{className:"h-4 w-full"}),t.jsx($a,{className:"h-4 w-5/6"}),t.jsx($a,{className:"h-4 w-4/5"})]}),t.jsxs("div",{className:"flex gap-4",children:[t.jsx($a,{className:"h-12 flex-1"}),t.jsx($a,{className:"h-12 flex-1"})]})]})]})})}),Ya=()=>t.jsx("div",{className:"min-h-screen bg-gray-50",children:t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 mb-8",children:[t.jsx($a,{className:"h-8 w-48 mb-2"}),t.jsx($a,{className:"h-4 w-64"})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[1,2,3].map((e=>t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[t.jsx($a,{className:"h-6 w-32 mb-2"}),t.jsx($a,{className:"h-8 w-16"})]},e)))}),t.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[t.jsx($a,{className:"h-6 w-32 mb-6"}),t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[1,2,3,4,5,6,7,8].map((e=>t.jsx(Wa,{type:"card"},e)))})]})]})}),Qa=({count:e=8})=>t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:Array.from({length:e}).map(((e,a)=>t.jsx(Wa,{type:"card"},a)))}),Za=()=>t.jsx("div",{className:"min-h-screen bg-gray-50",children:t.jsxs("div",{className:"animate-pulse",children:[t.jsx("div",{className:"bg-white shadow-sm border-b",children:t.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:t.jsxs("div",{className:"flex justify-between items-center py-4",children:[t.jsx($a,{className:"h-8 w-40"}),t.jsxs("div",{className:"flex space-x-4",children:[t.jsx($a,{className:"h-8 w-24"}),t.jsx($a,{className:"h-8 w-20"})]})]})})}),t.jsxs("div",{className:"flex",children:[t.jsxs("div",{className:"w-64 bg-white shadow-md",children:[t.jsxs("div",{className:"p-6 border-b",children:[t.jsx($a,{className:"h-6 w-3/4 mb-2"}),t.jsx($a,{className:"h-4 w-1/2"})]}),t.jsx("div",{className:"p-4 space-y-2",children:[...Array(6)].map(((e,a)=>t.jsxs("div",{className:"flex items-center px-4 py-3",children:[t.jsx($a,{className:"h-5 w-5 mr-3"}),t.jsx($a,{className:"h-4 w-24"})]},a)))})]}),t.jsx("div",{className:"flex-1 p-8",children:t.jsxs("div",{className:"max-w-5xl mx-auto",children:[t.jsx($a,{className:"h-8 w-1/3 mb-6"}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map(((e,a)=>t.jsxs("div",{className:"bg-white rounded-lg shadow p-6",children:[t.jsx($a,{className:"h-6 w-3/4 mb-4"}),t.jsxs("div",{className:"space-y-3",children:[t.jsx($a,{className:"h-4 w-full"}),t.jsx($a,{className:"h-4 w-5/6"}),t.jsx($a,{className:"h-4 w-4/6"})]})]},a)))})]})})]})]})}),Xa=8,es="firebasestorage.googleapis.com",as=e=>{try{return new URL(e).hostname.includes(es)}catch{return!1}},ss=(e=!0)=>e?"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 25vw":"(max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",ts=e=>{const a=(e=>{const a=document.createElement("link");return a.rel="preload",a.href=e.href,a.as=e.as||"image",e.fetchpriority&&a.setAttribute("fetchpriority",e.fetchpriority),e.crossorigin&&(a.crossOrigin=e.crossorigin),e.type&&(a.type=e.type),e.media&&(a.media=e.media),e.sizes&&a.setAttribute("sizes",e.sizes),a.setAttribute("data-preload-type","lcp-optimization"),a})(e);if(document.querySelector(`link[rel="preload"][href="${e.href}"]`))return;const s=document.head,t=s.firstChild;s.insertBefore(a,t)},rs=e=>{e.slice(0,Xa).forEach(((e,a)=>{const s=!(t=e.url)||t.includes(".webp")?t:as(t)?t.replace(/\.(jpg|jpeg|png)(\?|$)/i,".webp$2"):t;var t;const r=e.priority||(e.isAboveFold?"high":"low"),i=e.sizes||ss(e.isAboveFold),n={href:s,fetchpriority:r,crossorigin:as(s)?"anonymous":void 0,as:"image",sizes:i,media:e.media};"low"===r&&a>3?setTimeout((()=>ts(n)),100*(a-3)):ts(n)}))},is=()=>{document.querySelectorAll('link[data-preload-type="lcp-optimization"]').forEach((e=>e.remove()))},ns=()=>{if("undefined"!=typeof window&&"PerformanceObserver"in window)try{new PerformanceObserver((e=>{const a=e.getEntries(),s=a[a.length-1];if(s&&s.element&&"IMG"===s.element.tagName){const e=s.element.src;document.querySelector(`link[rel="preload"][href="${e}"]`)}})).observe({entryTypes:["largest-contentful-paint"]})}catch(e){}},os=({enabled:e=!0,onLCPMeasured:a,showDebugInfo:s=!1})=>{const[t,r]=O.useState(null),[i,n]=O.useState(!1);return O.useEffect((()=>{if(!e||"undefined"==typeof window)return;const s="PerformanceObserver"in window;if(n(s),!s)return;let t=null;try{t=new PerformanceObserver((e=>{var s;const t=e.getEntries(),i=t[t.length-1];if(i){let e=!1;if(i.element&&"IMG"===i.element.tagName){const a=i.element.src;e=!!document.querySelector(`link[rel="preload"][href="${a}"]`)}const t={value:i.startTime,element:i.element,url:i.url,timestamp:Date.now(),wasPreloaded:e};r(t),null==a||a(t),"IMG"===(null==(s=t.element)?void 0:s.tagName)&&t.wasPreloaded}})),t.observe({entryTypes:["largest-contentful-paint"]}),ns()}catch(i){}return()=>{null==t||t.disconnect()}}),[e,a]),null},ls=O.forwardRef((({className:e,...a},s)=>t.jsx(g,{ref:s,className:ta("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a})));ls.displayName=g.displayName;const cs=O.forwardRef((({className:e,...a},s)=>t.jsx(f,{ref:s,className:ta("aspect-square h-full w-full",e),...a})));cs.displayName=f.displayName;const ds=O.forwardRef((({className:e,...a},s)=>t.jsx(y,{ref:s,className:ta("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a})));ds.displayName=y.displayName;const ms=P,us=S,hs=C;O.forwardRef((({className:e,inset:a,children:s,...r},i)=>t.jsxs(j,{ref:i,className:ta("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",e),...r,children:[s,t.jsx(ie,{className:"ml-auto h-4 w-4"})]}))).displayName=j.displayName;O.forwardRef((({className:e,...a},s)=>t.jsx(w,{ref:s,className:ta("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}))).displayName=w.displayName;const xs=O.forwardRef((({className:e,sideOffset:a=4,...s},r)=>t.jsx(b,{children:t.jsx(v,{ref:r,sideOffset:a,className:ta("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})})));xs.displayName=v.displayName;const ps=O.forwardRef((({className:e,inset:a,...s},r)=>t.jsx(N,{ref:r,className:ta("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a&&"pl-8",e),...s})));ps.displayName=N.displayName;O.forwardRef((({className:e,children:a,checked:s,...r},i)=>t.jsxs(k,{ref:i,className:ta("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...r,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(A,{children:t.jsx(ne,{className:"h-4 w-4"})})}),a]}))).displayName=k.displayName;O.forwardRef((({className:e,children:a,...s},r)=>t.jsxs(E,{ref:r,className:ta("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(A,{children:t.jsx(oe,{className:"h-2 w-2 fill-current"})})}),a]}))).displayName=E.displayName;const gs=O.forwardRef((({className:e,inset:a,...s},r)=>t.jsx(_,{ref:r,className:ta("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",e),...s})));gs.displayName=_.displayName;const fs=O.forwardRef((({className:e,...a},s)=>t.jsx(D,{ref:s,className:ta("-mx-1 my-1 h-px bg-muted",e),...a})));fs.displayName=D.displayName;const ys=L,js=T,ws=O.forwardRef((({className:e,align:a="center",sideOffset:s=4,...r},i)=>t.jsx(R,{children:t.jsx(I,{ref:i,align:a,sideOffset:s,className:ta("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})})));ws.displayName=I.displayName;const bs=async e=>{try{await Na();const{collection:a,query:s,where:t,getDocs:r,orderBy:i}=await We((async()=>{const{collection:e,query:a,where:s,getDocs:t,orderBy:r}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,query:a,where:s,getDocs:t,orderBy:r}}),__vite__mapDeps([0,1,2])),n=s(a(wa,"books"),t("ownerId","==",e)),o=await r(n),l=[];return o.forEach((e=>{var a,s;const t=e.data(),r=(null==(a=t.createdAt)?void 0:a.toDate)?t.createdAt.toDate():new Date,i={id:e.id,title:t.title||"",author:t.author||"",isbn:t.isbn,genre:Array.isArray(t.genre)?t.genre:[],condition:t.condition||"Good",description:t.description||"",imageUrl:t.imageUrl||"https://via.placeholder.com/150?text=No+Image",imageUrls:t.imageUrls||void 0,displayImageIndex:t.displayImageIndex,perceivedValue:t.perceivedValue||5,price:t.price,rentalPrice:t.rentalPrice,rentalPeriod:t.rentalPeriod,securityDepositRequired:t.securityDepositRequired,securityDepositAmount:t.securityDepositAmount,availability:t.availability||"For Exchange",ownerId:t.ownerId||"",ownerName:t.ownerName||"",ownerEmail:t.ownerEmail,ownerLocation:t.ownerLocation,ownerCommunity:t.ownerCommunity||void 0,ownerCoordinates:t.ownerCoordinates||null,ownerPincode:t.ownerPincode||void 0,ownerRating:t.ownerRating||0,distance:t.distance,createdAt:r,approvalStatus:t.approvalStatus,status:t.status||"Available",nextAvailableDate:(null==(s=t.nextAvailableDate)?void 0:s.toDate)?t.nextAvailableDate.toDate():t.nextAvailableDate};l.push(i)})),l.sort(((e,a)=>{const s=e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt);return(a.createdAt instanceof Date?a.createdAt:new Date(a.createdAt)).getTime()-s.getTime()}))}catch(a){throw a}},vs=async e=>{var a,s;try{await Na();const{doc:i,getDoc:n}=await We((async()=>{const{doc:e,getDoc:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,getDoc:a}}),__vite__mapDeps([0,1,2])),o=i(wa,"books",e),l=await n(o);if(!l.exists())return null;const c=l.data(),d=(null==(a=c.createdAt)?void 0:a.toDate)?c.createdAt.toDate():new Date,m=c.imageUrls||[],u=c.imageUrl||(m.length>0?m[0]:"https://via.placeholder.com/150?text=No+Image"),h={id:l.id,title:c.title||"",author:c.author||"",isbn:c.isbn,genre:Array.isArray(c.genre)?c.genre:[],condition:c.condition||"Good",description:c.description||"",imageUrl:u,imageUrls:m.length>0?m:void 0,displayImageIndex:c.displayImageIndex,perceivedValue:c.perceivedValue||5,price:c.price,rentalPrice:c.rentalPrice,rentalPeriod:c.rentalPeriod,securityDepositRequired:c.securityDepositRequired,securityDepositAmount:c.securityDepositAmount,availability:c.availability||"For Exchange",ownerId:c.ownerId||"",ownerName:c.ownerName||"",ownerEmail:c.ownerEmail,ownerLocation:c.ownerLocation,ownerCommunity:c.ownerCommunity||void 0,ownerRating:c.ownerRating||0,distance:c.distance,createdAt:d,approvalStatus:c.approvalStatus,ownerCoordinates:c.ownerCoordinates||null,ownerPincode:c.ownerPincode||void 0,status:c.status||"Available",nextAvailableDate:(null==(s=c.nextAvailableDate)?void 0:s.toDate)?c.nextAvailableDate.toDate():c.nextAvailableDate};if(c.ownerPincode)h.ownerPincode=c.ownerPincode;else if(c.pincode)h.ownerPincode=c.pincode;else if(c.ownerLocation&&"string"==typeof c.ownerLocation){const e=c.ownerLocation.match(/\b\d{6}\b/);if(e){const a=e[0];h.ownerPincode=a}}if((!h.ownerCoordinates||!h.ownerPincode)&&h.ownerId)try{const e=i(wa,"users",h.ownerId),a=await n(e);if(a.exists()){const e=a.data();if(h.ownerEmail||(e.email?h.ownerEmail=e.email:e.emailAddress?h.ownerEmail=e.emailAddress:e.userEmail&&(h.ownerEmail=e.userEmail)),h.ownerCoordinates||(e.gpsCoordinates?h.ownerCoordinates=e.gpsCoordinates:void 0!==e.latitude&&void 0!==e.longitude&&(h.ownerCoordinates={latitude:e.latitude,longitude:e.longitude})),!h.ownerPincode)if(e.pincode)h.ownerPincode=e.pincode;else if(e.pinCode)h.ownerPincode=e.pinCode;else if(e.pin_code)h.ownerPincode=e.pin_code;else if(e.postalCode)h.ownerPincode=e.postalCode;else if(e.zipcode)h.ownerPincode=e.zipcode;else if(e.zip)h.ownerPincode=e.zip;else if(e.address){const a=e.address.match(/\b\d{6}\b/);if(a){const e=a[0];h.ownerPincode=e}}}}catch(t){}if(h.ownerPincode&&!c.ownerPincode||h.ownerEmail&&!c.ownerEmail)try{const{updateDoc:e}=await We((async()=>{const{updateDoc:e}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{updateDoc:e}}),__vite__mapDeps([0,1,2])),a={updatedAt:new Date};h.ownerPincode&&!c.ownerPincode&&(a.ownerPincode=h.ownerPincode),h.ownerEmail&&!c.ownerEmail&&(a.ownerEmail=h.ownerEmail),await e(o,a)}catch(r){}return h}catch(i){throw i}},Ns=async(e=!1,a)=>{try{await Na();const{collection:s,query:t,where:r,getDocs:i,orderBy:n}=await We((async()=>{const{collection:e,query:a,where:s,getDocs:t,orderBy:r}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,query:a,where:s,getDocs:t,orderBy:r}}),__vite__mapDeps([0,1,2])),{getUserLocationSilently:o,calculateDistance:l}=await We((async()=>{const{getUserLocationSilently:e,calculateDistance:a}=await import("./geolocationUtils-DEQwKbhP.js");return{getUserLocationSilently:e,calculateDistance:a}}),[]);let c;c=t(s(wa,"books"));const d=await o(),m=await i(c),u=[],h=[];if(m.forEach((e=>{var a,s;const t=e.data(),r=(null==(a=t.createdAt)?void 0:a.toDate)?t.createdAt.toDate():new Date,i={id:e.id,title:t.title||"",author:t.author||"",isbn:t.isbn,genre:Array.isArray(t.genre)?t.genre:[],condition:t.condition||"Good",description:t.description||"",imageUrl:t.imageUrl||"https://via.placeholder.com/150?text=No+Image",perceivedValue:t.perceivedValue||5,price:t.price,rentalPrice:t.rentalPrice,rentalPeriod:t.rentalPeriod,securityDepositRequired:t.securityDepositRequired,securityDepositAmount:t.securityDepositAmount,availability:t.availability||"For Exchange",ownerId:t.ownerId||"",ownerName:t.ownerName||"",ownerEmail:t.ownerEmail,ownerLocation:t.ownerLocation,ownerCommunity:t.ownerCommunity||void 0,ownerRating:t.ownerRating||0,distance:t.distance,createdAt:r,approvalStatus:t.approvalStatus,ownerCoordinates:null,ownerPincode:t.ownerPincode,status:t.status||"Available",nextAvailableDate:(null==(s=t.nextAvailableDate)?void 0:s.toDate)?t.nextAvailableDate.toDate():t.nextAvailableDate};if(t.ownerCoordinates)"object"==typeof t.ownerCoordinates&&null!==t.ownerCoordinates&&"number"==typeof t.ownerCoordinates.latitude&&"number"==typeof t.ownerCoordinates.longitude&&(i.ownerCoordinates={latitude:t.ownerCoordinates.latitude,longitude:t.ownerCoordinates.longitude});else if(i.ownerId){const e=u.length;h.push({book:i,index:e})}if(d&&i.ownerCoordinates)try{i.distance=l(d,i.ownerCoordinates)}catch(n){}u.push(i)})),h.length>0&&d){const{doc:e,getDoc:a}=await We((async()=>{const{doc:e,getDoc:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,getDoc:a}}),__vite__mapDeps([0,1,2])),s=5;for(let t=0;t<h.length;t+=s){const r=h.slice(t,t+s);await Promise.all(r.map((async({book:s,index:t})=>{try{const i=e(wa,"users",s.ownerId),n=await a(i);if(n.exists()){const e=n.data();let a=null;if(e.gpsCoordinates?a=e.gpsCoordinates:e.coordinates?a=e.coordinates:void 0!==e.latitude&&void 0!==e.longitude?a={latitude:e.latitude,longitude:e.longitude}:e.location&&"object"==typeof e.location&&void 0!==e.location.latitude&&void 0!==e.location.longitude&&(a={latitude:e.location.latitude,longitude:e.location.longitude}),a&&"object"==typeof a&&"number"==typeof a.latitude&&"number"==typeof a.longitude){u[t].ownerCoordinates=a;try{u[t].distance=l(d,a)}catch(r){}}}}catch(i){}})))}}let x=u;return e||(x=u.filter((e=>e.approvalStatus===Aa.Approved||void 0===e.approvalStatus||null===e.approvalStatus))),x.sort(((e,s)=>{const t=a&&e.ownerCommunity&&e.ownerCommunity===a,r=a&&s.ownerCommunity&&s.ownerCommunity===a;if(t&&!r)return-1;if(r&&!t)return 1;if(d){if(void 0!==e.distance&&void 0!==s.distance)return e.distance-s.distance;if(void 0!==e.distance)return-1;if(void 0!==s.distance)return 1}const i=e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt);return(s.createdAt instanceof Date?s.createdAt:new Date(s.createdAt)).getTime()-i.getTime()}))}catch(s){throw s}},ks=async()=>{try{await Na();const{collection:a,addDoc:s,serverTimestamp:t}=await We((async()=>{const{collection:e,addDoc:a,serverTimestamp:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,addDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2])),r=a(wa,"books"),i=[{title:"The Alchemist",author:"Paulo Coelho",isbn:"9780062315007",genre:["Fiction","Philosophy","Fantasy"],condition:"Like New",description:"A philosophical novel about a young shepherd who dreams of finding treasure and embarks on a journey of self-discovery.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1654371463i/18144590.jpg",perceivedValue:9,price:450,availability:"For Exchange",ownerId:"user123",ownerName:"Priya Sharma",ownerLocation:"Kolkata",ownerRating:4.7,distance:6.8},{title:"To Kill a Mockingbird",author:"Harper Lee",isbn:"9780061120084",genre:["Fiction","Classics","Literature"],condition:"Good",description:"A classic novel about racial injustice in the American South through the eyes of a young girl.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1553383690i/2657.jpg",perceivedValue:8,price:350,rentalPrice:50,rentalPeriod:"per week",availability:"For Rent, Sale & Exchange",ownerId:"user456",ownerName:"Sarah Johnson",ownerLocation:"Mumbai",ownerRating:4.8,distance:3.2},{title:"1984",author:"George Orwell",isbn:"9780451524935",genre:["Fiction","Classics","Dystopian"],condition:"Like New",description:"A dystopian novel set in a totalitarian society where critical thought is suppressed.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1657781256i/61439040.jpg",perceivedValue:9,price:400,availability:"For Sale & Exchange",ownerId:"user789",ownerName:"Raj Patel",ownerLocation:"Delhi",ownerRating:4.5,distance:5.7},{title:"The Great Gatsby",author:"F. Scott Fitzgerald",isbn:"9780743273565",genre:["Fiction","Classics","Literature"],condition:"Good",description:"A story of wealth, love, and the American Dream in the 1920s.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1490528560i/4671.jpg",perceivedValue:7,rentalPrice:40,rentalPeriod:"per week",availability:"For Rent & Exchange",ownerId:"user101",ownerName:"Anita Mehta",ownerLocation:"Bangalore",ownerRating:4.9,distance:1.3},{title:"Harry Potter and the Sorcerer's Stone",author:"J.K. Rowling",isbn:"9780590353427",genre:["Fantasy","Young Adult","Fiction"],condition:"Fair",description:"The first book in the beloved Harry Potter series about a boy who discovers he's a wizard.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1474154022i/3.jpg",perceivedValue:6,price:300,rentalPrice:35,rentalPeriod:"per week",availability:"For Rent & Sale",ownerId:"user202",ownerName:"Vikram Singh",ownerLocation:"Chennai",ownerRating:4.6,distance:4.5},{title:"Pride and Prejudice",author:"Jane Austen",isbn:"9780141439518",genre:["Fiction","Classics","Romance"],condition:"Like New",description:"A classic romance novel examining the relationships between young people in 19th century England.",imageUrl:"https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1320399351i/1885.jpg",perceivedValue:8,rentalPrice:45,rentalPeriod:"per week",availability:"For Rent & Exchange",ownerId:"user303",ownerName:"Anjali Kumar",ownerLocation:"Hyderabad",ownerRating:4.4,distance:2.9}];for(const n of i)try{const e={...n,createdAt:t(),updatedAt:t(),approvalStatus:Aa.Approved};await s(r,e)}catch(e){}}catch(a){throw a}},As=async()=>{try{await Na();const{collection:e,query:a,getDocs:s}=await We((async()=>{const{collection:e,query:a,getDocs:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,query:a,getDocs:s}}),__vite__mapDeps([0,1,2])),t=a(e(wa,"books")),r=await s(t),i=[];r.forEach((e=>{var a;const s=e.data();if(s.approvalStatus===Aa.Pending){const t=(null==(a=s.createdAt)?void 0:a.toDate)?s.createdAt.toDate():new Date,r={id:e.id,title:s.title||"",author:s.author||"",isbn:s.isbn,genre:Array.isArray(s.genre)?s.genre:[],condition:s.condition||"Good",description:s.description||"",imageUrl:s.imageUrl||"https://via.placeholder.com/150?text=No+Image",perceivedValue:s.perceivedValue||5,price:s.price,rentalPrice:s.rentalPrice,rentalPeriod:s.rentalPeriod,availability:s.availability||"For Exchange",ownerId:s.ownerId||"",ownerName:s.ownerName||"",ownerEmail:s.ownerEmail,ownerLocation:s.ownerLocation||"",ownerRating:s.ownerRating||0,distance:s.distance,createdAt:t,approvalStatus:s.approvalStatus};i.push(r)}}));return i.sort(((e,a)=>{const s=e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt);return(a.createdAt instanceof Date?a.createdAt:new Date(a.createdAt)).getTime()-s.getTime()}))}catch(e){if(e instanceof Error){if(e.message.includes("permission-denied"))throw new Error("Permission denied: You do not have access to view pending books");if(e.message.includes("unavailable"))throw new Error("Firebase service is currently unavailable. Please try again later");if(e.message.includes("network"))throw new Error("Network error. Please check your internet connection");if(e.message.includes("index"))throw new Error("Missing index error. Please contact the administrator")}throw e}},Es=async(e,a)=>{try{await Na();const{doc:s,getDoc:t,updateDoc:r,serverTimestamp:i,Timestamp:n}=await We((async()=>{const{doc:e,getDoc:a,updateDoc:s,serverTimestamp:t,Timestamp:r}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,getDoc:a,updateDoc:s,serverTimestamp:t,Timestamp:r}}),__vite__mapDeps([0,1,2])),o=s(wa,"books",e);if(!(await t(o)).exists())throw new Error(`Book with ID ${e} not found`);const l={};Object.entries(a).forEach((([e,a])=>{void 0!==a&&(Array.isArray(a)?l[e]=a:a instanceof Date?l[e]=n.fromDate(a):l[e]=a)}));const c={...l,updatedAt:i()};await r(o,c);await t(o)}catch(s){throw s}},_s=async e=>{try{await Na();const{doc:a,updateDoc:s,serverTimestamp:t}=await We((async()=>{const{doc:e,updateDoc:a,serverTimestamp:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,updateDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2])),r=a(wa,"books",e);await s(r,{approvalStatus:Aa.Approved,updatedAt:t()})}catch(a){throw a}},Ds=async(e,a)=>{try{await Na();const{doc:s,updateDoc:t,serverTimestamp:r}=await We((async()=>{const{doc:e,updateDoc:a,serverTimestamp:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,updateDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2])),i=s(wa,"books",e);await t(i,{approvalStatus:Aa.Rejected,rejectionReason:a,updatedAt:r()})}catch(s){throw s}},Ps=async e=>{try{if(!e||""===e.trim())return[];const a=e.toLowerCase().trim(),s=await Ns(!1);return s.filter((e=>{const s=e.title.toLowerCase().includes(a),t=e.author.toLowerCase().includes(a),r=e.genre.some((e=>e.toLowerCase().includes(a))),i=!!e.isbn&&e.isbn.toLowerCase().includes(a);return s||t||r||i}))}catch(a){throw new Error(`Failed to search books: ${a instanceof Error?a.message:"Unknown error"}`)}},Ss=Object.freeze(Object.defineProperty({__proto__:null,approveBook:_s,createBook:async e=>{try{if(!e.title)throw new Error("Book title is required");if(!e.author)throw new Error("Author name is required");if(!e.genre||!Array.isArray(e.genre)||0===e.genre.length)throw new Error("At least one genre is required");if(!e.condition)throw new Error("Book condition is required");if(!e.description)throw new Error("Book description is required");if(!e.availability)throw new Error("Availability option is required");if(!e.ownerId)throw new Error("Owner ID is required. Please make sure you are signed in.");if(e.availability.includes("Rent")){if(null===e.rentalPrice||void 0===e.rentalPrice)throw new Error('Rental price is required when availability includes "For Rent"');if(null===e.rentalPeriod||void 0===e.rentalPeriod)throw new Error('Rental period is required when availability includes "For Rent"')}if(e.availability.includes("Sale")&&(null===e.price||void 0===e.price))throw new Error('Sale price is required when availability includes "For Sale"');await Na();const{collection:t,addDoc:r,serverTimestamp:i}=await We((async()=>{const{collection:e,addDoc:a,serverTimestamp:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,addDoc:a,serverTimestamp:s}}),__vite__mapDeps([0,1,2]));try{const s=t(wa,"books"),n=Object.entries(e).reduce(((e,[a,s])=>(void 0!==s&&(e[a]=s),e)),{});let o=n.ownerEmail;if(!o&&n.ownerId)try{const{doc:e,getDoc:a}=await We((async()=>{const{doc:e,getDoc:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,getDoc:a}}),__vite__mapDeps([0,1,2])),s=e(wa,"users",n.ownerId),t=await a(s);if(t.exists()){const e=t.data();o=e.email||e.emailAddress||e.userEmail}}catch(a){}const l={...n,ownerEmail:o||void 0,createdAt:i(),updatedAt:i(),approvalStatus:Aa.Pending};return(await r(s,l)).id}catch(s){throw s instanceof Error?s.message.includes("permission-denied")?new Error("Permission denied: You do not have access to add books"):s.message.includes("unavailable")?new Error("Firebase service is currently unavailable. Please try again later"):s.message.includes("network")?new Error("Network error. Please check your internet connection"):s:new Error("Unknown error occurred while adding the book")}}catch(t){throw t}},getAllBooks:Ns,getBook:vs,getBooksByOwner:bs,getPendingBooks:As,rejectBook:Ds,searchBooks:Ps,seedRealBooksToFirebase:ks,updateBook:Es},Symbol.toStringTag,{value:"Module"})),Cs=({children:e})=>{var a;const{currentUser:s,userData:r,signOut:i,emailVerified:n,sendVerificationEmail:o}=Va(),[l,c]=O.useState([]),[d,m]=O.useState(!0),[u,h]=O.useState(!1),[x,p]=O.useState(!1),g=K();O.useEffect((()=>{(async()=>{if(s&&u)try{m(!0);const e=await bs(s.uid);c(e)}catch(e){}finally{m(!1)}})()}),[s,u]);const f=l.length||0,y=l.filter((e=>e.approvalStatus===Aa.Approved||!e.approvalStatus)).length,j=l.filter((e=>e.approvalStatus===Aa.Pending)).length,w=(null==(a=null==r?void 0:r.wishlist)?void 0:a.length)||0;return s&&r?t.jsxs(ys,{open:u,onOpenChange:h,children:[t.jsx(js,{asChild:!0,children:t.jsx("div",{onClick:e=>{e.preventDefault(),e.stopPropagation(),h(!1),g("/dashboard")},children:e})}),t.jsx(ws,{className:"w-80 p-0",align:"end",children:t.jsxs("div",{className:"flex flex-col",children:[t.jsxs("div",{className:"bg-gray-50 p-4 rounded-t-md",children:[t.jsxs("div",{className:"flex items-center gap-3",children:[t.jsxs(ls,{className:"h-12 w-12",children:[t.jsx(cs,{src:r.photoURL||"",alt:r.displayName||"User"}),t.jsx(ds,{className:"bg-burgundy-100 text-burgundy-700",children:(null==r?void 0:r.displayName)?r.displayName.split(" ").map((e=>e[0])).join("").toUpperCase().substring(0,2):(null==(b=null==s?void 0:s.email)?void 0:b.substring(0,2).toUpperCase())||"U"})]}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("h3",{className:"font-medium text-navy-800 truncate",children:r.displayName}),t.jsx("p",{className:"text-sm text-gray-500 truncate",children:r.email}),t.jsx("div",{className:"flex items-center mt-1",children:n?t.jsxs("div",{className:"flex items-center text-green-600 text-xs",children:[t.jsx(le,{className:"h-3 w-3 mr-1"}),t.jsx("span",{children:"Email verified"})]}):t.jsxs("div",{className:"flex items-center text-amber-600 text-xs",children:[t.jsx(ee,{className:"h-3 w-3 mr-1"}),t.jsx("span",{children:"Email not verified"})]})})]})]}),t.jsxs("div",{className:"mt-3 text-xs text-gray-500 flex items-center",children:[t.jsx(ce,{className:"h-3 w-3 mr-1"}),t.jsxs("span",{children:["Member since ",(e=>{if(!e)return"N/A";const a=e.toDate?e.toDate():new Date(e);return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(a)})(r.createdAt)]})]})]}),t.jsxs("div",{className:"p-4",children:[t.jsx("h4",{className:"text-sm font-medium text-gray-700 mb-3",children:"Your Book Stats"}),d?t.jsxs("div",{className:"space-y-2",children:[t.jsx($a,{className:"h-4 w-full"}),t.jsx($a,{className:"h-4 w-full"}),t.jsx($a,{className:"h-4 w-full"})]}):t.jsxs("div",{className:"grid grid-cols-2 gap-2",children:[t.jsx("div",{className:"bg-gray-50 p-2 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(re,{className:"h-3.5 w-3.5 text-burgundy-500 mr-1.5"}),t.jsx("span",{className:"text-xs",children:"Total Books"})]}),t.jsx("span",{className:"font-medium text-sm",children:f})]})}),t.jsx("div",{className:"bg-gray-50 p-2 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(de,{className:"h-3.5 w-3.5 text-green-500 mr-1.5"}),t.jsx("span",{className:"text-xs",children:"Active"})]}),t.jsx("span",{className:"font-medium text-sm",children:y})]})}),t.jsx("div",{className:"bg-gray-50 p-2 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(me,{className:"h-3.5 w-3.5 text-amber-500 mr-1.5"}),t.jsx("span",{className:"text-xs",children:"Pending"})]}),t.jsx("span",{className:"font-medium text-sm",children:j})]})}),t.jsx("div",{className:"bg-gray-50 p-2 rounded-md",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(ue,{className:"h-3.5 w-3.5 text-red-500 mr-1.5"}),t.jsx("span",{className:"text-xs",children:"Wishlist"})]}),t.jsx("span",{className:"font-medium text-sm",children:w})]})})]})]}),t.jsxs("div",{className:"border-t border-gray-100 p-2",children:[t.jsxs(U,{to:"/dashboard",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>h(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(he,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Dashboard"})]}),t.jsx(ie,{className:"h-4 w-4 text-gray-400"})]}),t.jsxs(U,{to:"/profile",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>h(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(xe,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Profile"})]}),t.jsx(ie,{className:"h-4 w-4 text-gray-400"})]}),t.jsxs(U,{to:"/my-books",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>h(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(re,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"My Books"})]}),t.jsx(ie,{className:"h-4 w-4 text-gray-400"})]}),t.jsxs(U,{to:"/wishlist",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>h(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(ue,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Wishlist"})]}),t.jsx(ie,{className:"h-4 w-4 text-gray-400"})]}),t.jsxs(U,{to:"/settings",className:"flex items-center justify-between p-2 hover:bg-gray-50 rounded-md",onClick:()=>h(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(pe,{className:"h-4 w-4 text-gray-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Settings"})]}),t.jsx(ie,{className:"h-4 w-4 text-gray-400"})]}),!n&&t.jsxs("div",{className:"border-t border-gray-100 mt-2 pt-2",children:[t.jsxs("button",{onClick:async()=>{if(s){p(!0);try{await o(),Q.success("Verification email sent! Please check your inbox.")}catch(e){const a=e instanceof Error?e.message:"Failed to send verification email";Q.error(a)}finally{p(!1)}}},disabled:x,className:"flex items-center justify-between w-full p-2 hover:bg-amber-50 rounded-md text-left text-amber-700",children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(ae,{className:"h-4 w-4 mr-2"}),t.jsx("span",{className:"text-sm",children:x?"Sending...":"Verify Email"})]}),t.jsx(ie,{className:"h-4 w-4 text-amber-400"})]}),t.jsxs(U,{to:"/verify-email",className:"flex items-center justify-between w-full p-2 hover:bg-gray-50 rounded-md text-left",onClick:()=>h(!1),children:[t.jsxs("div",{className:"flex items-center",children:[t.jsx(ee,{className:"h-4 w-4 text-amber-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Go to Verification Page"})]}),t.jsx(ie,{className:"h-4 w-4 text-gray-400"})]})]}),t.jsx("div",{className:"border-t border-gray-100 mt-2 pt-2",children:t.jsx("button",{onClick:async()=>{try{await i(),h(!1),g("/")}catch(e){}},className:"flex items-center justify-between w-full p-2 hover:bg-gray-50 rounded-md text-left",children:t.jsxs("div",{className:"flex items-center",children:[t.jsx(ge,{className:"h-4 w-4 text-red-500 mr-2"}),t.jsx("span",{className:"text-sm",children:"Sign Out"})]})})})]})]})})]}):t.jsx(t.Fragment,{children:e});var b},Rs=()=>{var e;const{currentUser:a,userData:s,signOut:r,emailVerified:i,sendVerificationEmail:n}=Va(),[o,l]=O.useState(!1),[c,d]=O.useState(!1);return t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx(Cs,{children:t.jsxs(za,{variant:"ghost",className:"flex items-center gap-2 px-2 py-1 hover:bg-burgundy-50 hover:text-burgundy-700 rounded-full cursor-pointer transition-colors","aria-label":"View Dashboard",children:[t.jsxs(ls,{className:"h-8 w-8",children:[t.jsx(cs,{src:(null==s?void 0:s.photoURL)||"",alt:(null==s?void 0:s.displayName)||"User"}),t.jsx(ds,{className:"bg-burgundy-100 text-burgundy-700",children:(null==s?void 0:s.displayName)?s.displayName.split(" ").map((e=>e[0])).join("").toUpperCase().substring(0,2):(null==(m=null==a?void 0:a.email)?void 0:m.substring(0,2).toUpperCase())||"U"})]}),t.jsx("span",{className:"hidden md:inline text-sm font-medium",children:(null==s?void 0:s.displayName)||(null==(e=null==a?void 0:a.email)?void 0:e.split("@")[0])||"User"})]})}),t.jsxs(ms,{children:[t.jsx(us,{asChild:!0,children:t.jsx(za,{variant:"ghost",className:"hidden","aria-label":"Menu",children:t.jsx("span",{className:"sr-only",children:"Menu"})})}),t.jsxs(xs,{align:"end",className:"w-56",children:[t.jsx(gs,{children:"My Account"}),!i&&t.jsxs("div",{className:"px-2 py-1.5 text-xs bg-amber-50 text-amber-800 border-y border-amber-200 flex items-center gap-1.5",children:[t.jsx(ee,{className:"h-3.5 w-3.5"}),t.jsx("span",{children:"Email not verified"})]}),t.jsx(fs,{}),t.jsxs(hs,{children:[t.jsx(ps,{asChild:!0,children:t.jsxs(U,{to:"/dashboard",className:"cursor-pointer w-full",children:[t.jsx(he,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Dashboard"})]})}),t.jsx(ps,{asChild:!0,children:t.jsxs(U,{to:"/profile",className:"cursor-pointer w-full",children:[t.jsx(xe,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Profile"})]})}),t.jsx(ps,{asChild:!0,children:t.jsxs(U,{to:"/my-books",className:"cursor-pointer w-full",children:[t.jsx(re,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"My Books"})]})}),t.jsx(ps,{asChild:!0,children:t.jsxs(U,{to:"/wishlist",className:"cursor-pointer w-full",children:[t.jsx(ue,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Wishlist"})]})}),t.jsx(ps,{asChild:!0,children:t.jsxs(U,{to:"/messages",className:"cursor-pointer w-full",children:[t.jsx(fe,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Messages"})]})})]}),t.jsx(fs,{}),t.jsx(ps,{asChild:!0,children:t.jsxs(U,{to:"/settings",className:"cursor-pointer w-full",children:[t.jsx(pe,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Settings"})]})}),t.jsx(fs,{}),!i&&t.jsxs(t.Fragment,{children:[t.jsxs(ps,{onClick:async()=>{if(a){d(!0);try{await n(),Q.success("Verification email sent! Please check your inbox.")}catch(e){const a=e instanceof Error?e.message:"Failed to send verification email";Q.error(a)}finally{d(!1)}}},disabled:c,className:"cursor-pointer text-amber-700",children:[t.jsx(ae,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:c?"Sending...":"Verify Email"})]}),t.jsx(fs,{})]}),t.jsxs(ps,{onClick:async()=>{try{l(!0),await r(),Q.success("Signed out successfully")}catch(e){Q.error("Failed to sign out")}finally{l(!1)}},disabled:o,className:"cursor-pointer",children:[t.jsx(ge,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:o?"Signing out...":"Sign out"})]})]})]})]});var m},Is=()=>{const[e,a]=z.useState(!1),{currentUser:s,loading:r,emailVerified:i}=Va();return t.jsxs("header",{className:"bg-white shadow-sm sticky top-0 z-50",children:[t.jsxs("div",{className:"container mx-auto px-4 py-4 flex justify-between items-center",children:[t.jsx("div",{className:"flex items-center",children:t.jsxs(U,{to:"/",className:"flex items-center",children:[t.jsx(re,{className:"h-6 w-6 text-burgundy-500 mr-2"}),t.jsx("span",{className:"text-xl font-playfair font-bold text-navy-500",children:"PeerBooks"})]})}),t.jsxs("nav",{className:"hidden md:flex items-center space-x-6",children:[t.jsx(U,{to:"/",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"Home"}),t.jsx(U,{to:"/browse",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"Browse Books"}),s&&!i?t.jsx(ha,{children:t.jsxs(xa,{children:[t.jsx(pa,{asChild:!0,children:t.jsxs(U,{to:"/add-books",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation flex items-center",children:["Add Your Books",t.jsx(ee,{className:"h-3.5 w-3.5 ml-1 text-amber-500"})]})}),t.jsx(ga,{children:t.jsx("p",{children:"Email verification required"})})]})}):t.jsx(U,{to:"/add-books",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"Add Your Books"}),t.jsx(U,{to:"/how-it-works",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"How It Works"}),t.jsx(U,{to:"/feedback",className:"text-gray-700 hover:text-burgundy-500 hover-underline-animation",children:"Feedback"})]}),t.jsx("div",{className:"hidden md:flex items-center space-x-4",children:r?t.jsx("div",{className:"h-9 w-20 bg-gray-200 animate-pulse rounded-md"}):s?t.jsx(Rs,{}):t.jsxs(t.Fragment,{children:[t.jsx(U,{to:"/signin",children:t.jsx(za,{variant:"link",children:"Sign In"})}),t.jsx(U,{to:"/join",children:t.jsx(za,{children:"Join Now"})})]})}),t.jsx("button",{className:"md:hidden text-gray-700",onClick:()=>a(!e),children:t.jsx(ye,{className:"h-6 w-6"})})]}),e&&t.jsx("div",{className:"md:hidden bg-white shadow-lg py-4 px-4 animate-fade-in",children:t.jsxs("nav",{className:"flex flex-col space-y-3",children:[t.jsx(U,{to:"/",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Home"}),t.jsx(U,{to:"/browse",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Browse Books"}),s&&!i?t.jsxs(U,{to:"/add-books",className:"text-gray-700 hover:text-burgundy-500 py-2 flex items-center",onClick:()=>a(!1),children:["Add Your Books",t.jsx(ee,{className:"h-3.5 w-3.5 ml-1 text-amber-500"}),t.jsx("span",{className:"ml-1 text-xs text-amber-600",children:"(Verification Required)"})]}):t.jsx(U,{to:"/add-books",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Add Your Books"}),t.jsx(U,{to:"/how-it-works",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"How It Works"}),t.jsx(U,{to:"/feedback",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Feedback"}),t.jsx(U,{to:"/contact",className:"text-gray-700 hover:text-burgundy-500 py-2",onClick:()=>a(!1),children:"Contact Us"}),t.jsx("div",{className:"pt-2 flex space-x-4",children:r?t.jsx("div",{className:"h-8 w-full bg-gray-200 animate-pulse rounded-md"}):s?t.jsxs("div",{className:"w-full space-y-2",children:[t.jsx(U,{to:"/dashboard",onClick:()=>a(!1),className:"block",children:t.jsxs(za,{variant:"outline",size:"sm",className:"w-full justify-start",children:[t.jsx(he,{className:"h-4 w-4 mr-2"}),"Dashboard",!i&&t.jsx(ee,{className:"h-3.5 w-3.5 ml-2 text-amber-500"})]})}),t.jsx(U,{to:"/profile",onClick:()=>a(!1),className:"block",children:t.jsxs(za,{variant:"outline",size:"sm",className:"w-full justify-start",children:[t.jsx(xe,{className:"h-4 w-4 mr-2"}),"My Profile",!i&&t.jsx(ee,{className:"h-3.5 w-3.5 ml-2 text-amber-500"})]})}),t.jsx(U,{to:"/my-books",onClick:()=>a(!1),className:"block",children:t.jsxs(za,{variant:"outline",size:"sm",className:"w-full justify-start",children:[t.jsx(je,{className:"h-4 w-4 mr-2"}),"My Books",!i&&t.jsx(ee,{className:"h-3.5 w-3.5 ml-2 text-amber-500"})]})}),!i&&t.jsx(U,{to:"/verify-email",onClick:()=>a(!1),className:"block mt-4",children:t.jsx(za,{size:"sm",className:"w-full justify-center bg-amber-500 hover:bg-amber-600",children:"Verify Email"})})]}):t.jsxs(t.Fragment,{children:[t.jsx(U,{to:"/signin",onClick:()=>a(!1),children:t.jsx(za,{variant:"link",size:"sm",children:"Sign In"})}),t.jsx(U,{to:"/join",onClick:()=>a(!1),children:t.jsx(za,{size:"sm",children:"Join Now"})})]})})]})})]})},Ls=()=>t.jsx("footer",{className:"bg-navy-500 text-white pt-12 pb-6",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[t.jsxs("div",{className:"col-span-1 md:col-span-1",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx(re,{className:"h-6 w-6 text-beige-500 mr-2"}),t.jsx("span",{className:"text-xl font-playfair font-bold",children:"PeerBooks"})]}),t.jsx("p",{className:"text-sm text-gray-300 mb-6",children:"A peer-to-peer platform for book lovers to rent, buy, and exchange used books directly with each other."}),t.jsxs("div",{className:"flex space-x-4",children:[t.jsx("a",{href:"#",className:"text-gray-300 hover:text-beige-500",children:t.jsx(we,{className:"h-5 w-5"})}),t.jsx("a",{href:"#",className:"text-gray-300 hover:text-beige-500",children:t.jsx(be,{className:"h-5 w-5"})}),t.jsx("a",{href:"#",className:"text-gray-300 hover:text-beige-500",children:t.jsx(ve,{className:"h-5 w-5"})})]})]}),t.jsxs("div",{className:"col-span-1",children:[t.jsx("h3",{className:"text-lg font-playfair font-medium mb-4 text-beige-500",children:"Quick Links"}),t.jsxs("ul",{className:"space-y-2 text-sm",children:[t.jsx("li",{children:t.jsx(U,{to:"/",className:"text-gray-300 hover:text-white transition duration-300",children:"Home"})}),t.jsx("li",{children:t.jsx(U,{to:"/browse",className:"text-gray-300 hover:text-white transition duration-300",children:"Browse Books"})}),t.jsx("li",{children:t.jsx(U,{to:"/how-it-works",className:"text-gray-300 hover:text-white transition duration-300",children:"How It Works"})}),t.jsx("li",{children:t.jsx(U,{to:"/add-books",className:"text-gray-300 hover:text-white transition duration-300",children:"Add a Book"})})]})]}),t.jsxs("div",{className:"col-span-1",children:[t.jsx("h3",{className:"text-lg font-playfair font-medium mb-4 text-beige-500",children:"Help & Support"}),t.jsxs("ul",{className:"space-y-2 text-sm",children:[t.jsx("li",{children:t.jsx(U,{to:"/faq",className:"text-gray-300 hover:text-white transition duration-300",children:"FAQ"})}),t.jsx("li",{children:t.jsx(U,{to:"/contact",className:"text-gray-300 hover:text-white transition duration-300",children:"Contact Us"})}),t.jsx("li",{children:t.jsx(U,{to:"/terms",className:"text-gray-300 hover:text-white transition duration-300",children:"Terms of Service"})}),t.jsx("li",{children:t.jsx(U,{to:"/privacy",className:"text-gray-300 hover:text-white transition duration-300",children:"Privacy Policy"})}),t.jsx("li",{children:t.jsx(U,{to:"/data-deletion",className:"text-gray-300 hover:text-white transition duration-300",children:"Data Deletion"})})]})]}),t.jsxs("div",{className:"col-span-1",children:[t.jsx("h3",{className:"text-lg font-playfair font-medium mb-4 text-beige-500",children:"Stay Updated"}),t.jsx("p",{className:"text-sm text-gray-300 mb-4",children:"Subscribe to our newsletter for new books and updates."}),t.jsx("form",{className:"mb-4",children:t.jsxs("div",{className:"flex",children:[t.jsx("input",{type:"email",placeholder:"Your email",className:"px-3 py-2 text-sm text-gray-900 bg-white border-0 rounded-l-md focus:ring-burgundy-500 focus:border-burgundy-500 flex-grow"}),t.jsx("button",{type:"submit",className:"px-3 py-2 text-sm text-white bg-burgundy-500 rounded-r-md hover:bg-burgundy-600 focus:outline-none",children:t.jsx(ae,{className:"h-4 w-4"})})]})})]})]}),t.jsx("div",{className:"border-t border-gray-700 mt-8 pt-6",children:t.jsxs("p",{className:"text-sm text-center text-gray-400",children:["© ",(new Date).getFullYear()," PeerBooks. All rights reserved."]})})]})}),Ts=({children:e})=>t.jsxs("div",{className:"flex flex-col min-h-screen",children:[t.jsx(Is,{}),t.jsx("main",{className:"flex-grow",children:e}),t.jsx(Ls,{})]}),Vs=({onSearch:e,onFilter:a,className:s})=>{const[r,i]=O.useState("");return t.jsx("div",{className:`w-full ${s}`,children:t.jsxs("form",{onSubmit:a=>{a.preventDefault(),e&&e(r)},className:"flex",children:[t.jsxs("div",{className:"relative flex-grow",children:[t.jsx("input",{type:"text",placeholder:"Search by title, author, or ISBN...",className:"w-full pl-10 pr-4 py-3 rounded-l-lg border-y border-l focus:ring-1 focus:ring-burgundy-500 focus:border-burgundy-500 outline-none",value:r,onChange:e=>i(e.target.value)}),t.jsx(Ne,{className:"absolute left-3 top-3.5 h-4 w-4 text-gray-400"})]}),t.jsx(za,{type:"submit",className:"rounded-l-none",children:"Search"}),a&&t.jsxs(za,{type:"button",variant:"outline",className:"ml-2 border border-gray-300 hover:bg-gray-50",onClick:a,children:[t.jsx(ke,{className:"h-4 w-4 mr-2"}),"Filters"]})]})})},Bs=({onSearch:e})=>t.jsxs("div",{className:"relative bg-gradient-to-br from-beige-500 to-beige-100 pt-8 pb-24",children:[t.jsx("div",{className:"absolute inset-0 opacity-20",children:t.jsx("div",{className:"absolute inset-0",style:{backgroundImage:"url('data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M54.627 0l.83.828-1.415 1.415L51.8 0h2.827zM5.373 0l-.83.828L5.96 2.243 8.2 0H5.374zM48.97 0l3.657 3.657-1.414 1.414L46.143 0h2.828zM11.03 0L7.372 3.657 8.787 5.07 13.857 0H11.03zm32.284 0L49.8 6.485 48.384 7.9l-7.9-7.9h2.83zm-24.596 0L12.143 6.485l1.415 1.414 7.9-7.9h-2.83zM38.384 0l3.657 3.657-1.414 1.414-3.657-3.657H38.384zm-20.83 0l-3.657 3.657 1.415 1.414L19.17 0h-1.625zM33.84 0l3.658 3.657-1.414 1.414L30 0h3.84zM1.414 0L0 1.414l3.657 3.657 1.414-1.414L1.414 0zM56.97 0l-3.657 3.657 1.414 1.414L58.385 1.414 56.97 0z' fill='%239C92AC' fill-opacity='0.4' fill-rule='evenodd'/%3E%3C/svg%3E')"}})}),t.jsx("div",{className:"container mx-auto px-4 relative z-10 mt-8",children:t.jsxs("div",{className:"max-w-3xl mx-auto text-center",children:[t.jsxs("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-playfair font-bold text-navy-800 mb-6",children:["Share the ",t.jsx("span",{className:"text-burgundy-500",children:"Joy of Reading"})," with Your Community"]}),t.jsx("p",{className:"text-lg md:text-xl text-gray-700 mb-8",children:"Rent, buy, or exchange used books directly with other readers. No middleman, just a community of book lovers sharing stories."}),t.jsxs("div",{className:"flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 justify-center mb-12",children:[t.jsx(U,{to:"/browse",children:t.jsx(za,{size:"lg",children:"Browse Books"})}),t.jsx(U,{to:"/add-books",children:t.jsx(za,{variant:"navy",size:"lg",children:"Add Your Books"})})]}),t.jsx(Vs,{className:"max-w-2xl mx-auto",onSearch:e})]})}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0",children:t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1440 100",className:"fill-white",children:t.jsx("path",{d:"M0,64L48,58.7C96,53,192,43,288,48C384,53,480,75,576,80C672,85,768,75,864,69.3C960,64,1056,64,1152,58.7C1248,53,1344,43,1392,37.3L1440,32L1440,100L1392,100C1344,100,1248,100,1152,100C1056,100,960,100,864,100C768,100,672,100,576,100C480,100,384,100,288,100C192,100,96,100,48,100L0,100Z"})})})]}),Os=()=>{const e=[{icon:re,title:"List Your Books",description:"Add your used books to our platform with details like condition, perceived value, and availability options."},{icon:se,title:"Exchange, Rent or Sell",description:"Choose how you want to share your books - swap for another book, rent it out, or sell it to another reader."},{icon:fe,title:"Connect & Arrange",description:"Chat with interested readers, agree on terms, and arrange for book handover or shipping."},{icon:Ae,title:"Rate & Review",description:"After a successful transaction, rate your experience to help build trust in our community."}];return t.jsx("section",{className:"py-16 bg-beige-100",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"text-center mb-12",children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-3",children:"How PeerBooks Works"}),t.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Our platform makes it easy to connect with fellow book lovers and share your literary treasures."})]}),t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:e.map(((e,a)=>t.jsxs("div",{className:"bg-white rounded-lg p-6 shadow-md relative",children:[t.jsx("div",{className:"absolute -top-5 left-1/2 transform -translate-x-1/2 bg-burgundy-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold",children:a+1}),t.jsxs("div",{className:"mt-4 text-center",children:[t.jsx("div",{className:"bg-beige-200 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4",children:t.jsx(e.icon,{className:"h-8 w-8 text-burgundy-500"})}),t.jsx("h3",{className:"font-playfair font-bold text-lg text-navy-700 mb-2",children:e.title}),t.jsx("p",{className:"text-gray-600 text-sm",children:e.description})]})]},a)))}),t.jsx("div",{className:"mt-12 bg-navy-500 text-white p-8 rounded-lg shadow-lg",children:t.jsxs("div",{className:"flex flex-col md:flex-row items-center",children:[t.jsxs("div",{className:"md:w-2/3 mb-6 md:mb-0 md:pr-6",children:[t.jsx("h3",{className:"text-2xl font-playfair font-bold mb-4",children:"Our Value-Matching System"}),t.jsx("p",{className:"mb-4",children:"We've created a unique system that helps match books based on perceived value, making exchanges fair and transparent:"}),t.jsxs("ul",{className:"list-disc list-inside space-y-2 text-beige-100",children:[t.jsx("li",{children:"Each book gets assigned a value (1-10) by its owner"}),t.jsx("li",{children:"Our system matches books with similar values for equitable exchanges"}),t.jsx("li",{children:"If there's a value gap, users can negotiate a small fee to balance the exchange"}),t.jsx("li",{children:"No middleman fees - all negotiations happen directly between readers"})]})]}),t.jsx("div",{className:"md:w-1/3",children:t.jsxs("div",{className:"bg-burgundy-500/20 border border-burgundy-500/50 rounded-lg p-6",children:[t.jsxs("div",{className:"flex justify-between items-center mb-4",children:[t.jsx("div",{className:"text-sm",children:"Book A: Harry Potter"}),t.jsx("div",{className:"text-sm font-bold",children:"Value: 8/10"})]}),t.jsx("div",{className:"flex items-center justify-center my-3",children:t.jsx(se,{className:"h-10 w-10 text-beige-500"})}),t.jsxs("div",{className:"flex justify-between items-center",children:[t.jsx("div",{className:"text-sm",children:"Book B: 1984"}),t.jsx("div",{className:"text-sm font-bold",children:"Value: 7/10"})]}),t.jsxs("div",{className:"mt-3 pt-3 border-t border-white/20 text-center text-sm",children:[t.jsx("span",{className:"font-medium",children:"Difference:"})," 1 point"]})]})})]})})]})})},zs=J("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground",secondary:"border-transparent bg-secondary text-secondary-foreground",destructive:"border-transparent bg-destructive text-destructive-foreground",outline:"text-foreground",burgundy:"border-transparent bg-burgundy-500 text-white",navy:"border-transparent bg-navy-500 text-white"}},defaultVariants:{variant:"default"}});function Us({className:e,variant:a,...s}){return t.jsx("div",{className:ta(zs({variant:a}),e),...s})}const Fs=({status:e="Available",nextAvailableDate:a,className:s=""})=>{const r=(()=>{switch(e){case"Available":default:return{icon:le,text:"Available",bgColor:"bg-green-100",textColor:"text-green-800",iconColor:"text-green-600"};case"Sold Out":return{icon:Ee,text:"Sold Out",bgColor:"bg-red-100",textColor:"text-red-800",iconColor:"text-red-600"};case"Rented Out":return{icon:me,text:a?`Rented (back ${a.toLocaleDateString()})`:"Rented Out",bgColor:"bg-yellow-100",textColor:"text-yellow-800",iconColor:"text-yellow-600"}}})(),i=r.icon;return t.jsxs("div",{className:`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${r.bgColor} ${r.textColor} ${s}`,children:[t.jsx(i,{className:`h-3 w-3 mr-1 ${r.iconColor}`}),r.text]})},Ms=({src:e,alt:a,className:s="",style:r,placeholder:i,onLoad:n,onError:o,loading:l="lazy",aspectRatio:c,fallbackSrc:d,fetchPriority:m})=>{const[u,h]=O.useState(!1),[x,p]=O.useState(!1),[g,f]=O.useState(e),y=O.useRef(null);O.useEffect((()=>{h(!1),p(!1),f(e)}),[e]);const j=t.jsx($a,{className:`w-full h-full ${s}`,style:{aspectRatio:c||"auto",...r}});return!x||d&&g!==d?t.jsxs("div",{className:"relative",style:{aspectRatio:c||"auto"},children:[!u&&(i||j),t.jsx("img",{ref:y,src:g,alt:a,className:`${s} ${u?"opacity-100":"opacity-0"} transition-opacity duration-300`,style:{...r,position:u?"static":"absolute",top:u?"auto":0,left:u?"auto":0,width:"100%",height:"100%"},loading:l,fetchPriority:m,onLoad:()=>{h(!0),null==n||n()},onError:()=>{p(!0),d&&g!==d?(f(d),p(!1)):null==o||o()},decoding:"async"})]}):t.jsx("div",{className:`flex items-center justify-center bg-gray-100 text-gray-400 ${s}`,style:{aspectRatio:c||"auto",...r},children:t.jsxs("div",{className:"text-center p-4",children:[t.jsx("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})}),t.jsx("p",{className:"text-xs",children:"Image not available"})]})})},Ks=({book:e,size:a="medium",alt:s,className:r="",priority:i=!1,fetchPriority:n,loading:o,...l})=>{const c={small:"h-48",medium:"h-64",large:"h-96"},d=s||(e?`${e.title}${e.author?` by ${e.author}`:""}`:"Book cover"),m=i?"eager":o||"lazy";return t.jsx(Ms,{...l,alt:d,aspectRatio:{small:"3/4",medium:"3/4",large:"3/4"}[a],className:`${c[a]} ${r}`,loading:m,fetchPriority:n,placeholder:t.jsx("div",{className:`${c[a]} bg-gray-100 animate-pulse flex items-center justify-center ${r}`,children:t.jsxs("div",{className:"text-center text-gray-400",children:[t.jsx("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})}),t.jsx("p",{className:"text-xs",children:"Loading..."})]})})})},qs=({book:e,priority:a=!1,index:s=0})=>{const{userData:r}=Va(),i=a||s<6,n=i?"high":"low",o=(null==r?void 0:r.community)&&e.ownerCommunity&&r.community===e.ownerCommunity;return t.jsx("div",{onClick:()=>window.location.href=`/books/${e.id}`,className:"block cursor-pointer",children:t.jsxs("div",{className:"book-card bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-200 "+(o?"ring-2 ring-blue-200 border-blue-300 bg-blue-50":""),children:[t.jsxs("div",{className:"relative h-48 overflow-hidden",children:[t.jsx(Ks,{src:e.imageUrl,book:{title:e.title,author:e.author},size:"small",className:"w-full h-full object-cover",priority:i,fetchPriority:n,loading:i?"eager":"lazy"}),t.jsxs("div",{className:"absolute top-2 right-2 flex flex-col gap-1",children:[t.jsx(Fs,{status:e.status,nextAvailableDate:e.nextAvailableDate}),o&&t.jsxs(Us,{className:"bg-blue-500 hover:bg-blue-600 text-white text-xs flex items-center gap-1",children:[t.jsx(te,{className:"h-3 w-3"}),"Your Community"]})]}),t.jsx("div",{className:"absolute bottom-0 left-0 w-full bg-gradient-to-t from-black/70 to-transparent p-3",children:t.jsx("div",{className:"text-white font-medium",children:(e=>{switch(e){case"For Rent":return t.jsx(Us,{className:"bg-blue-500 hover:bg-blue-600",children:"For Rent"});case"For Sale":return t.jsx(Us,{className:"bg-green-500 hover:bg-green-600",children:"For Sale"});case"For Exchange":return t.jsx(Us,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange"});case"For Rent & Sale":return t.jsxs("div",{className:"flex space-x-1",children:[t.jsx(Us,{className:"bg-blue-500 hover:bg-blue-600",children:"For Rent"}),t.jsx(Us,{className:"bg-green-500 hover:bg-green-600",children:"For Sale"})]});case"For Rent & Exchange":return t.jsxs("div",{className:"flex space-x-1",children:[t.jsx(Us,{className:"bg-blue-500 hover:bg-blue-600",children:"For Rent"}),t.jsx(Us,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange"}),t.jsx(Us,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange123"})]});case"For Sale & Exchange":return t.jsxs("div",{className:"flex space-x-1",children:[t.jsx(Us,{className:"bg-green-500 hover:bg-green-600",children:"For Sale"}),t.jsx(Us,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange"})]});case"For Rent, Sale & Exchange":return t.jsxs("div",{className:"flex space-x-1",children:[t.jsx(Us,{className:"bg-blue-500 hover:bg-blue-600",children:"For Rent"}),t.jsx(Us,{className:"bg-green-500 hover:bg-green-600",children:"For Sale"}),t.jsx(Us,{className:"bg-purple-500 hover:bg-purple-600",children:"For Exchange"})]});default:return null}})(e.availability)})})]}),t.jsxs("div",{className:"p-4",children:[t.jsx("h3",{className:"font-playfair font-medium text-lg text-navy-800 mb-1 line-clamp-1",children:e.title}),t.jsxs("p",{className:"text-gray-600 text-sm mb-2",children:["by ",e.author]}),t.jsxs("div",{className:"flex items-center justify-between text-sm",children:[t.jsxs("span",{className:"text-gray-700 flex items-center",children:[t.jsx(_e,{className:"h-3.5 w-3.5 mr-1"}),e.condition]}),t.jsxs("span",{className:"text-gray-700 flex items-center",children:[t.jsx(Ae,{className:"h-3.5 w-3.5 mr-1 text-yellow-500"}),e.ownerRating]})]}),t.jsxs("div",{className:"mt-2 text-sm text-gray-600 flex items-center",children:[t.jsx(De,{className:"h-3.5 w-3.5 mr-1"}),void 0!==e.distance&&null!==e.distance?t.jsxs("div",{className:"flex items-center flex-wrap",children:[t.jsxs("span",{className:"font-medium text-burgundy-600",children:["number"==typeof e.distance?e.distance.toFixed(1):e.distance," km away"]}),e.ownerCommunity?t.jsxs("span",{className:"ml-1 font-medium flex items-center "+(o?"text-blue-700 font-bold":"text-blue-600"),children:["• ",e.ownerCommunity]}):e.ownerLocation&&"Unknown Location"!==e.ownerLocation?t.jsxs("span",{className:"ml-1 text-gray-500",children:["• ",e.ownerLocation]}):null]}):e.ownerCoordinates?t.jsxs("div",{className:"flex items-center flex-wrap",children:[t.jsx("span",{className:"text-amber-600",children:"Distance calculation pending"}),e.ownerCommunity&&t.jsxs("span",{className:"ml-1 font-medium flex items-center "+(o?"text-blue-700 font-bold":"text-blue-600"),children:["• ",e.ownerCommunity]})]}):e.ownerCommunity?t.jsx("div",{className:"flex items-center",children:t.jsx("span",{className:"font-medium "+(o?"text-blue-700 font-bold":"text-blue-600"),children:e.ownerCommunity})}):e.ownerLocation&&"Unknown Location"!==e.ownerLocation?t.jsx("span",{children:e.ownerLocation}):e.ownerPincode?t.jsxs("span",{title:"Exact location unavailable",children:["Pincode: ",e.ownerPincode]}):t.jsx("span",{children:"Location unavailable"})]}),t.jsx("div",{className:"mt-2 pt-2 border-t border-gray-100",children:t.jsxs("div",{className:"flex items-center text-sm text-gray-600 mb-2",children:[t.jsx(xe,{className:"h-3.5 w-3.5 mr-1"}),t.jsxs("span",{children:["Owner: ",e.ownerName]})]})}),t.jsxs("div",{className:"mt-2 pt-2 border-t border-gray-100 flex justify-between items-center",children:[e.price&&t.jsxs("div",{className:"text-burgundy-600 font-semibold",children:["₹",e.price]}),e.rentalPrice&&t.jsxs("div",{className:"text-blue-600 font-semibold",children:["₹",e.rentalPrice," ",e.rentalPeriod]}),!e.price&&!e.rentalPrice&&t.jsx("div",{className:"text-purple-600 font-semibold",children:"Exchange Only"})]})]})]})})},Hs=(e={})=>{const{enabled:a=!0,maxPreloads:s=8,delayMs:t=0,pageType:r="custom",priority:i="high"}=e,n=O.useRef(new Set),o=O.useRef(null),l=O.useRef(!1);O.useEffect((()=>(a&&!l.current&&(ns(),is(),l.current=!0),()=>{o.current&&clearTimeout(o.current),is()})),[a]),O.useEffect((()=>{is(),n.current.clear()}),[r]);return{preloadBookImages:O.useCallback((e=>{if(!a||0===e.length)return;const i=()=>{const a=e.filter((e=>e.imageUrl&&!n.current.has(e.imageUrl)));if(0!==a.length)switch(a.forEach((e=>{e.imageUrl&&n.current.add(e.imageUrl)})),r){case"homepage":(e=>{const a=e.slice(0,6).map(((e,a)=>({url:e.imageUrl,priority:a<3?"high":"low",isAboveFold:a<4,sizes:ss(a<4)})));rs(a)})(a.slice(0,Math.min(6,s)).map((e=>({imageUrl:e.imageUrl,id:e.id}))));break;case"browse":(e=>{const a=e.slice(0,8).map(((e,a)=>({url:e.imageUrl,priority:a<4?"high":"low",isAboveFold:a<6,sizes:ss(a<6)})));rs(a)})(a.slice(0,Math.min(8,s)).map((e=>({imageUrl:e.imageUrl,id:e.id}))));break;default:const e=a.slice(0,s).map(((e,a)=>({url:e.imageUrl,priority:a<4?"high":"low",isAboveFold:a<6})));rs(e)}};t>0?o.current=setTimeout(i,t):i()}),[a,r,s,t]),preloadCustomImages:O.useCallback((e=>{if(!a||0===e.length)return;const r=()=>{const a=e.filter((e=>!n.current.has(e.url)));if(0===a.length)return;a.forEach((e=>{n.current.add(e.url)}));const t=a.map((e=>({...e,priority:e.priority||i})));rs(t.slice(0,s))};t>0?o.current=setTimeout(r,t):r()}),[a,s,t,i]),clearPreloads:O.useCallback((()=>{is(),n.current.clear(),o.current&&(clearTimeout(o.current),o.current=null)}),[]),isEnabled:a}},Gs=(e,a=!0)=>{const{preloadBookImages:s}=Hs({enabled:a,pageType:"browse",maxPreloads:8,delayMs:50});O.useEffect((()=>{e.length>0&&s(e)}),[e,s])},$s=({books:e})=>(((e,a=!0)=>{const{preloadBookImages:s}=Hs({enabled:a,pageType:"homepage",maxPreloads:6,delayMs:100});O.useEffect((()=>{e.length>0&&s(e)}),[e,s])})(e),t.jsx("section",{className:"py-16 bg-white",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:[t.jsxs("div",{children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Featured Books"}),t.jsx("p",{className:"text-gray-600",children:"Explore our community's most interesting finds"})]}),t.jsx(U,{to:"/browse",children:t.jsxs(Ha,{variant:"link",className:"mt-2 md:mt-0",children:["View All Books",t.jsx(Pe,{className:"ml-1 h-4 w-4"})]})})]}),e.length>0?t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:e.map(((e,a)=>t.jsx(qs,{book:e,index:a,priority:a<4},e.id)))}):t.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[t.jsx(je,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),t.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Featured Books Available Yet"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),t.jsx(U,{to:"/add-books",children:t.jsx(Ha,{children:"Add Your Books"})})]})]})})),Ws=({results:e,query:a,loading:s,onClearSearch:r})=>s?t.jsx("div",{className:"py-8",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsxs("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:['Searching for "',a,'"...']}),t.jsxs(za,{variant:"ghost",size:"sm",onClick:r,children:[t.jsx(X,{className:"h-4 w-4 mr-2"}),"Clear Search"]})]}),t.jsx("div",{className:"flex justify-center items-center py-12",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-burgundy-500"})})]})}):0===e.length?t.jsx("div",{className:"py-8",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsxs("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:['Search Results for "',a,'"']}),t.jsxs(za,{variant:"ghost",size:"sm",onClick:r,children:[t.jsx(X,{className:"h-4 w-4 mr-2"}),"Clear Search"]})]}),t.jsxs("div",{className:"bg-beige-50 rounded-lg p-8 text-center",children:[t.jsx(Ne,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),t.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Found"}),t.jsxs("p",{className:"text-gray-600 mb-6",children:["We couldn't find any books matching \"",a,'". Try a different search term or browse all books.']}),t.jsxs("div",{className:"flex flex-col sm:flex-row justify-center gap-4",children:[t.jsx(za,{onClick:r,children:"Try Another Search"}),t.jsx(U,{to:"/browse",children:t.jsx(za,{variant:"outline",children:"Browse All Books"})})]})]})]})}):t.jsx("div",{className:"py-8",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex justify-between items-center mb-6",children:[t.jsxs("div",{children:[t.jsxs("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:['Search Results for "',a,'"']}),t.jsxs("p",{className:"text-gray-600",children:["Found ",e.length," ",1===e.length?"book":"books"," matching your search"]})]}),t.jsxs(za,{variant:"ghost",size:"sm",onClick:r,children:[t.jsx(X,{className:"h-4 w-4 mr-2"}),"Clear Search"]})]}),t.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:e.map((e=>t.jsx(qs,{book:e},e.id)))}),t.jsxs("div",{className:"mt-8 text-center",children:[t.jsx("p",{className:"text-gray-600 mb-4",children:"Want to see more books?"}),t.jsx(U,{to:"/browse",children:t.jsx(za,{variant:"outline",children:"Browse All Books"})})]})]})}),Js=()=>{const{userData:e}=Va(),[a,s]=O.useState([]),[r,i]=O.useState(!0),[n,o]=O.useState(""),[l,c]=O.useState([]),[d,m]=O.useState(!1);O.useEffect((()=>{i(!0);(async()=>{try{const a=null==e?void 0:e.community,t=await Ns(!1,a);if(0===t.length)return void s([]);const r=t.slice(0,8);r.some((e=>void 0!==e.distance)),a&&r.some((e=>e.ownerCommunity===a));s(r)}catch(a){s([]),a instanceof Error&&a.stack}finally{i(!1)}})()}),[]);return t.jsxs(Ts,{children:[t.jsx(Bs,{onSearch:async e=>{if(e.trim()){o(e),m(!0);try{const a=await Ps(e);c(a)}catch(a){Q.error("An error occurred while searching. Please try again."),c([])}finally{m(!1)}}else Q.error("Please enter a search term")}}),n?t.jsx(Ws,{results:l,query:n,loading:d,onClearSearch:()=>{o(""),c([])}}):t.jsxs(t.Fragment,{children:[r?t.jsx("section",{className:"py-16 bg-white",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-8",children:t.jsxs("div",{children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Featured Books"}),t.jsx("p",{className:"text-gray-600",children:"Explore our community's most interesting finds"})]})}),t.jsx(Qa,{count:8})]})}):t.jsx($s,{books:a}),t.jsx(Os,{})]}),t.jsx("section",{className:"py-16 bg-white",children:t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"text-center mb-12",children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-navy-800 mb-3",children:"What Our Community Says"}),t.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Hear from readers who've found their next favorite book through our platform."})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[t.jsxs("div",{className:"bg-beige-100 rounded-lg p-6",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx("div",{className:"bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium",children:"AP"}),t.jsxs("div",{className:"ml-3",children:[t.jsx("h4",{className:"font-medium text-navy-800",children:"Ankit Patel"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Mumbai"})]})]}),t.jsx("p",{className:"text-gray-700 italic",children:'"I\'ve exchanged over 15 books on PeerBooks and discovered authors I would have never found otherwise. The value-matching system makes exchanges so easy and fair!"'})]}),t.jsxs("div",{className:"bg-beige-100 rounded-lg p-6",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx("div",{className:"bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium",children:"SR"}),t.jsxs("div",{className:"ml-3",children:[t.jsx("h4",{className:"font-medium text-navy-800",children:"Sneha Reddy"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Bangalore"})]})]}),t.jsx("p",{className:"text-gray-700 italic",children:'"As a student, I couldn\'t afford to buy all the books I wanted to read. PeerBooks has been a game changer - I can rent books affordably or exchange mine for new reads."'})]}),t.jsxs("div",{className:"bg-beige-100 rounded-lg p-6",children:[t.jsxs("div",{className:"flex items-center mb-4",children:[t.jsx("div",{className:"bg-burgundy-500 rounded-full w-10 h-10 flex items-center justify-center text-white font-medium",children:"RK"}),t.jsxs("div",{className:"ml-3",children:[t.jsx("h4",{className:"font-medium text-navy-800",children:"Rahul Khanna"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Delhi"})]})]}),t.jsx("p",{className:"text-gray-700 italic",children:'"I love the community aspect of PeerBooks. I\'ve met several fellow readers in my neighborhood and we now have regular book club meetups thanks to this platform!"'})]})]})]})}),t.jsx("section",{className:"py-16 bg-burgundy-500",children:t.jsxs("div",{className:"container mx-auto px-4 text-center",children:[t.jsx("h2",{className:"text-3xl font-playfair font-bold text-white mb-4",children:"Ready to Join Our Community?"}),t.jsx("p",{className:"text-beige-100 mb-8 max-w-2xl mx-auto",children:"Start sharing your books with fellow readers and discover your next great read today."}),t.jsxs("div",{className:"flex flex-col md:flex-row justify-center space-y-4 md:space-y-0 md:space-x-4",children:[t.jsx(U,{to:"/join",children:t.jsx("button",{className:"bg-white text-burgundy-500 hover:bg-beige-100 font-medium px-6 py-3 rounded-md shadow-md transition-colors",children:"Sign Up Now"})}),t.jsx(U,{to:"/how-it-works",children:t.jsx("button",{className:"bg-transparent border border-white text-white hover:bg-burgundy-600 font-medium px-6 py-3 rounded-md shadow-md transition-colors",children:"Learn More"})})]})]})})]})},Ys=O.forwardRef((({className:e,type:a,...s},r)=>t.jsx("input",{type:a,className:ta("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...s})));Ys.displayName="Input";const Qs=J("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),Zs=O.forwardRef((({className:e,...a},s)=>t.jsx(V,{ref:s,className:ta(Qs(),e),...a})));Zs.displayName=V.displayName;const Xs=Oe,et=O.createContext({}),at=({...e})=>t.jsx(et.Provider,{value:{name:e.name},children:t.jsx(ze,{...e})}),st=()=>{const e=O.useContext(et),a=O.useContext(tt),{getFieldState:s,formState:t}=Ue(),r=s(e.name,t);if(!e)throw new Error("useFormField should be used within <FormField>");const{id:i}=a;return{id:i,name:e.name,formItemId:`${i}-form-item`,formDescriptionId:`${i}-form-item-description`,formMessageId:`${i}-form-item-message`,...r}},tt=O.createContext({}),rt=O.forwardRef((({className:e,...a},s)=>{const r=O.useId();return t.jsx(tt.Provider,{value:{id:r},children:t.jsx("div",{ref:s,className:ta("space-y-2",e),...a})})}));rt.displayName="FormItem";const it=O.forwardRef((({className:e,...a},s)=>{const{error:r,formItemId:i}=st();return t.jsx(Zs,{ref:s,className:ta(r&&"text-destructive",e),htmlFor:i,...a})}));it.displayName="FormLabel";const nt=O.forwardRef((({...e},a)=>{const{error:s,formItemId:r,formDescriptionId:i,formMessageId:n}=st();return t.jsx(p,{ref:a,id:r,"aria-describedby":s?`${i} ${n}`:`${i}`,"aria-invalid":!!s,...e})}));nt.displayName="FormControl";const ot=O.forwardRef((({className:e,...a},s)=>{const{formDescriptionId:r}=st();return t.jsx("p",{ref:s,id:r,className:ta("text-sm text-muted-foreground",e),...a})}));ot.displayName="FormDescription";const lt=O.forwardRef((({className:e,children:a,...s},r)=>{const{error:i,formMessageId:n}=st(),o=i?String(null==i?void 0:i.message):a;return o?t.jsx("p",{ref:r,id:n,className:ta("text-sm font-medium text-destructive",e),...s,children:o}):null}));lt.displayName="FormMessage";var ct=function(e,a){for(var s={};e.length;){var t=e[0],r=t.code,i=t.message,n=t.path.join(".");if(!s[n])if("unionErrors"in t){var o=t.unionErrors[0].errors[0];s[n]={message:o.message,type:o.code}}else s[n]={message:i,type:r};if("unionErrors"in t&&t.unionErrors.forEach((function(a){return a.errors.forEach((function(a){return e.push(a)}))})),a){var l=s[n].types,c=l&&l[t.code];s[n]=Ke(n,a,s,r,c?[].concat(c,t.message):t.message)}e.shift()}return s},dt=function(e,a,s){return void 0===s&&(s={}),function(t,r,i){try{return Promise.resolve(function(r,n){try{var o=Promise.resolve(e["sync"===s.mode?"parse":"parseAsync"](t,a)).then((function(e){return i.shouldUseNativeValidation&&Fe({},i),{errors:{},values:s.raw?t:e}}))}catch(l){return n(l)}return o&&o.then?o.then(void 0,n):o}(0,(function(e){if(a=e,Array.isArray(null==a?void 0:a.errors))return{values:{},errors:Me(ct(e.errors,!i.shouldUseNativeValidation&&"all"===i.criteriaMode),i)};var a;throw e})))}catch(n){return Promise.reject(n)}}};const mt=Z.object({email:Z.string().email({message:"Please enter a valid email address"}),password:Z.string().min(6,{message:"Password must be at least 6 characters"})}),ut=()=>{const e=K(),[a,s]=O.useState(!1),{signIn:r}=Va(),i=qe({resolver:dt(mt),defaultValues:{email:"",password:""}});return t.jsx(Ts,{children:t.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[t.jsxs("div",{className:"text-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Sign In"}),t.jsx("p",{className:"text-gray-600",children:"Access your PeerBooks account"})]}),t.jsx(Xs,{...i,children:t.jsxs("form",{onSubmit:i.handleSubmit((async a=>{s(!0);try{if(!(await r(a.email,a.password)).emailVerified)return Q.warning("Please verify your email before signing in."),void e("/");Q.success("Signed in successfully!"),e("/dashboard")}catch(t){const e=t instanceof Error?t.message:"Failed to sign in";Q.error(e)}finally{s(!1)}})),className:"space-y-4",children:[t.jsx(at,{control:i.control,name:"email",render:({field:e})=>t.jsxs(rt,{children:[t.jsx(it,{children:"Email"}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"<EMAIL>",type:"email",disabled:a,...e})}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"password",render:({field:e})=>t.jsxs(rt,{children:[t.jsx(it,{children:"Password"}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"••••••••",type:"password",disabled:a,...e})}),t.jsx(lt,{})]})}),t.jsx("div",{className:"text-right",children:t.jsx(U,{to:"/forgot-password",className:"text-sm text-burgundy-500 hover:underline",children:"Forgot password?"})}),t.jsxs(za,{type:"submit",className:"w-full flex items-center justify-center gap-2",disabled:a,children:[t.jsx(Se,{className:"h-4 w-4"}),a?"Signing in...":"Sign In"]})]})}),t.jsxs("div",{className:"text-center mt-6",children:[t.jsxs("p",{className:"text-gray-600",children:["Don't have an account? "," ",t.jsx(U,{to:"/join",className:"text-burgundy-500 hover:underline font-medium",children:"Join Now"})]}),t.jsxs("p",{className:"text-xs text-gray-500 mt-4",children:["By signing in, you agree to our"," ",t.jsx(U,{to:"/terms",className:"text-burgundy-500 hover:underline",children:"Terms of Service"})," ","and"," ",t.jsx(U,{to:"/privacy",className:"text-burgundy-500 hover:underline",children:"Privacy Policy"}),"."]})]})]})})})},ht=["Andhra Pradesh","Arunachal Pradesh","Assam","Bihar","Chhattisgarh","Goa","Gujarat","Haryana","Himachal Pradesh","Jharkhand","Karnataka","Kerala","Madhya Pradesh","Maharashtra","Manipur","Meghalaya","Mizoram","Nagaland","Odisha","Punjab","Rajasthan","Sikkim","Tamil Nadu","Telangana","Tripura","Uttar Pradesh","Uttarakhand","West Bengal","Andaman and Nicobar Islands","Chandigarh","Dadra and Nagar Haveli and Daman and Diu","Delhi","Jammu and Kashmir","Ladakh","Lakshadweep","Puducherry"],xt={"Andhra Pradesh":["Visakhapatnam","Vijayawada","Guntur","Nellore","Kurnool","Rajahmundry","Tirupati","Kakinada","Kadapa","Anantapur"],"Arunachal Pradesh":["Itanagar","Naharlagun","Pasighat","Namsai","Tezu","Bomdila","Tawang","Ziro","Roing","Along"],Assam:["Guwahati","Silchar","Dibrugarh","Jorhat","Nagaon","Tinsukia","Tezpur","Karimganj","Diphu","Goalpara"],Bihar:["Patna","Gaya","Bhagalpur","Muzaffarpur","Darbhanga","Arrah","Begusarai","Chhapra","Katihar","Munger"],Chhattisgarh:["Raipur","Bhilai","Bilaspur","Korba","Durg","Rajnandgaon","Jagdalpur","Ambikapur","Chirmiri","Dhamtari"],Goa:["Panaji","Margao","Vasco da Gama","Mapusa","Ponda","Bicholim","Curchorem","Sanquelim","Canacona","Quepem"],Gujarat:["Ahmedabad","Surat","Vadodara","Rajkot","Bhavnagar","Jamnagar","Junagadh","Gandhinagar","Anand","Navsari"],Haryana:["Faridabad","Gurgaon","Panipat","Ambala","Yamunanagar","Rohtak","Hisar","Karnal","Sonipat","Panchkula"],"Himachal Pradesh":["Shimla","Mandi","Solan","Dharamshala","Baddi","Nahan","Kullu","Palampur","Hamirpur","Una"],Jharkhand:["Ranchi","Jamshedpur","Dhanbad","Bokaro","Hazaribagh","Deoghar","Giridih","Ramgarh","Phusro","Chirkunda"],Karnataka:["Bangalore","Mysore","Hubli","Mangalore","Belgaum","Gulbarga","Davanagere","Bellary","Bijapur","Shimoga"],Kerala:["Thiruvananthapuram","Kochi","Kozhikode","Thrissur","Kollam","Kannur","Alappuzha","Kottayam","Palakkad","Malappuram"],"Madhya Pradesh":["Indore","Bhopal","Jabalpur","Gwalior","Ujjain","Sagar","Dewas","Satna","Ratlam","Rewa"],Maharashtra:["Mumbai","Pune","Nagpur","Thane","Nashik","Aurangabad","Solapur","Kolhapur","Amravati","Navi Mumbai"],Manipur:["Imphal","Thoubal","Bishnupur","Kakching","Ukhrul","Churachandpur","Senapati","Tamenglong","Chandel","Jiribam"],Meghalaya:["Shillong","Tura","Jowai","Nongstoin","Baghmara","Williamnagar","Resubelpara","Ampati","Khliehriat","Mawkyrwat"],Mizoram:["Aizawl","Lunglei","Champhai","Serchhip","Kolasib","Lawngtlai","Saiha","Mamit","Khawzawl","Hnahthial"],Nagaland:["Kohima","Dimapur","Mokokchung","Tuensang","Wokha","Zunheboto","Mon","Phek","Kiphire","Longleng"],Odisha:["Bhubaneswar","Cuttack","Rourkela","Berhampur","Sambalpur","Puri","Balasore","Bhadrak","Baripada","Jharsuguda"],Punjab:["Ludhiana","Amritsar","Jalandhar","Patiala","Bathinda","Mohali","Pathankot","Hoshiarpur","Batala","Moga"],Rajasthan:["Jaipur","Jodhpur","Kota","Bikaner","Ajmer","Udaipur","Bhilwara","Alwar","Sikar","Sri Ganganagar"],Sikkim:["Gangtok","Namchi","Mangan","Gyalshing","Rangpo","Singtam","Jorethang","Nayabazar","Ravangla","Soreng"],"Tamil Nadu":["Chennai","Coimbatore","Madurai","Tiruchirappalli","Salem","Tirunelveli","Tiruppur","Vellore","Erode","Thoothukudi"],Telangana:["Hyderabad","Warangal","Nizamabad","Karimnagar","Khammam","Ramagundam","Mahbubnagar","Nalgonda","Adilabad","Suryapet"],Tripura:["Agartala","Udaipur","Dharmanagar","Kailashahar","Belonia","Khowai","Ambassa","Sabroom","Santirbazar","Teliamura"],"Uttar Pradesh":["Lucknow","Kanpur","Ghaziabad","Agra","Varanasi","Meerut","Allahabad","Bareilly","Aligarh","Moradabad"],Uttarakhand:["Dehradun","Haridwar","Roorkee","Haldwani","Rudrapur","Kashipur","Rishikesh","Pithoragarh","Ramnagar","Khatima"],"West Bengal":["Kolkata","Asansol","Siliguri","Durgapur","Bardhaman","Malda","Baharampur","Habra","Kharagpur","Shantipur"],"Andaman and Nicobar Islands":["Port Blair","Mayabunder","Diglipur","Rangat","Havelock Island","Car Nicobar","Little Andaman","Neil Island","Kamorta","Campbell Bay"],Chandigarh:["Chandigarh"],"Dadra and Nagar Haveli and Daman and Diu":["Silvassa","Daman","Diu","Dadra","Naroli","Vapi","Amli","Khanvel","Dunetha","Samarvarni"],Delhi:["New Delhi","Delhi","Dwarka","Rohini","Pitampura","Janakpuri","Vasant Kunj","Saket","Mayur Vihar","Laxmi Nagar"],"Jammu and Kashmir":["Srinagar","Jammu","Anantnag","Baramulla","Kathua","Sopore","Udhampur","Poonch","Kupwara","Pulwama"],Ladakh:["Leh","Kargil","Diskit","Zanskar","Nubra","Drass","Khalsi","Nyoma","Skurbuchan","Sankoo"],Lakshadweep:["Kavaratti","Agatti","Amini","Andrott","Minicoy","Kalpeni","Kiltan","Kadmat","Chetlat","Bitra"],Puducherry:["Puducherry","Karaikal","Yanam","Mahe","Ozhukarai","Villianur","Ariyankuppam","Bahour","Mannadipet","Nettapakkam"]},pt=["Aashirwad Apartments","Akshaya Towers","Anand Vihar","Ashoka Enclave","Bharat Residency","Chandan Heights","Daffodil Gardens","Ekta Apartments","Evergreen Residency","Ganga Vihar","Golden Palms","Green Valley","Harmony Heights","Indraprastha Towers","Jeevan Apartments","Kailash Heights","Krishna Gardens","Lakshmi Nivas","Lotus Apartments","Mahalaxmi Towers","Malabar Heights","Narmada Residency","Nilgiri Apartments","Ocean View","Orchid Gardens","Palm Grove","Paradise Apartments","Pearl Heights","Prestige Towers","Radha Kunj","Rajhans Residency","Royal Palms","Sagar Apartments","Saraswati Kunj","Shanti Niketan","Shivam Apartments","Silver Oaks","Sunrise Apartments","Swapna Lok","Tulsi Gardens","Usha Kiran","Vaibhav Apartments","Vaishnavi Towers","Vasant Vihar","Vinayak Heights","Yamuna Apartments","Zenith Towers"];function gt({options:e,value:a,onChange:s,placeholder:r="Select an option",emptyMessage:i="No results found.",disabled:n=!1,className:o,triggerClassName:l}){const[c,d]=O.useState(!1),[m,u]=O.useState(""),h=O.useRef(null);O.useEffect((()=>{}),[e,a,r,n,c]),O.useEffect((()=>{const e=e=>{h.current&&!h.current.contains(e.target)&&d(!1)};return c&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[c]);const x=O.useMemo((()=>m?(e||[]).filter((e=>e.label.toLowerCase().includes(m.toLowerCase()))):e||[]),[e,m]),p=O.useMemo((()=>(e||[]).find((e=>e.value===a))),[e,a]);return t.jsxs("div",{className:"relative w-full",ref:h,children:[t.jsxs(za,{type:"button",variant:"outline",role:"combobox","aria-expanded":c,disabled:n,onClick:()=>{n||d(!c)},className:ta("w-full justify-between font-normal",!a&&"text-muted-foreground",n&&"opacity-50 cursor-not-allowed",l),children:[p?p.label:r,t.jsx(Ce,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]}),c&&t.jsxs("div",{className:"absolute z-50 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200",children:[t.jsxs("div",{className:"flex items-center border-b px-3 py-2",children:[t.jsx(Ne,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),t.jsx("input",{placeholder:`Search ${r.toLowerCase()}...`,className:"flex h-9 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",value:m,onChange:e=>u(e.target.value),autoFocus:!0})]}),t.jsx("div",{className:"max-h-[300px] overflow-y-auto p-1",children:0===x.length?t.jsx("div",{className:"py-6 text-center text-sm",children:i}):x.map((e=>t.jsxs("div",{className:ta("relative flex cursor-pointer select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none hover:bg-gray-100",a===e.value?"bg-gray-100 font-medium":""),onClick:()=>{s(e.value),d(!1),u("")},children:[t.jsx(ne,{className:ta("mr-2 h-4 w-4",a===e.value?"opacity-100":"opacity-0")}),e.label]},e.value)))})]})]})}const ft=function(e,a){let s=null;return function(...t){return new Promise(((r,i)=>{s&&clearTimeout(s),s=setTimeout((()=>{e(...t).then(r).catch(i)}),a)}))}}((async(e,a=100)=>{if(!e||e.length<6)return[];try{await Na();const{collection:t,query:r,where:i,limit:n,getDocs:o,getFirestore:l}=await We((async()=>{const{collection:e,query:a,where:s,limit:t,getDocs:r,getFirestore:i}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,query:a,where:s,limit:t,getDocs:r,getFirestore:i}}),__vite__mapDeps([0,1,2])),c=l(),d=parseInt(e,10),m=[r(t(c,"hyderabadProperties"),i("pincode","==",d),n(a)),r(t(c,"hyderabadProperties"),i("pincode","==",e),n(a))],u=new Set;let h=0;for(let e=0;e<m.length;e++){try{const a=await o(m[e]),s=a.size;h+=s,s>0&&a.forEach((e=>{const a=e.data();a.communityName&&"string"==typeof a.communityName&&u.add(a.communityName.trim())}))}catch(s){}}return Array.from(u).sort()}catch(t){return[]}}),500),yt=Z.object({name:Z.string().min(2,{message:"Name must be at least 2 characters"}),email:Z.string().email({message:"Please enter a valid email address"}),phone:Z.string().min(10,{message:"Please enter a valid phone number"}).max(15),address:Z.string().min(3,{message:"Please enter your address"}),apartment:Z.string().optional().default(""),city:Z.string().min(2,{message:"Please select your city"}),state:Z.string().min(2,{message:"Please select your state"}),pincode:Z.string().min(6,{message:"Please enter a valid pincode"}).max(6),community:Z.string().min(1,{message:"Please select or enter your community"}),customCommunity:Z.string().optional(),password:Z.string().min(6,{message:"Password must be at least 6 characters"}),confirmPassword:Z.string()}).refine((e=>e.password===e.confirmPassword),{message:"Passwords don't match",path:["confirmPassword"]}).refine((e=>"Other"!==e.community||e.customCommunity&&e.customCommunity.length>0),{message:"Please enter your community name",path:["customCommunity"]}),jt=()=>{const e=K(),[a,s]=O.useState(!1),{signUp:r}=Va(),i=qe({resolver:dt(yt),defaultValues:{name:"",email:"",password:"",confirmPassword:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",community:"",customCommunity:""}}),[n,o]=O.useState(""),[l,c]=O.useState([]),[d,m]=O.useState([]),[u,h]=O.useState([]),[x,p]=O.useState([]),[g,f]=O.useState(!1),[y,j]=O.useState(null);O.useMemo((()=>pt.map((e=>({value:e,label:e})))),[]);const w=O.useMemo((()=>ht.map((e=>({value:e,label:e})))),[]),b=He({control:i.control,name:"pincode"});O.useEffect((()=>{(async()=>{if(!b||b.length<6)return h([]),p([]),void j(null);if(6===b.length){f(!0),j(null);try{const e=await ft(b);h(e);const a=e.map((e=>({value:e,label:e})));a.push({value:"Other",label:"Other (Enter manually)"}),p(a),0===e.length&&j("No communities found for this pincode. Please select 'Other' and enter manually.")}catch(e){j("Failed to fetch communities. Please try again or enter manually.")}finally{f(!1)}}})()}),[b]);return t.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:t.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8",children:[t.jsxs("div",{className:"text-center mb-6",children:[t.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Join PeerBooks"}),t.jsx("p",{className:"text-gray-600",children:"Create your account to start exchanging books"}),t.jsxs("p",{className:"mt-2 text-burgundy-600 font-medium",children:["All fields marked with ",t.jsx("span",{className:"text-red-500",children:"*"})," are mandatory"]})]}),t.jsx(Xs,{...i,children:t.jsxs("form",{onSubmit:i.handleSubmit((async a=>{s(!0);try{const s="Other"===a.community?a.customCommunity:a.community;await r(a.email,a.password,a.name,{phone:a.phone,address:a.address,apartment:a.apartment||"",city:a.city,state:a.state,pincode:a.pincode,community:s});Q.success("Account created successfully! Please check your email for verification."),e("/verify-email")}catch(t){const e=t instanceof Error?t.message:"Failed to create account";Q.error(e)}finally{s(!1)}})),className:"space-y-4",children:[t.jsx(at,{control:i.control,name:"name",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["Full Name ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"John Doe",disabled:a,...e})}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"email",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["Email ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"<EMAIL>",type:"email",disabled:a,...e})}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"phone",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["Phone Number ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"+91 **********",type:"tel",disabled:a,...e})}),t.jsx(ot,{children:"For verification and contact purposes"}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"password",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["Password ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"••••••••",type:"password",disabled:a,...e})}),t.jsx(ot,{children:"At least 6 characters"}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"confirmPassword",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["Confirm Password ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"••••••••",type:"password",disabled:a,...e})}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"address",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["Address ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"Enter your street address",disabled:a,...e})}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"state",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["State ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(gt,{options:w,value:e.value||"",onChange:e=>{(e=>{if(o(e),i.setValue("state",e),i.setValue("city",""),e&&xt[e]){const a=xt[e];c(a);const s=a.map((e=>({value:e,label:e})));m(s)}else c([]),m([])})(e)},placeholder:"Search or select state",disabled:a,emptyMessage:"No states found"})}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"city",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["City ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(gt,{options:d,value:e.value||"",onChange:a=>{e.onChange(a)},placeholder:n?"Search or select city":"Select state first",disabled:a||!n,emptyMessage:n?"No cities found":"Please select a state first"})}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"pincode",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["Pincode ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"Enter 6-digit pincode",disabled:a,maxLength:6,...e})}),t.jsx(ot,{children:"Enter your 6-digit pincode to find your community"}),t.jsx(lt,{})]})}),t.jsx(at,{control:i.control,name:"community",render:({field:e})=>(e.value,t.jsxs(rt,{children:[t.jsxs(it,{children:["Community ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsxs("div",{className:"relative",children:[t.jsx(gt,{options:x,value:e.value||"",onChange:a=>{e.onChange(a),"Other"!==a&&i.setValue("customCommunity","")},placeholder:6===(null==b?void 0:b.length)?"Select your community":"Enter pincode first",disabled:a||!b||b.length<6||g,emptyMessage:y||"No communities found. Select 'Other' to enter manually."}),g&&t.jsx("div",{className:"absolute right-10 top-3",children:t.jsx(Re,{className:"h-4 w-4 animate-spin text-gray-500"})})]})}),t.jsx(ot,{children:'Select your community or choose "Other" to enter manually'}),t.jsx(lt,{})]}))}),"Other"===i.watch("community")&&t.jsx(at,{control:i.control,name:"customCommunity",render:({field:e})=>t.jsxs(rt,{children:[t.jsxs(it,{children:["Enter Community Name ",t.jsx("span",{className:"text-red-500",children:"*"})]}),t.jsx(nt,{children:t.jsx(Ys,{placeholder:"Enter your community name",disabled:a,...e})}),t.jsx(lt,{})]})}),t.jsxs(za,{type:"submit",className:"w-full flex items-center justify-center gap-2",disabled:a,children:[t.jsx(Ie,{className:"h-4 w-4"}),a?"Creating Account...":"Create Account & Verify Email"]})]})}),t.jsx("div",{className:"text-center mt-6",children:t.jsxs("p",{className:"text-gray-600",children:["Already have an account? "," ",t.jsx(U,{to:"/signin",className:"text-burgundy-500 hover:underline font-medium",children:"Sign In"})]})})]})})},wt=()=>t.jsx(jt,{}),bt=()=>t.jsx("div",{className:"flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4",children:t.jsxs("div",{className:"text-center max-w-md",children:[t.jsx("div",{className:"flex justify-center mb-6",children:t.jsx("div",{className:"bg-burgundy-100 p-4 rounded-full",children:t.jsx(re,{className:"h-12 w-12 text-burgundy-500"})})}),t.jsx("h1",{className:"text-4xl font-bold font-playfair text-navy-800 mb-2",children:"Page Not Found"}),t.jsx("p",{className:"text-lg text-gray-600 mb-6",children:"The page you're looking for doesn't seem to exist. It may have been moved or deleted."}),t.jsxs("div",{className:"space-y-4",children:[t.jsx(U,{to:"/",children:t.jsx(za,{className:"w-full",children:"Return to Home"})}),t.jsx("p",{className:"text-gray-500 text-sm",children:"Lost? Try searching for books or browsing our categories."})]})]})}),vt=()=>t.jsxs("div",{className:"min-h-screen flex flex-col",children:[t.jsx(Is,{}),t.jsx("main",{className:"flex-grow flex items-center justify-center",children:t.jsxs("div",{className:"max-w-md w-full mx-auto p-8 bg-white rounded-lg shadow-lg text-center",children:[t.jsx(Le,{className:"h-16 w-16 text-burgundy-500 mx-auto mb-4"}),t.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Access Denied"}),t.jsx("p",{className:"text-gray-600 mb-6",children:"You don't have permission to access this page. This area is restricted to administrators only."}),t.jsxs("div",{className:"flex flex-col space-y-2",children:[t.jsx(U,{to:"/",children:t.jsx(Ha,{className:"w-full",children:"Return to Home"})}),t.jsx(U,{to:"/browse",children:t.jsx(Ha,{variant:"outline",className:"w-full",children:"Browse Books"})})]})]})}),t.jsx(Ls,{})]}),Nt=z.lazy((()=>We((()=>import("./BookDetail-BTCE1QP9.js")),__vite__mapDeps([4,1,2,5,6,7,8,9,10])))),kt=z.lazy((()=>We((()=>import("./MyBooks-Dos5Zzm6.js")),__vite__mapDeps([11,1,2,12,7,5,8,9,10])))),At=z.lazy((()=>We((()=>import("./AddBooks-CR18lQrp.js")),__vite__mapDeps([13,1,2,14,5,10,6,7,8,9])))),Et=z.lazy((()=>We((()=>import("./BrowseBooks--qi3dfRY.js")),__vite__mapDeps([15,1,2,12,7,5,8,9,10])))),_t=z.lazy((()=>We((()=>import("./UserAccount-C57ZbS7H.js")),__vite__mapDeps([16,1,2,5,7,8,9,10])))),Dt=z.lazy((()=>We((()=>import("./AdminDashboard-B0LoSn53.js")),__vite__mapDeps([17,1,2,18,7,5,8,9,10])))),Pt=z.lazy((()=>We((()=>import("./AdminBookApprovals-CWuE8Tls.js")),__vite__mapDeps([19,1,2,5,20,7,14,18,8,9,10])))),St=z.lazy((()=>We((()=>import("./AdminUsers-CoQc7clh.js")),__vite__mapDeps([21,1,2,5,20,7,12,10,18,8,9])))),Ct=z.lazy((()=>We((()=>import("./AdminUtilities-CIRlSmt8.js")),__vite__mapDeps([22,1,2,5,20,7,18,8,9,10])))),Rt=z.lazy((()=>We((()=>import("./AdminSettings-DmsLbfc7.js")),__vite__mapDeps([23,1,2,5,18,7,24,8,9,10])))),It=z.lazy((()=>We((()=>import("./AdminContactMessages-CN10jo9h.js")),__vite__mapDeps([25,1,2,9,26,20,7,18,5,8,10])))),Lt=z.lazy((()=>We((()=>import("./ForgotPassword-hGqzlF5L.js")),__vite__mapDeps([27,1,2,5,10,7,8,9])))),Tt=z.lazy((()=>We((()=>import("./VerifyEmail-BPCEGmGb.js")),__vite__mapDeps([28,1,2,5,7,8,9,10])))),Vt=z.lazy((()=>We((()=>import("./HowItWorks-ChAd511g.js")),__vite__mapDeps([29,1,2,7,5,8,9,10])))),Bt=z.lazy((()=>We((()=>import("./FAQ-Bli8LLd2.js")),__vite__mapDeps([30,1,2,7,5,8,9,10])))),Ot=z.lazy((()=>We((()=>import("./ContactUs-BVUjPO1Z.js")),__vite__mapDeps([31,1,2,10,5,26,14,7,8,9])))),zt=z.lazy((()=>We((()=>import("./Feedback-CrK9zX1t.js")),__vite__mapDeps([32,1,2,10,5,33,7,12,14,8,9])))),Ut=z.lazy((()=>We((()=>import("./PrivacyPolicy-CM8G7fgj.js")),__vite__mapDeps([34,1,2,5,7,8,9,10])))),Ft=z.lazy((()=>We((()=>import("./Terms-W54UmMdW.js")),__vite__mapDeps([35,1,2,5,7,8,9,10])))),Mt=z.lazy((()=>We((()=>import("./DataDeletion-CS_xpOM9.js")),__vite__mapDeps([36,1,2,7,5,8,9,10])))),Kt=z.lazy((()=>We((()=>import("./SeedBooks-DzqWdzsT.js")),__vite__mapDeps([37,1,2,5,7,8,9,10])))),qt=z.lazy((()=>We((()=>import("./DatabaseBooks-UqhiqPl-.js")),__vite__mapDeps([38,1,2,5,7,8,9,10])))),Ht=z.lazy((()=>We((()=>import("./AdminSetup-DWiuFNXM.js")),__vite__mapDeps([39,1,2,5,7,8,9,10])))),Gt=z.lazy((()=>We((()=>import("./AdminDiagnostic-Ct7qQys2.js")),__vite__mapDeps([40,1,2,5,7,8,9,10])))),$t=z.lazy((()=>We((()=>import("./AdminFeedback-C29VQn79.js")),__vite__mapDeps([41,1,2,9,33,7,20,24,18,5,8,10])))),Wt=new Ve,Jt=()=>t.jsx(q,{children:t.jsx(Be,{client:Wt,children:t.jsx(Ba,{children:t.jsxs(ha,{children:[t.jsx(ma,{}),t.jsx(ua,{}),t.jsx(os,{enabled:!1,showDebugInfo:!1}),t.jsxs(H,{children:[t.jsx(G,{path:"/",element:t.jsx(Js,{})}),t.jsx(G,{path:"/signin",element:t.jsx(ut,{})}),t.jsx(G,{path:"/join",element:t.jsx(wt,{})}),t.jsx(G,{path:"/forgot-password",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading forgot password page..."}),children:t.jsx(Lt,{})})}),t.jsx(G,{path:"/verify-email",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading email verification..."}),children:t.jsx(Tt,{})})}),t.jsx(G,{path:"/browse",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading books..."}),children:t.jsx(Et,{})})}),t.jsx(G,{path:"/books/:id",element:t.jsx(O.Suspense,{fallback:t.jsx(Ja,{}),children:t.jsx(Nt,{})})}),t.jsx(G,{path:"/how-it-works",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading How It Works..."}),children:t.jsx(Vt,{})})}),t.jsx(G,{path:"/faq",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading FAQ..."}),children:t.jsx(Bt,{})})}),t.jsx(G,{path:"/contact",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading Contact Us..."}),children:t.jsx(Ot,{})})}),t.jsx(G,{path:"/feedback",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading Feedback..."}),children:t.jsx(zt,{})})}),t.jsx(G,{path:"/privacy",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading Privacy Policy..."}),children:t.jsx(Ut,{})})}),t.jsx(G,{path:"/terms",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading Terms of Service..."}),children:t.jsx(Ft,{})})}),t.jsx(G,{path:"/data-deletion",element:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading Data Deletion..."}),children:t.jsx(Mt,{})})}),t.jsx(G,{path:"/dashboard",element:t.jsx(Fa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your dashboard",verificationMessage:"You need to verify your email address before you can access your dashboard and manage your books.",children:t.jsx(O.Suspense,{fallback:t.jsx(Ya,{}),children:t.jsx(_t,{})})})}),t.jsx(G,{path:"/profile",element:t.jsx(Fa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your profile",children:t.jsx(_t,{})})}),t.jsx(G,{path:"/my-books",element:t.jsx(Fa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your books",children:t.jsx(O.Suspense,{fallback:t.jsx(Ya,{}),children:t.jsx(kt,{})})})}),t.jsx(G,{path:"/settings",element:t.jsx(Fa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your settings",children:t.jsx(_t,{})})}),t.jsx(G,{path:"/add-books",element:t.jsx(Fa,{requireVerification:!0,showVerificationUI:!0,featureName:"add new books",verificationMessage:"You need to verify your email address before you can add books to the platform. This helps ensure the quality and security of our book-sharing community.",children:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading add books form..."}),children:t.jsx(At,{})})})}),t.jsx(G,{path:"/wishlist",element:t.jsx(Fa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your wishlist",children:t.jsx("div",{children:"Wishlist Page (Coming Soon)"})})}),t.jsx(G,{path:"/messages",element:t.jsx(Fa,{requireVerification:!0,showVerificationUI:!0,featureName:"access your messages",children:t.jsx("div",{children:"Messages Page (Coming Soon)"})})}),t.jsx(G,{path:"/seed-books",element:t.jsx(Kt,{})}),t.jsx(G,{path:"/database-books",element:t.jsx(qt,{})}),t.jsx(G,{path:"/admin-setup",element:t.jsx(Ht,{})}),t.jsx(G,{path:"/admin-diagnostic",element:t.jsx(Gt,{})}),t.jsx(G,{path:"/unauthorized",element:t.jsx(vt,{})}),t.jsx(G,{path:"/admin",element:t.jsx(Ga,{children:t.jsx(Ka,{children:t.jsx(O.Suspense,{fallback:t.jsx(Za,{}),children:t.jsx(Dt,{})})})})}),t.jsx(G,{path:"/admin/books",element:t.jsx(Ga,{children:t.jsx(Ka,{children:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading book approvals..."}),children:t.jsx(Pt,{})})})})}),t.jsx(G,{path:"/admin/users",element:t.jsx(Ga,{children:t.jsx(Ka,{children:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading user management..."}),children:t.jsx(St,{})})})})}),t.jsx(G,{path:"/admin/utilities",element:t.jsx(Ga,{children:t.jsx(Ka,{children:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading admin utilities..."}),children:t.jsx(Ct,{})})})})}),t.jsx(G,{path:"/admin/settings",element:t.jsx(Ga,{children:t.jsx(Ka,{children:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading admin settings..."}),children:t.jsx(Rt,{})})})})}),t.jsx(G,{path:"/admin/messages",element:t.jsx(Ga,{children:t.jsx(Ka,{children:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading contact messages..."}),children:t.jsx(It,{})})})})}),t.jsx(G,{path:"/admin/feedback",element:t.jsx(Ga,{children:t.jsx(Ka,{children:t.jsx(O.Suspense,{fallback:t.jsx(Wa,{message:"Loading feedback..."}),children:t.jsx($t,{})})})})}),t.jsx(G,{path:"*",element:t.jsx(bt,{})})]})]})})})});(async()=>{try{await Na();e(document.getElementById("root")).render(t.jsx(Jt,{}))}catch(a){e(document.getElementById("root")).render(t.jsx(Jt,{}))}})();export{Ms as $,ls as A,Aa as B,gt as C,_a as D,Sa as E,Xs as F,va as G,Is as H,Ys as I,As as J,_s as K,Ds as L,Ts as M,qa as N,Pa as O,Wa as P,ot as Q,ht as R,$a as S,Ca as T,ka as U,Ra as V,Ia as W,Zs as X,Es as Y,ba as Z,We as _,za as a,vs as a0,Gs as a1,Ss as a2,Fs as b,Ha as c,cs as d,ds as e,qs as f,bs as g,ft as h,Da as i,Ma as j,Us as k,at as l,rt as m,it as n,nt as o,lt as p,dt as q,Ls as r,ks as s,sa as t,Va as u,Na as v,wa as w,ta as x,Ns as y,La as z};
