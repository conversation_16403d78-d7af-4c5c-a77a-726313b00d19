import { initializeFirebase, db } from './firebase';
import { Book, BookApprovalStatus } from '@/types';

/**
 * Gets books by owner ID from Firestore
 * @param ownerId - The ID of the owner
 * @returns Promise<Book[]> - Array of books owned by the specified user
 */
export const getBooksByOwner = async (ownerId: string): Promise<Book[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, where, getDocs, orderBy } = await import('firebase/firestore');

    console.log(`Fetching books for owner: ${ownerId} from Firestore`);

    // Create a reference to the books collection
    const booksRef = collection(db, 'books');

    // Create a simple query to avoid composite index requirements
    const booksQuery = query(
      booksRef,
      where('ownerId', '==', ownerId)
    );

    // Execute the query
    const querySnapshot = await getDocs(booksQuery);

    // Map the query results to Book objects
    const books: Book[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();

      // Convert Firestore timestamp to Date
      const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();

      // Create a Book object from the document data
      const book: Book = {
        id: doc.id,
        title: data.title || '',
        author: data.author || '',
        isbn: data.isbn,
        genre: Array.isArray(data.genre) ? data.genre : [],
        condition: data.condition || 'Good',
        description: data.description || '',
        imageUrl: data.imageUrl || 'https://via.placeholder.com/150?text=No+Image',
        imageUrls: data.imageUrls || undefined,
        displayImageIndex: data.displayImageIndex,
        perceivedValue: data.perceivedValue || 5,
        price: data.price,
        rentalPrice: data.rentalPrice,
        rentalPeriod: data.rentalPeriod,
        securityDepositRequired: data.securityDepositRequired, // Add security deposit required field
        securityDepositAmount: data.securityDepositAmount, // Add security deposit amount field
        availability: data.availability || 'For Exchange',
        ownerId: data.ownerId || '',
        ownerName: data.ownerName || '',
        ownerEmail: data.ownerEmail, // Include owner email if available
        ownerLocation: data.ownerLocation, // Legacy field for backward compatibility
        ownerCommunity: data.ownerCommunity || undefined, // Primary location identifier
        ownerCoordinates: data.ownerCoordinates || null, // GPS coordinates
        ownerPincode: data.ownerPincode || undefined, // Owner's pincode
        ownerRating: data.ownerRating || 0,
        distance: data.distance,
        createdAt: createdAt,
        approvalStatus: data.approvalStatus,
        status: data.status || 'Available',
        nextAvailableDate: data.nextAvailableDate?.toDate ? data.nextAvailableDate.toDate() : data.nextAvailableDate
      };

      books.push(book);
    });

    console.log(`Found ${books.length} books for owner: ${ownerId} in Firestore`);

    // Sort by creation date (newest first)
    return books.sort((a, b) => {
      const dateA = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt);
      const dateB = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });
  } catch (error) {
    console.error('Error getting books by owner from Firestore:', error);
    throw error; // Propagate the error to be handled by the caller
  }
};

/**
 * Gets a book by ID from Firestore
 * @param bookId - The ID of the book to fetch
 * @returns Promise<Book | null> - The book or null if not found
 */
export const getBook = async (bookId: string): Promise<Book | null> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc } = await import('firebase/firestore');

    console.log(`Fetching book with ID: ${bookId} from Firestore`);

    // Get the book document from Firestore
    const bookRef = doc(db, 'books', bookId);
    const bookSnapshot = await getDoc(bookRef);

    if (!bookSnapshot.exists()) {
      console.log(`No book found with ID: ${bookId} in Firestore`);
      return null;
    }

    // Get the book data
    const data = bookSnapshot.data();

    // Convert Firestore timestamp to Date
    const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();

    // Handle image URLs
    const imageUrls = data.imageUrls || [];
    const mainImageUrl = data.imageUrl || (imageUrls.length > 0 ? imageUrls[0] : 'https://via.placeholder.com/150?text=No+Image');

    // Create a Book object from the document data
    const book: Book = {
      id: bookSnapshot.id,
      title: data.title || '',
      author: data.author || '',
      isbn: data.isbn,
      genre: Array.isArray(data.genre) ? data.genre : [],
      condition: data.condition || 'Good',
      description: data.description || '',
      imageUrl: mainImageUrl,
      imageUrls: imageUrls.length > 0 ? imageUrls : undefined,
      displayImageIndex: data.displayImageIndex,
      perceivedValue: data.perceivedValue || 5,
      price: data.price,
      rentalPrice: data.rentalPrice,
      rentalPeriod: data.rentalPeriod,
      securityDepositRequired: data.securityDepositRequired, // Add security deposit required field
      securityDepositAmount: data.securityDepositAmount, // Add security deposit amount field
      availability: data.availability || 'For Exchange',
      ownerId: data.ownerId || '',
      ownerName: data.ownerName || '',
      ownerEmail: data.ownerEmail, // Include owner email if available in book document
      ownerLocation: data.ownerLocation, // Legacy field for backward compatibility
      ownerCommunity: data.ownerCommunity || undefined, // Primary location identifier
      ownerRating: data.ownerRating || 0,
      distance: data.distance,
      createdAt: createdAt,
      approvalStatus: data.approvalStatus,
      ownerCoordinates: data.ownerCoordinates || null,
      ownerPincode: data.ownerPincode || undefined,
      status: data.status || 'Available',
      nextAvailableDate: data.nextAvailableDate?.toDate ? data.nextAvailableDate.toDate() : data.nextAvailableDate
    };

    // Debug security deposit fields
    console.log(`Book "${book.title}" security deposit info:`, {
      required: book.securityDepositRequired,
      amount: book.securityDepositAmount,
      types: {
        required: typeof book.securityDepositRequired,
        amount: typeof book.securityDepositAmount
      }
    });

    // Check if pincode is directly in the book data
    if (data.ownerPincode) {
      console.log(`Book has ownerPincode field:`, data.ownerPincode);
      book.ownerPincode = data.ownerPincode;
    } else if (data.pincode) {
      console.log(`Book has pincode field:`, data.pincode);
      book.ownerPincode = data.pincode;
    } else if (data.ownerLocation) {
      // Try to extract pincode from ownerLocation if it's a string
      console.log(`Checking ownerLocation for pincode:`, data.ownerLocation);
      if (typeof data.ownerLocation === 'string') {
        const pincodeMatch = data.ownerLocation.match(/\b\d{6}\b/); // Indian pincodes are 6 digits
        if (pincodeMatch) {
          const extractedPincode = pincodeMatch[0];
          console.log(`Extracted pincode from ownerLocation:`, extractedPincode);
          book.ownerPincode = extractedPincode;
        }
      }
    }

    // If the book doesn't have owner coordinates or pincode, try to get them from the owner's user document
    if (((!book.ownerCoordinates || !book.ownerPincode) && book.ownerId)) {
      try {
        console.log(`Book "${book.title}" (ID: ${book.id}) needs additional owner information. Fetching from owner's user document...`);
        console.log(`Owner ID: ${book.ownerId}, Owner Name: ${book.ownerName}`);

        const userRef = doc(db, 'users', book.ownerId);
        const userSnapshot = await getDoc(userRef);

        if (userSnapshot.exists()) {
          const userData = userSnapshot.data();
          console.log(`Found owner user document for ${book.ownerId} (${userData.displayName || 'Unknown'})`);

          // Get owner email if not already set
          if (!book.ownerEmail) {
            if (userData.email) {
              console.log(`Found owner's email:`, userData.email);
              book.ownerEmail = userData.email;
            } else if (userData.emailAddress) {
              console.log(`Found owner's emailAddress:`, userData.emailAddress);
              book.ownerEmail = userData.emailAddress;
            } else if (userData.userEmail) {
              console.log(`Found owner's userEmail:`, userData.userEmail);
              book.ownerEmail = userData.userEmail;
            } else {
              console.log(`No email found in user document for owner ${book.ownerId}`);
            }
          }

          // Try to get GPS coordinates if needed
          if (!book.ownerCoordinates) {
            if (userData.gpsCoordinates) {
              console.log(`Found owner's GPS coordinates:`, userData.gpsCoordinates);
              book.ownerCoordinates = userData.gpsCoordinates;
            } else if (userData.latitude !== undefined && userData.longitude !== undefined) {
              console.log(`Found owner's latitude/longitude:`, userData.latitude, userData.longitude);
              book.ownerCoordinates = {
                latitude: userData.latitude,
                longitude: userData.longitude
              };
            } else {
              console.log(`Owner doesn't have GPS coordinates in their user document`);
            }
          }

          // Try to get pincode if needed
          if (!book.ownerPincode) {
            console.log('Checking for pincode in user data for', book.ownerId);

            // Check various fields that might contain pincode
            if (userData.pincode) {
              console.log(`Found owner's pincode:`, userData.pincode);
              book.ownerPincode = userData.pincode;
            } else if (userData.pinCode) { // Try alternative capitalization
              console.log(`Found owner's pinCode (alternative capitalization):`, userData.pinCode);
              book.ownerPincode = userData.pinCode;
            } else if (userData.pin_code) { // Try snake case
              console.log(`Found owner's pin_code (snake case):`, userData.pin_code);
              book.ownerPincode = userData.pin_code;
            } else if (userData.postalCode) { // Try alternative naming
              console.log(`Found owner's postalCode:`, userData.postalCode);
              book.ownerPincode = userData.postalCode;
            } else if (userData.zipcode) { // Try zipcode
              console.log(`Found owner's zipcode:`, userData.zipcode);
              book.ownerPincode = userData.zipcode;
            } else if (userData.zip) { // Try zip
              console.log(`Found owner's zip:`, userData.zip);
              book.ownerPincode = userData.zip;
            } else if (userData.address) {
              // Try to extract pincode from address
              const pincodeMatch = userData.address.match(/\b\d{6}\b/); // Indian pincodes are 6 digits
              if (pincodeMatch) {
                const extractedPincode = pincodeMatch[0];
                console.log(`Extracted pincode from address:`, extractedPincode);
                book.ownerPincode = extractedPincode;
              } else {
                console.log(`No pincode found in address:`, userData.address);
              }
            } else {
              console.log(`No pincode found in any expected field for owner ${book.ownerId}`);
            }
          }
        } else {
          console.log(`Owner user document not found`);
        }
      } catch (userError) {
        console.error('Error fetching owner user document:', userError);
        // Continue without owner information
      }
    }

    // Update the book in Firestore if we found pincode or email that wasn't there before
    if ((book.ownerPincode && !data.ownerPincode) || (book.ownerEmail && !data.ownerEmail)) {
      try {
        console.log(`Updating book with owner information that was found in user document`);
        const { updateDoc } = await import('firebase/firestore');

        const updateData: Record<string, any> = {
          updatedAt: new Date()
        };

        // Add pincode to update if it's new
        if (book.ownerPincode && !data.ownerPincode) {
          updateData.ownerPincode = book.ownerPincode;
          console.log(`Adding owner pincode to update:`, book.ownerPincode);
        }

        // Add email to update if it's new
        if (book.ownerEmail && !data.ownerEmail) {
          updateData.ownerEmail = book.ownerEmail;
          console.log(`Adding owner email to update:`, book.ownerEmail);
        }

        await updateDoc(bookRef, updateData);
        console.log(`Book updated with owner information`);
      } catch (updateError) {
        console.error('Error updating book with owner information:', updateError);
        // Continue without updating the book
      }
    }

    console.log(`Found book in Firestore: ${book.title}`);
    return book;
  } catch (error) {
    console.error('Error getting book from Firestore:', error);
    throw error; // Propagate the error to be handled by the caller
  }
};

/**
 * Gets paginated books from Firestore
 * @param includeUnapproved - Whether to include books that are not approved
 * @param userCommunity - Current user's community for priority sorting
 * @param page - Page number (1-based)
 * @param itemsPerPage - Number of items per page
 * @returns Promise<{ books: Book[], totalCount: number }> - Paginated books and total count
 */
export const getPaginatedBooks = async (
  includeUnapproved = false,
  userCommunity?: string,
  page: number = 1,
  itemsPerPage: number = 12
): Promise<{ books: Book[], totalCount: number }> => {
  try {
    // Get all books first (we'll optimize this later with proper pagination)
    const allBooks = await getAllBooks(includeUnapproved, userCommunity);

    // Calculate pagination
    const startIndex = (page - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedBooks = allBooks.slice(startIndex, endIndex);

    return {
      books: paginatedBooks,
      totalCount: allBooks.length
    };
  } catch (error) {
    console.error('Error getting paginated books:', error);
    throw error;
  }
};

/**
 * Gets all books from Firestore
 * @param includeUnapproved - Whether to include books that are not approved
 * @param userCommunity - Current user's community for priority sorting
 * @returns Promise<Book[]> - Array of books sorted by community priority, then distance
 */
export const getAllBooks = async (includeUnapproved = false, userCommunity?: string): Promise<Book[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, where, getDocs, orderBy } = await import('firebase/firestore');

    // Import geolocation utilities
    const { getUserLocationSilently, calculateDistance } = await import('./geolocationUtils');

    console.log('Fetching all books from Firestore');

    // Create a reference to the books collection
    const booksRef = collection(db, 'books');

    // Create a query against the collection
    let booksQuery;

    // Simplified query to avoid composite index requirements
    // We'll filter the results in memory instead
    booksQuery = query(booksRef);
    console.log(includeUnapproved ? 'Fetching all books including unapproved ones' : 'Fetching only approved books');

    // Get user's current location silently
    console.log('Getting user location for distance calculation...');
    const userLocation = await getUserLocationSilently();

    if (userLocation) {
      console.log('User location obtained for distance calculation:', userLocation);
    } else {
      console.log('Could not get user location, books will not be sorted by distance');
    }

    // Execute the query
    const querySnapshot = await getDocs(booksQuery);

    // Map the query results to Book objects
    const books: Book[] = [];
    const booksWithoutCoordinates: { book: Book, index: number }[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();

      // Convert Firestore timestamp to Date
      const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();

      // Create a Book object from the document data
      const book: Book = {
        id: doc.id,
        title: data.title || '',
        author: data.author || '',
        isbn: data.isbn,
        genre: Array.isArray(data.genre) ? data.genre : [],
        condition: data.condition || 'Good',
        description: data.description || '',
        imageUrl: data.imageUrl || 'https://via.placeholder.com/150?text=No+Image',
        perceivedValue: data.perceivedValue || 5,
        price: data.price,
        rentalPrice: data.rentalPrice,
        rentalPeriod: data.rentalPeriod,
        securityDepositRequired: data.securityDepositRequired, // Add security deposit required field
        securityDepositAmount: data.securityDepositAmount, // Add security deposit amount field
        availability: data.availability || 'For Exchange',
        ownerId: data.ownerId || '',
        ownerName: data.ownerName || '',
        ownerEmail: data.ownerEmail, // Include owner email if available
        ownerLocation: data.ownerLocation, // Legacy field for backward compatibility
        ownerCommunity: data.ownerCommunity || undefined, // Primary location identifier
        ownerRating: data.ownerRating || 0,
        distance: data.distance,
        createdAt: createdAt,
        approvalStatus: data.approvalStatus,
        ownerCoordinates: null, // Will be set below after validation
        ownerPincode: data.ownerPincode,
        status: data.status || 'Available',
        nextAvailableDate: data.nextAvailableDate?.toDate ? data.nextAvailableDate.toDate() : data.nextAvailableDate
      };

      // Process owner coordinates if they exist
      if (data.ownerCoordinates) {
        // Validate coordinates format
        if (typeof data.ownerCoordinates === 'object' &&
            data.ownerCoordinates !== null &&
            typeof data.ownerCoordinates.latitude === 'number' &&
            typeof data.ownerCoordinates.longitude === 'number') {

          book.ownerCoordinates = {
            latitude: data.ownerCoordinates.latitude,
            longitude: data.ownerCoordinates.longitude
          };

          console.log(`Book "${book.title}" has valid coordinates:`, book.ownerCoordinates);
        } else {
          console.log(`Book "${book.title}" has invalid coordinates format:`, data.ownerCoordinates);
        }
      } else {
        console.log(`Book "${book.title}" has no coordinates in the book document`);
        // Track books without coordinates for later lookup if they have an owner ID
        if (book.ownerId) {
          const index = books.length; // Current index where this book will be added
          booksWithoutCoordinates.push({ book, index });
        }
      }

      // Calculate distance if we have user location and book owner coordinates
      if (userLocation && book.ownerCoordinates) {
        try {
          book.distance = calculateDistance(userLocation, book.ownerCoordinates);
          console.log(`Calculated distance for "${book.title}": ${book.distance} km`);
        } catch (distanceError) {
          console.error(`Error calculating distance for book "${book.title}":`, distanceError);
        }
      }

      books.push(book);
    });

    console.log(`Found ${books.length} books in Firestore`);

    // If we have books without coordinates and user location is available, try to fetch coordinates from user documents
    if (booksWithoutCoordinates.length > 0 && userLocation) {
      console.log(`Found ${booksWithoutCoordinates.length} books without coordinates. Fetching from user documents...`);

      // Import necessary Firestore functions
      const { doc, getDoc } = await import('firebase/firestore');

      // Process books without coordinates in batches to avoid too many parallel requests
      const batchSize = 5;
      for (let i = 0; i < booksWithoutCoordinates.length; i += batchSize) {
        const batch = booksWithoutCoordinates.slice(i, i + batchSize);

        // Process each book in the batch in parallel
        await Promise.all(batch.map(async ({ book, index }) => {
          try {
            console.log(`Fetching coordinates for book "${book.title}" from owner document...`);

            // Get the user document
            const userRef = doc(db, 'users', book.ownerId);
            const userSnapshot = await getDoc(userRef);

            if (userSnapshot.exists()) {
              const userData = userSnapshot.data();

              // Try to get coordinates from various possible fields
              let ownerCoordinates = null;

              if (userData.gpsCoordinates) {
                ownerCoordinates = userData.gpsCoordinates;
              } else if (userData.coordinates) {
                ownerCoordinates = userData.coordinates;
              } else if (userData.latitude !== undefined && userData.longitude !== undefined) {
                ownerCoordinates = {
                  latitude: userData.latitude,
                  longitude: userData.longitude
                };
              } else if (userData.location &&
                         typeof userData.location === 'object' &&
                         userData.location.latitude !== undefined &&
                         userData.location.longitude !== undefined) {
                ownerCoordinates = {
                  latitude: userData.location.latitude,
                  longitude: userData.location.longitude
                };
              }

              // Validate coordinates
              if (ownerCoordinates &&
                  typeof ownerCoordinates === 'object' &&
                  typeof ownerCoordinates.latitude === 'number' &&
                  typeof ownerCoordinates.longitude === 'number') {

                console.log(`Found valid coordinates for book "${book.title}" in owner document:`, ownerCoordinates);

                // Update the book in the books array
                books[index].ownerCoordinates = ownerCoordinates;

                // Calculate distance
                try {
                  books[index].distance = calculateDistance(userLocation, ownerCoordinates);
                  console.log(`Calculated distance for "${book.title}" from owner coordinates: ${books[index].distance} km`);
                } catch (distanceError) {
                  console.error(`Error calculating distance for book "${book.title}" from owner coordinates:`, distanceError);
                }
              } else {
                console.log(`No valid coordinates found in owner document for book "${book.title}"`);
              }
            } else {
              console.log(`Owner document not found for book "${book.title}" (Owner ID: ${book.ownerId})`);
            }
          } catch (error) {
            console.error(`Error fetching owner coordinates for book "${book.title}":`, error);
          }
        }));
      }
    }

    // Filter books by approval status if needed
    let filteredBooks = books;
    if (!includeUnapproved) {
      filteredBooks = books.filter(book =>
        book.approvalStatus === BookApprovalStatus.Approved ||
        book.approvalStatus === undefined ||
        book.approvalStatus === null
      );
      console.log(`Filtered to ${filteredBooks.length} approved books`);
    }

    // Implement two-tier sorting: Community priority first, then distance
    console.log('Implementing two-tier sorting: Community priority + Distance');
    console.log('User community:', userCommunity);

    return filteredBooks.sort((a, b) => {
      // First tier: Community priority sorting
      const aIsSameCommunity = userCommunity && a.ownerCommunity && a.ownerCommunity === userCommunity;
      const bIsSameCommunity = userCommunity && b.ownerCommunity && b.ownerCommunity === userCommunity;

      // If one book is from same community and other is not, prioritize same community
      if (aIsSameCommunity && !bIsSameCommunity) {
        console.log(`Prioritizing "${a.title}" (same community: ${a.ownerCommunity}) over "${b.title}"`);
        return -1;
      }
      if (bIsSameCommunity && !aIsSameCommunity) {
        console.log(`Prioritizing "${b.title}" (same community: ${b.ownerCommunity}) over "${a.title}"`);
        return 1;
      }

      // Second tier: Distance sorting within the same community group
      if (userLocation) {
        // If both books have distance, sort by distance
        if (a.distance !== undefined && b.distance !== undefined) {
          return a.distance - b.distance;
        }

        // If only one book has distance, prioritize the one with distance
        if (a.distance !== undefined) return -1;
        if (b.distance !== undefined) return 1;
      }

      // Third tier: If no distance available, sort by creation date (newest first)
      const dateA = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt);
      const dateB = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });
  } catch (error) {
    console.error('Error getting all books from Firestore:', error);
    throw error; // Propagate the error to be handled by the caller
  }
};

/**
 * Creates a new book in Firestore
 * @param bookData - The book data to create
 * @returns Promise<string> - The ID of the created book
 */
export const createBook = async (bookData: any): Promise<string> => {
  try {
    // Validate required fields
    if (!bookData.title) {
      throw new Error('Book title is required');
    }

    if (!bookData.author) {
      throw new Error('Author name is required');
    }

    if (!bookData.genre || !Array.isArray(bookData.genre) || bookData.genre.length === 0) {
      throw new Error('At least one genre is required');
    }

    if (!bookData.condition) {
      throw new Error('Book condition is required');
    }

    if (!bookData.description) {
      throw new Error('Book description is required');
    }

    if (!bookData.availability) {
      throw new Error('Availability option is required');
    }

    if (!bookData.ownerId) {
      throw new Error('Owner ID is required. Please make sure you are signed in.');
    }

    // Validate rental fields
    if (bookData.availability.includes('Rent')) {
      if (bookData.rentalPrice === null || bookData.rentalPrice === undefined) {
        throw new Error('Rental price is required when availability includes "For Rent"');
      }

      if (bookData.rentalPeriod === null || bookData.rentalPeriod === undefined) {
        throw new Error('Rental period is required when availability includes "For Rent"');
      }
    }

    // Validate sale price
    if (bookData.availability.includes('Sale') && (bookData.price === null || bookData.price === undefined)) {
      throw new Error('Sale price is required when availability includes "For Sale"');
    }

    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    console.log('Creating new book with data:', bookData);

    try {
      // Create a reference to the books collection
      const booksRef = collection(db, 'books');

      // Filter out undefined values as Firestore doesn't accept them
      const filteredBookData = Object.entries(bookData).reduce((acc, [key, value]) => {
        // Only include fields that are not undefined
        if (value !== undefined) {
          acc[key] = value;
        }
        return acc;
      }, {} as Record<string, any>);

      // Get owner email if not already provided
      let ownerEmail = filteredBookData.ownerEmail;

      if (!ownerEmail && filteredBookData.ownerId) {
        try {
          console.log(`Fetching owner email for user ID: ${filteredBookData.ownerId}`);
          const { doc, getDoc } = await import('firebase/firestore');
          const userRef = doc(db, 'users', filteredBookData.ownerId);
          const userSnapshot = await getDoc(userRef);

          if (userSnapshot.exists()) {
            const userData = userSnapshot.data();
            // Check various fields that might contain email
            ownerEmail = userData.email || userData.emailAddress || userData.userEmail;

            if (ownerEmail) {
              console.log(`Found owner email: ${ownerEmail}`);
            } else {
              console.log('No email field found in user document');
            }
          } else {
            console.log(`No user document found for owner ID: ${filteredBookData.ownerId}`);
          }
        } catch (emailError) {
          console.error('Error fetching owner email:', emailError);
          // Continue without email rather than failing the book creation
        }
      }

      // Add timestamps, email, and set approval status to pending
      const newBook = {
        ...filteredBookData,
        ownerEmail: ownerEmail || undefined, // Only add if we found it
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        approvalStatus: BookApprovalStatus.Pending // Default to pending approval
      };

      console.log('Filtered book data (no undefined values):', newBook);

      // Add the book to Firestore
      const docRef = await addDoc(booksRef, newBook);
      console.log('Book created successfully with ID:', docRef.id);

      return docRef.id;
    } catch (firestoreError) {
      console.error('Firestore error creating book:', firestoreError);

      // Check for specific Firestore errors
      if (firestoreError instanceof Error) {
        if (firestoreError.message.includes('permission-denied')) {
          throw new Error('Permission denied: You do not have access to add books');
        } else if (firestoreError.message.includes('unavailable')) {
          throw new Error('Firebase service is currently unavailable. Please try again later');
        } else if (firestoreError.message.includes('network')) {
          throw new Error('Network error. Please check your internet connection');
        } else {
          throw firestoreError; // Re-throw the original error if it's not one we specifically handle
        }
      } else {
        throw new Error('Unknown error occurred while adding the book');
      }
    }
  } catch (error) {
    console.error('Error creating book:', error);
    throw error;
  }
};

/**
 * Seeds the database with real books
 * This is a utility function for development purposes
 */
export const seedRealBooksToFirebase = async (): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, addDoc, serverTimestamp } = await import('firebase/firestore');

    console.log('Seeding real books to Firebase...');

    // Create a reference to the books collection
    const booksRef = collection(db, 'books');

    // Define some real books
    const realBooks = [
      {
        title: 'The Alchemist',
        author: 'Paulo Coelho',
        isbn: '9780062315007',
        genre: ['Fiction', 'Philosophy', 'Fantasy'],
        condition: 'Like New',
        description: 'A philosophical novel about a young shepherd who dreams of finding treasure and embarks on a journey of self-discovery.',
        imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1654371463i/18144590.jpg',
        perceivedValue: 9,
        price: 450,
        availability: 'For Exchange',
        ownerId: 'user123',
        ownerName: 'Priya Sharma',
        ownerLocation: 'Kolkata',
        ownerRating: 4.7,
        distance: 6.8
      },
      {
        title: 'To Kill a Mockingbird',
        author: 'Harper Lee',
        isbn: '9780061120084',
        genre: ['Fiction', 'Classics', 'Literature'],
        condition: 'Good',
        description: 'A classic novel about racial injustice in the American South through the eyes of a young girl.',
        imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1553383690i/2657.jpg',
        perceivedValue: 8,
        price: 350,
        rentalPrice: 50,
        rentalPeriod: 'per week',
        availability: 'For Rent, Sale & Exchange',
        ownerId: 'user456',
        ownerName: 'Sarah Johnson',
        ownerLocation: 'Mumbai',
        ownerRating: 4.8,
        distance: 3.2
      },
      {
        title: '1984',
        author: 'George Orwell',
        isbn: '9780451524935',
        genre: ['Fiction', 'Classics', 'Dystopian'],
        condition: 'Like New',
        description: 'A dystopian novel set in a totalitarian society where critical thought is suppressed.',
        imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1657781256i/61439040.jpg',
        perceivedValue: 9,
        price: 400,
        availability: 'For Sale & Exchange',
        ownerId: 'user789',
        ownerName: 'Raj Patel',
        ownerLocation: 'Delhi',
        ownerRating: 4.5,
        distance: 5.7
      },
      {
        title: 'The Great Gatsby',
        author: 'F. Scott Fitzgerald',
        isbn: '9780743273565',
        genre: ['Fiction', 'Classics', 'Literature'],
        condition: 'Good',
        description: 'A story of wealth, love, and the American Dream in the 1920s.',
        imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1490528560i/4671.jpg',
        perceivedValue: 7,
        rentalPrice: 40,
        rentalPeriod: 'per week',
        availability: 'For Rent & Exchange',
        ownerId: 'user101',
        ownerName: 'Anita Mehta',
        ownerLocation: 'Bangalore',
        ownerRating: 4.9,
        distance: 1.3
      },
      {
        title: 'Harry Potter and the Sorcerer\'s Stone',
        author: 'J.K. Rowling',
        isbn: '9780590353427',
        genre: ['Fantasy', 'Young Adult', 'Fiction'],
        condition: 'Fair',
        description: 'The first book in the beloved Harry Potter series about a boy who discovers he\'s a wizard.',
        imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1474154022i/3.jpg',
        perceivedValue: 6,
        price: 300,
        rentalPrice: 35,
        rentalPeriod: 'per week',
        availability: 'For Rent & Sale',
        ownerId: 'user202',
        ownerName: 'Vikram Singh',
        ownerLocation: 'Chennai',
        ownerRating: 4.6,
        distance: 4.5
      },
      {
        title: 'Pride and Prejudice',
        author: 'Jane Austen',
        isbn: '9780141439518',
        genre: ['Fiction', 'Classics', 'Romance'],
        condition: 'Like New',
        description: 'A classic romance novel examining the relationships between young people in 19th century England.',
        imageUrl: 'https://images-na.ssl-images-amazon.com/images/S/compressed.photo.goodreads.com/books/1320399351i/1885.jpg',
        perceivedValue: 8,
        rentalPrice: 45,
        rentalPeriod: 'per week',
        availability: 'For Rent & Exchange',
        ownerId: 'user303',
        ownerName: 'Anjali Kumar',
        ownerLocation: 'Hyderabad',
        ownerRating: 4.4,
        distance: 2.9
      }
    ];

    // Add each real book to Firestore
    for (const book of realBooks) {
      try {
        // Add timestamps and set approval status to approved
        const bookData = {
          ...book,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
          approvalStatus: BookApprovalStatus.Approved
        };

        // Add the book to Firestore
        const docRef = await addDoc(booksRef, bookData);
        console.log(`Added book: ${book.title} with ID: ${docRef.id}`);
      } catch (bookError) {
        console.error(`Error adding book "${book.title}":`, bookError);
        // Continue with the next book instead of failing the entire operation
      }
    }

    console.log('Seeding completed successfully');
  } catch (error) {
    console.error('Error seeding real books to Firebase:', error);
    throw error;
  }
};

/**
 * Gets pending books that need approval
 * @returns Promise<Book[]> - Array of books with pending approval status
 */
export const getPendingBooks = async (): Promise<Book[]> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { collection, query, getDocs } = await import('firebase/firestore');

    console.log('Fetching pending books from Firestore');

    // Create a reference to the books collection
    const booksRef = collection(db, 'books');

    // Create a simple query without composite index requirements
    // We'll filter the results in memory
    const booksQuery = query(booksRef);

    // Execute the query
    console.log('Executing query to get all books');
    const querySnapshot = await getDocs(booksQuery);
    console.log(`Retrieved ${querySnapshot.size} total books from Firestore`);

    // Map the query results to Book objects and filter for pending status
    const pendingBooks: Book[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data();

      // Check if the book has pending approval status
      if (data.approvalStatus === BookApprovalStatus.Pending) {
        console.log(`Found pending book: ${data.title} (ID: ${doc.id})`);

        // Convert Firestore timestamp to Date
        const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date();

        // Create a Book object from the document data
        const book: Book = {
          id: doc.id,
          title: data.title || '',
          author: data.author || '',
          isbn: data.isbn,
          genre: Array.isArray(data.genre) ? data.genre : [],
          condition: data.condition || 'Good',
          description: data.description || '',
          imageUrl: data.imageUrl || 'https://via.placeholder.com/150?text=No+Image',
          perceivedValue: data.perceivedValue || 5,
          price: data.price,
          rentalPrice: data.rentalPrice,
          rentalPeriod: data.rentalPeriod,
          availability: data.availability || 'For Exchange',
          ownerId: data.ownerId || '',
          ownerName: data.ownerName || '',
          ownerEmail: data.ownerEmail, // Include owner email if available
          ownerLocation: data.ownerLocation || '',
          ownerRating: data.ownerRating || 0,
          distance: data.distance,
          createdAt: createdAt,
          approvalStatus: data.approvalStatus
        };

        pendingBooks.push(book);
      }
    });

    // Sort by creation date (newest first)
    const sortedPendingBooks = pendingBooks.sort((a, b) => {
      const dateA = a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt);
      const dateB = b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });

    console.log(`Found ${sortedPendingBooks.length} pending books in Firestore`);
    return sortedPendingBooks;
  } catch (error) {
    console.error('Error getting pending books from Firestore:', error);
    // Add more detailed error logging
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });

      // Check for specific Firestore errors
      if (error.message.includes('permission-denied')) {
        throw new Error('Permission denied: You do not have access to view pending books');
      } else if (error.message.includes('unavailable')) {
        throw new Error('Firebase service is currently unavailable. Please try again later');
      } else if (error.message.includes('network')) {
        throw new Error('Network error. Please check your internet connection');
      } else if (error.message.includes('index')) {
        throw new Error('Missing index error. Please contact the administrator');
      }
    }
    throw error;
  }
};

/**
 * Updates a book document in Firestore
 * @param bookId - The ID of the book to update
 * @param bookData - Partial book data to update
 * @returns Promise<void>
 */
export const updateBook = async (bookId: string, bookData: Partial<Book>): Promise<void> => {
  try {
    console.log('updateBook called with ID:', bookId);
    console.log('updateBook data received:', bookData);

    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, getDoc, updateDoc, serverTimestamp, Timestamp } = await import('firebase/firestore');

    const bookRef = doc(db, 'books', bookId);

    // Check if the book exists
    const bookSnapshot = await getDoc(bookRef);
    if (!bookSnapshot.exists()) {
      console.error(`Book with ID ${bookId} not found`);
      throw new Error(`Book with ID ${bookId} not found`);
    }

    console.log('Current book data:', bookSnapshot.data());

    // Convert any Date objects to Firestore timestamps
    const sanitizedData: Record<string, any> = {};

    // Process each field to ensure it's in the correct format for Firestore
    Object.entries(bookData).forEach(([key, value]) => {
      // Skip undefined values
      if (value === undefined) return;

      // Handle arrays (like genre)
      if (Array.isArray(value)) {
        sanitizedData[key] = value;
      }
      // Handle dates
      else if (value instanceof Date) {
        sanitizedData[key] = Timestamp.fromDate(value);
      }
      // Handle everything else
      else {
        sanitizedData[key] = value;
      }
    });

    // Add updated timestamp
    const updatedData = {
      ...sanitizedData,
      updatedAt: serverTimestamp()
    };

    console.log('Final data to be sent to Firestore:', updatedData);

    // Perform the update
    await updateDoc(bookRef, updatedData);
    console.log('Book updated successfully:', bookId);

    // Verify the update
    const updatedSnapshot = await getDoc(bookRef);
    console.log('Updated book data in Firestore:', updatedSnapshot.data());
  } catch (error) {
    console.error('Error updating book:', error);
    console.error('Error details:', {
      message: (error as any).message,
      code: (error as any).code,
      stack: (error as any).stack
    });
    throw error;
  }
};

/**
 * Approves a book
 * @param bookId - The ID of the book to approve
 * @returns Promise<void>
 */
export const approveBook = async (bookId: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Approving book with ID: ${bookId}`);

    // Get a reference to the book document
    const bookRef = doc(db, 'books', bookId);

    // Update the book's approval status
    await updateDoc(bookRef, {
      approvalStatus: BookApprovalStatus.Approved,
      updatedAt: serverTimestamp()
    });

    console.log(`Book ${bookId} approved successfully`);
  } catch (error) {
    console.error(`Error approving book ${bookId}:`, error);
    throw error;
  }
};

/**
 * Rejects a book
 * @param bookId - The ID of the book to reject
 * @param reason - The reason for rejection
 * @returns Promise<void>
 */
export const rejectBook = async (bookId: string, reason: string): Promise<void> => {
  try {
    // Initialize Firebase
    await initializeFirebase();

    // Dynamically import Firestore functions
    const { doc, updateDoc, serverTimestamp } = await import('firebase/firestore');

    console.log(`Rejecting book with ID: ${bookId}, reason: ${reason}`);

    // Get a reference to the book document
    const bookRef = doc(db, 'books', bookId);

    // Update the book's approval status and add rejection reason
    await updateDoc(bookRef, {
      approvalStatus: BookApprovalStatus.Rejected,
      rejectionReason: reason,
      updatedAt: serverTimestamp()
    });

    console.log(`Book ${bookId} rejected successfully`);
  } catch (error) {
    console.error(`Error rejecting book ${bookId}:`, error);
    throw error;
  }
};

/**
 * Search for books by title, author, or genre
 * @param query - The search query
 * @returns Promise<Book[]> - Array of books matching the search query
 */
export const searchBooks = async (query: string): Promise<Book[]> => {
  try {
    console.log(`Searching for books with query: "${query}"`);

    if (!query || query.trim() === '') {
      console.log('Empty search query, returning empty results');
      return [];
    }

    // Normalize the query (lowercase, trim whitespace)
    const normalizedQuery = query.toLowerCase().trim();

    // Get all approved books
    const allBooks = await getAllBooks(false);

    // Filter books based on the search query
    const searchResults = allBooks.filter(book => {
      // Check if the query matches the title, author, or genre
      const titleMatch = book.title.toLowerCase().includes(normalizedQuery);
      const authorMatch = book.author.toLowerCase().includes(normalizedQuery);

      // Check if any genre includes the query
      const genreMatch = book.genre.some(genre =>
        genre.toLowerCase().includes(normalizedQuery)
      );

      // Check if ISBN matches (if available)
      const isbnMatch = book.isbn ? book.isbn.toLowerCase().includes(normalizedQuery) : false;

      return titleMatch || authorMatch || genreMatch || isbnMatch;
    });

    console.log(`Found ${searchResults.length} books matching query "${query}"`);

    return searchResults;
  } catch (error) {
    console.error('Error searching books:', error);
    throw new Error(`Failed to search books: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};