import{r as e}from"./chunk-b0HHmiEU.js";import"./chunk-CpdlqjqK.js";var t=["light","dark"],r=e.createContext(void 0),a={setTheme:e=>{},themes:[]},c=()=>{var t;return null!=(t=e.useContext(r))?t:a};e.memo((({forcedTheme:r,storageKey:a,attribute:c,enableSystem:l,enableColorScheme:n,defaultTheme:s,value:o,attrs:m,nonce:i})=>{let d="system"===s,$="class"===c?`var d=document.documentElement,c=d.classList;c.remove(${m.map((e=>`'${e}'`)).join(",")});`:`var d=document.documentElement,n='${c}',s='setAttribute';`,u=n?t.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",h=(e,r=!1,a=!0)=>{let l=o?o[e]:e,s=r?e+"|| ''":`'${l}'`,m="";return n&&a&&!r&&t.includes(e)&&(m+=`d.style.colorScheme = '${e}';`),"class"===c?m+=r||l?`c.add(${s})`:"null":l&&(m+=`d[s](n,${s})`),m},f=r?`!function(){${$}${h(r)}}()`:l?`!function(){try{${$}var e=localStorage.getItem('${a}');if('system'===e||(!e&&${d})){var t='(prefers-color-scheme: dark)',m=window.matchMedia(t);if(m.media!==t||m.matches){${h("dark")}}else{${h("light")}}}else if(e){${o?`var x=${JSON.stringify(o)};`:""}${h(o?"x[e]":"e",!0)}}${d?"":"else{"+h(s,!1,!1)+"}"}${u}}catch(e){}}()`:`!function(){try{${$}var e=localStorage.getItem('${a}');if(e){${o?`var x=${JSON.stringify(o)};`:""}${h(o?"x[e]":"e",!0)}}else{${h(s,!1,!1)};}${u}}catch(t){}}();`;return e.createElement("script",{nonce:i,dangerouslySetInnerHTML:{__html:f}})}));export{c as j};
