/**
 * Pagination Utilities
 * 
 * Utility functions for handling pagination calculations and metadata
 */

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  startIndex: number;
  endIndex: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  pageNumbers: number[];
}

export interface PaginatedData<T> {
  items: T[];
  pagination: PaginationInfo;
}

/**
 * Calculate pagination metadata
 */
export const calculatePagination = (
  totalItems: number,
  currentPage: number,
  itemsPerPage: number = 12
): PaginationInfo => {
  // Ensure currentPage is at least 1
  const page = Math.max(1, currentPage);
  
  // Calculate total pages
  const totalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));
  
  // Ensure currentPage doesn't exceed totalPages
  const validPage = Math.min(page, totalPages);
  
  // Calculate start and end indices
  const startIndex = (validPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage - 1, totalItems - 1);
  
  // Calculate navigation flags
  const hasNextPage = validPage < totalPages;
  const hasPreviousPage = validPage > 1;
  
  // Generate page numbers for pagination controls
  const pageNumbers = generatePageNumbers(validPage, totalPages);
  
  return {
    currentPage: validPage,
    totalPages,
    totalItems,
    itemsPerPage,
    startIndex,
    endIndex,
    hasNextPage,
    hasPreviousPage,
    pageNumbers
  };
};

/**
 * Generate page numbers for pagination controls
 * Shows up to 7 page numbers with ellipsis when needed
 */
export const generatePageNumbers = (currentPage: number, totalPages: number): number[] => {
  if (totalPages <= 7) {
    // Show all pages if 7 or fewer
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }
  
  const pages: number[] = [];
  
  if (currentPage <= 4) {
    // Show first 5 pages + ellipsis + last page
    pages.push(1, 2, 3, 4, 5);
    if (totalPages > 6) {
      pages.push(-1); // Ellipsis marker
    }
    pages.push(totalPages);
  } else if (currentPage >= totalPages - 3) {
    // Show first page + ellipsis + last 5 pages
    pages.push(1);
    if (totalPages > 6) {
      pages.push(-1); // Ellipsis marker
    }
    for (let i = totalPages - 4; i <= totalPages; i++) {
      pages.push(i);
    }
  } else {
    // Show first page + ellipsis + current-1, current, current+1 + ellipsis + last page
    pages.push(1);
    pages.push(-1); // Ellipsis marker
    pages.push(currentPage - 1, currentPage, currentPage + 1);
    pages.push(-1); // Ellipsis marker
    pages.push(totalPages);
  }
  
  return pages;
};

/**
 * Paginate an array of items
 */
export const paginateArray = <T>(
  items: T[],
  currentPage: number,
  itemsPerPage: number = 12
): PaginatedData<T> => {
  const pagination = calculatePagination(items.length, currentPage, itemsPerPage);
  const startIndex = pagination.startIndex;
  const endIndex = startIndex + itemsPerPage;
  
  return {
    items: items.slice(startIndex, endIndex),
    pagination
  };
};

/**
 * Get pagination display text
 */
export const getPaginationDisplayText = (pagination: PaginationInfo): string => {
  if (pagination.totalItems === 0) {
    return 'No books found';
  }
  
  const start = pagination.startIndex + 1;
  const end = Math.min(pagination.endIndex + 1, pagination.totalItems);
  
  if (pagination.totalPages === 1) {
    return `Showing ${pagination.totalItems} book${pagination.totalItems === 1 ? '' : 's'}`;
  }
  
  return `Showing ${start}-${end} of ${pagination.totalItems} books`;
};

/**
 * Get page info for screen readers
 */
export const getPageAriaLabel = (page: number, totalPages: number): string => {
  return `Go to page ${page} of ${totalPages}`;
};

/**
 * Validate page number
 */
export const validatePageNumber = (page: string | number | null | undefined): number => {
  if (page === null || page === undefined) {
    return 1;
  }
  
  const pageNum = typeof page === 'string' ? parseInt(page, 10) : page;
  
  if (isNaN(pageNum) || pageNum < 1) {
    return 1;
  }
  
  return pageNum;
};

/**
 * Build URL search params for pagination
 */
export const buildPaginationSearchParams = (
  currentParams: URLSearchParams,
  page: number,
  resetToFirstPage: boolean = false
): URLSearchParams => {
  const newParams = new URLSearchParams(currentParams);
  
  if (resetToFirstPage || page === 1) {
    newParams.delete('page');
  } else {
    newParams.set('page', page.toString());
  }
  
  return newParams;
};
