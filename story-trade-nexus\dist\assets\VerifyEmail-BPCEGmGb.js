import{j as e}from"./chunk-CpdlqjqK.js";import{r as s,u as i,L as a}from"./chunk-b0HHmiEU.js";import{u as t,M as r,a as c}from"./index-DwRq5Uwb.js";import{J as n}from"./chunk-Cw96wKwP.js";import{M as l,R as o}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const d=()=>{const{currentUser:d,emailVerified:u,sendVerificationEmail:m,reloadUser:h,signOut:x}=t(),[f,y]=s.useState(!1),[j,b]=s.useState(!1),[v,p]=s.useState(0),g=i();s.useEffect((()=>{if(!d)return void g("/signin");if(u)return void g("/");const e=setInterval((async()=>{try{await h()&&(n.success("Email verified successfully!"),g("/"))}catch(e){}}),5e3);return()=>clearInterval(e)}),[d,u,g,h]),s.useEffect((()=>{if(v>0){const e=setTimeout((()=>p(v-1)),1e3);return()=>clearTimeout(e)}0===v&&j&&b(!1)}),[v,j]);const k=async()=>{if(!j){y(!0);try{await m(),n.success("Verification email sent! Please check your inbox."),b(!0),p(60)}catch(e){const s=e instanceof Error?e.message:"Failed to send verification email";n.error(s)}finally{y(!1)}}};return e.jsx(r,{children:e.jsx("div",{className:"container mx-auto px-4 py-8 max-w-md",children:e.jsx("div",{className:"bg-white rounded-lg shadow-lg p-8",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"mx-auto bg-blue-100 rounded-full p-3 w-16 h-16 flex items-center justify-center mb-4",children:e.jsx(l,{className:"h-8 w-8 text-blue-600"})}),e.jsx("h1",{className:"text-2xl font-bold text-navy-800 font-playfair mb-2",children:"Verify Your Email"}),e.jsxs("p",{className:"text-gray-600 mb-4",children:["We've sent a verification email to ",e.jsx("strong",{children:null==d?void 0:d.email}),". Please check your inbox and click the verification link to activate your account."]}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6",children:[e.jsxs("p",{className:"text-blue-800 text-sm",children:[e.jsx("strong",{children:"Important:"})," You must verify your email before you can access all features. This helps ensure the security of your account and allows us to contact you about your books."]}),e.jsxs("p",{className:"text-blue-700 text-sm mt-2",children:[e.jsx("strong",{children:"Note:"})," We're automatically checking your verification status every few seconds. Once you click the verification link in your email, you'll be redirected automatically."]}),e.jsxs("p",{className:"text-blue-700 text-sm mt-2",children:[e.jsx("strong",{children:"While waiting for verification:"})," You can still browse books, view book details, and access public pages, but you won't be able to add books or access your dashboard until your email is verified."]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs(c,{onClick:k,disabled:f||j,className:"w-full flex items-center justify-center gap-2",children:[f&&j?e.jsx(o,{className:"h-4 w-4 animate-spin"}):e.jsx(l,{className:"h-4 w-4"}),j?`Resend Email (${v}s)`:f&&!j?"Sending...":"Resend Verification Email"]}),e.jsxs(c,{onClick:async()=>{y(!0);try{await h()?(n.success("Email verified successfully! Redirecting to dashboard..."),setTimeout((()=>g("/dashboard")),1500)):n.info("Your email is not verified yet. Please check your inbox and click the verification link.")}catch(e){const s=e instanceof Error?e.message:"Failed to check verification status";n.error(s)}finally{y(!1)}},variant:"secondary",disabled:f,className:"w-full flex items-center justify-center gap-2",children:[e.jsx(o,{className:"h-4 w-4 "+(f&&!j?"animate-spin":"")}),f&&!j?"Checking...":"I've Verified My Email"]}),e.jsx(a,{to:"/",children:e.jsx(c,{variant:"secondary",className:"w-full mb-2",children:"Continue Browsing Books"})}),e.jsx(c,{variant:"outline",onClick:async()=>{try{await x(),g("/signin")}catch(e){n.error("Failed to sign out")}},className:"w-full",children:"Sign Out"})]}),e.jsx("div",{className:"mt-6 text-sm text-gray-500",children:e.jsxs("p",{children:["Didn't receive the email? Check your spam folder or"," ",e.jsx("button",{onClick:k,disabled:j,className:"text-burgundy-500 hover:underline",children:"click here to resend"}),"."]})}),e.jsxs("div",{className:"mt-6 border-t border-gray-200 pt-6",children:[e.jsx("h3",{className:"text-sm font-medium text-gray-700 mb-2",children:"Troubleshooting Tips:"}),e.jsxs("ul",{className:"text-xs text-gray-600 space-y-1 list-disc pl-5",children:[e.jsx("li",{children:"Check your spam or junk folder"}),e.jsxs("li",{children:["Add ",e.jsx("span",{className:"font-mono",children:"<EMAIL>"})," to your contacts"]}),e.jsx("li",{children:'If using Gmail, check the "Promotions" or "Updates" tabs'}),e.jsx("li",{children:"Try using a different browser or device to verify"}),e.jsxs("li",{children:["If you're still having issues, contact support at ",e.jsx("a",{href:"mailto:<EMAIL>",className:"text-burgundy-500 hover:underline",children:"<EMAIL>"})]})]})]})]})})})})};export{d as default};
