import{j as e}from"./chunk-CpdlqjqK.js";import{r}from"./chunk-b0HHmiEU.js";import{x as o}from"./index-DwRq5Uwb.js";const s=r.forwardRef((({className:r,...s},i)=>e.jsx("textarea",{className:o("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:i,...s})));s.displayName="Textarea";export{s as T};
