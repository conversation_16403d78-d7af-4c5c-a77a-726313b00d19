import{j as e}from"./chunk-CpdlqjqK.js";import{r as s,L as a}from"./chunk-b0HHmiEU.js";import{H as o,c as l,r as t,s as r}from"./index-DwRq5Uwb.js";import{J as d}from"./chunk-Cw96wKwP.js";import"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const i=()=>{const[i,n]=s.useState(!1),[c,m]=s.useState(!1),[x,h]=s.useState(null);return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(o,{}),e.jsx("main",{className:"flex-grow",children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Seed Database"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"This utility page allows you to seed the database with real books for testing purposes. Use this only in development environments."}),x&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:x})}),c&&e.jsx("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:"Real books added to the database successfully!"})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx(l,{onClick:async()=>{if(confirm("Are you sure you want to seed the database with real books? This will add duplicate books if they already exist.")){n(!0),h(null),m(!1);try{await r(),m(!0),d.success("Real books added to the database successfully!")}catch(e){h("Failed to seed books. See console for details."),d.error("Failed to seed books")}finally{n(!1)}}},disabled:i,className:"w-full sm:w-auto",children:i?"Adding Real Books...":"Add Real Books"}),e.jsx(a,{to:"/browse",children:e.jsx(l,{variant:"outline",className:"w-full sm:w-auto",children:"Browse Books"})})]})]})})}),e.jsx(t,{})]})};export{i as default};
