/**
 * <PERSON>zy<PERSON>hart Component
 * 
 * Lazy-loaded wrapper for recharts components to reduce initial bundle size
 */

import React, { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy load recharts components
const BarChart = React.lazy(() => 
  import('recharts').then(module => ({ default: module.BarChart }))
);

const LineChart = React.lazy(() => 
  import('recharts').then(module => ({ default: module.LineChart }))
);

const PieChart = React.lazy(() => 
  import('recharts').then(module => ({ default: module.PieChart }))
);

const AreaChart = React.lazy(() => 
  import('recharts').then(module => ({ default: module.AreaChart }))
);

const ResponsiveContainer = React.lazy(() => 
  import('recharts').then(module => ({ default: module.ResponsiveContainer }))
);

const XAxis = React.lazy(() => 
  import('recharts').then(module => ({ default: module.XAxis }))
);

const YAxis = React.lazy(() => 
  import('recharts').then(module => ({ default: module.YAxis }))
);

const CartesianGrid = React.lazy(() => 
  import('recharts').then(module => ({ default: module.CartesianGrid }))
);

const Tooltip = React.lazy(() => 
  import('recharts').then(module => ({ default: module.Tooltip }))
);

const Legend = React.lazy(() => 
  import('recharts').then(module => ({ default: module.Legend }))
);

const Bar = React.lazy(() => 
  import('recharts').then(module => ({ default: module.Bar }))
);

const Line = React.lazy(() => 
  import('recharts').then(module => ({ default: module.Line }))
);

const Area = React.lazy(() => 
  import('recharts').then(module => ({ default: module.Area }))
);

const Pie = React.lazy(() => 
  import('recharts').then(module => ({ default: module.Pie }))
);

const Cell = React.lazy(() => 
  import('recharts').then(module => ({ default: module.Cell }))
);

// Chart loading skeleton
const ChartSkeleton: React.FC<{ height?: number }> = ({ height = 300 }) => (
  <div className="w-full" style={{ height }}>
    <Skeleton className="w-full h-full rounded-lg" />
  </div>
);

// Lazy chart wrapper components
interface LazyChartProps {
  children: React.ReactNode;
  height?: number;
  fallback?: React.ReactNode;
}

export const LazyBarChart: React.FC<LazyChartProps> = ({ 
  children, 
  height = 300, 
  fallback 
}) => (
  <Suspense fallback={fallback || <ChartSkeleton height={height} />}>
    {children}
  </Suspense>
);

export const LazyLineChart: React.FC<LazyChartProps> = ({ 
  children, 
  height = 300, 
  fallback 
}) => (
  <Suspense fallback={fallback || <ChartSkeleton height={height} />}>
    {children}
  </Suspense>
);

export const LazyPieChart: React.FC<LazyChartProps> = ({ 
  children, 
  height = 300, 
  fallback 
}) => (
  <Suspense fallback={fallback || <ChartSkeleton height={height} />}>
    {children}
  </Suspense>
);

export const LazyAreaChart: React.FC<LazyChartProps> = ({ 
  children, 
  height = 300, 
  fallback 
}) => (
  <Suspense fallback={fallback || <ChartSkeleton height={height} />}>
    {children}
  </Suspense>
);

// Export lazy-loaded components
export {
  BarChart,
  LineChart,
  PieChart,
  AreaChart,
  ResponsiveContainer,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Bar,
  Line,
  Area,
  Pie,
  Cell
};

// Default export for convenience
export default {
  BarChart: LazyBarChart,
  LineChart: LazyLineChart,
  PieChart: LazyPieChart,
  AreaChart: LazyAreaChart,
  ChartSkeleton
};
