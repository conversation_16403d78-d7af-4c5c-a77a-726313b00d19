const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/chunk-B1ZgTNj8.js","assets/chunk-CpdlqjqK.js","assets/chunk-b0HHmiEU.js"])))=>i.map(i=>d[i]);
import{j as e}from"./chunk-CpdlqjqK.js";import{r as s,L as a}from"./chunk-b0HHmiEU.js";import{v as t,_ as r,w as i,B as l,J as n,c,j as o,K as d,L as m}from"./index-DwRq5Uwb.js";import{J as h}from"./chunk-Cw96wKwP.js";import{D as x,a as j,b as p,c as g,d as u,e as b}from"./chunk-BjtUNXro.js";import{T as v}from"./chunk-DQKmWaJS.js";import{A as f}from"./chunk-BS5eevMn.js";import{P as k,R as y,m as N,w,X as C}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const T=()=>{const[T,F]=s.useState([]),[A,S]=s.useState(!0),[B,R]=s.useState(null),[P,z]=s.useState(null),[D,E]=s.useState(""),[L,_]=s.useState(!1),[I,V]=s.useState(!1),[$,O]=s.useState(!1),[U,G]=s.useState(!1);s.useEffect((()=>{J()}),[]);const J=async()=>{try{S(!0),R(null);const e=await n();F(e)}catch(e){e instanceof Error?R(`Failed to load pending books: ${e.message}`):R("Failed to load pending books. Please try again."),Error}finally{S(!1)}},K=async()=>{try{G(!0);await(async()=>{try{await t();const{collection:e,addDoc:s,serverTimestamp:a}=await r((async()=>{const{collection:e,addDoc:s,serverTimestamp:a}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{collection:e,addDoc:s,serverTimestamp:a}}),__vite__mapDeps([0,1,2])),n=e(i,"books"),c={title:"Test Pending Book",author:"Test Author",isbn:"1234567890123",genre:["Fiction","Test"],condition:"Good",description:"This is a test book created for testing the admin approval workflow.",imageUrl:"https://via.placeholder.com/150?text=Test+Book",perceivedValue:5,price:299,availability:"For Sale & Exchange",ownerId:"testuser123",ownerName:"Test User",ownerLocation:"Test Location",ownerRating:4.5,distance:2.5,createdAt:a(),updatedAt:a(),approvalStatus:l.Pending};return(await s(n,c)).id}catch(B){throw B}})();h.success("Test pending book created successfully"),J()}catch(e){h.error("Failed to create test book. Please try again.")}finally{G(!1)}};return e.jsxs(f,{title:"Book Approvals",description:"Review and approve new book submissions",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Book Approvals"}),e.jsx("p",{className:"text-gray-600",children:"Review and approve new book submissions"})]}),e.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[e.jsx(c,{variant:"outline",onClick:K,disabled:U||A,className:"flex items-center",children:U?e.jsxs(e.Fragment,{children:[e.jsx(o,{size:"sm",className:"mr-2"}),"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),"Create Test Book"]})}),e.jsx(c,{variant:"outline",onClick:J,disabled:A,children:A?e.jsxs(e.Fragment,{children:[e.jsx(y,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(y,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),B&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:B}),e.jsxs("details",{className:"mt-2",children:[e.jsx("summary",{className:"text-sm cursor-pointer",children:"Debug Information"}),e.jsxs("div",{className:"mt-2 text-xs",children:[e.jsx("p",{children:"If you're seeing this error, try the following:"}),e.jsxs("ol",{className:"list-decimal pl-5 mt-1 space-y-1",children:[e.jsx("li",{children:'Click the "Create Test Book" button to add a test pending book'}),e.jsx("li",{children:"Check your Firebase permissions and rules"}),e.jsx("li",{children:"Verify that your Firestore database has the correct structure"}),e.jsx("li",{children:"Check the browser console for more detailed error messages"})]})]})]})]}),A?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(o,{size:"lg"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading pending books..."})]}):T.length>0?e.jsx("div",{className:"space-y-6",children:T.map((s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md p-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"w-32 h-32 flex-shrink-0",children:e.jsx("img",{src:s.imageUrl||"https://via.placeholder.com/150?text=No+Image",alt:s.title,className:"w-full h-full object-cover rounded-md"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-xl font-medium text-navy-800",children:s.title}),e.jsxs("p",{className:"text-gray-600 mb-2",children:["by ",s.author]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 mb-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Owner:"})," ",s.ownerName]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Genre:"})," ",Array.isArray(s.genre)?s.genre.join(", "):s.genre]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Condition:"})," ",s.condition]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Availability:"})," ",s.availability]})]}),e.jsx("p",{className:"text-sm text-gray-700 mb-3 line-clamp-2",children:s.description}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(a,{to:`/books/${s.id}`,target:"_blank",rel:"noopener noreferrer",children:e.jsxs(c,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[e.jsx(N,{className:"h-4 w-4"}),"View Details"]})}),e.jsxs(c,{variant:"default",size:"sm",className:"flex items-center gap-1 bg-green-600 hover:bg-green-700",onClick:()=>(async e=>{try{V(!0),await d(e.id),h.success(`"${e.title}" has been approved`),F(T.filter((s=>s.id!==e.id)))}catch(s){h.error("Failed to approve book. Please try again.")}finally{V(!1)}})(s),disabled:I,children:[I?e.jsx(o,{size:"sm",className:"mr-1"}):e.jsx(w,{className:"h-4 w-4"}),"Approve"]}),e.jsxs(c,{variant:"destructive",size:"sm",className:"flex items-center gap-1",onClick:()=>(e=>{z(e),E(""),_(!0)})(s),disabled:$,children:[e.jsx(C,{className:"h-4 w-4"}),"Reject"]})]})]})]})},s.id)))}):e.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No pending books"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"All books have been reviewed. Check back later for new submissions."}),e.jsx(c,{variant:"outline",onClick:K,disabled:U,className:"mt-2",children:U?e.jsxs(e.Fragment,{children:[e.jsx(o,{size:"sm",className:"mr-2"}),"Creating Test Book..."]}):e.jsxs(e.Fragment,{children:[e.jsx(k,{className:"h-4 w-4 mr-2"}),"Create Test Book for Approval"]})}),e.jsx("p",{className:"text-xs text-gray-500 mt-4",children:"Click the button above to create a test book with pending approval status. This is useful for testing the approval workflow."})]}),e.jsx(x,{open:L,onOpenChange:_,children:e.jsxs(j,{children:[e.jsxs(p,{children:[e.jsx(g,{children:"Reject Book"}),e.jsxs(u,{children:['Please provide a reason for rejecting "',null==P?void 0:P.title,'". This will be visible to the user who submitted the book.']})]}),e.jsx(v,{placeholder:"Enter rejection reason...",value:D,onChange:e=>E(e.target.value),className:"min-h-[100px]"}),e.jsxs(b,{children:[e.jsx(c,{variant:"outline",onClick:()=>_(!1),children:"Cancel"}),e.jsx(c,{variant:"destructive",onClick:async()=>{if(P)try{O(!0),await m(P.id,D),h.success(`"${P.title}" has been rejected`),F(T.filter((e=>e.id!==P.id))),_(!1)}catch(e){h.error("Failed to reject book. Please try again.")}finally{O(!1)}},disabled:!D.trim()||$,children:$?e.jsxs(e.Fragment,{children:[e.jsx(o,{size:"sm",className:"mr-2"}),"Rejecting..."]}):"Confirm Rejection"})]})]})})]})};export{T as default};
