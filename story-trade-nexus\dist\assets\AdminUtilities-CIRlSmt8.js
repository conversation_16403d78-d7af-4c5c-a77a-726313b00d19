import{j as e}from"./chunk-CpdlqjqK.js";import{r as s}from"./chunk-b0HHmiEU.js";import{c as t,j as a,s as i}from"./index-DwRq5Uwb.js";import{J as r}from"./chunk-Cw96wKwP.js";import{D as o,a as l,b as c,c as n,d,e as m}from"./chunk-BjtUNXro.js";import{A as u}from"./chunk-BS5eevMn.js";import{D as h,T as x}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const j=()=>{const[j,p]=s.useState(!1),[g,f]=s.useState(!1),[b,y]=s.useState(null),k=e=>{y(e),f(!0)},v=[{title:"Seed Sample Books",description:"Add sample books to the database for testing",icon:e.jsx(h,{className:"h-8 w-8 text-burgundy-500"}),action:()=>k("seed"),color:"bg-burgundy-50"},{title:"Purge Test Data",description:"Remove test data from the database",icon:e.jsx(x,{className:"h-8 w-8 text-red-500"}),action:()=>k("purge"),color:"bg-red-50"}];return e.jsxs(u,{title:"Admin Tools",description:"Access administrative utilities and functions",children:[e.jsx("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Admin Tools"}),e.jsx("p",{className:"text-gray-600",children:"Access admin tools and utilities"})]})}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:v.map(((s,a)=>e.jsx("div",{className:`rounded-lg shadow-md p-6 transition-all hover:shadow-lg ${s.color}`,children:e.jsxs("div",{className:"flex flex-col items-center text-center",children:[s.icon,e.jsx("h2",{className:"text-xl font-semibold mt-4 mb-2",children:s.title}),e.jsx("p",{className:"text-gray-600 mb-4",children:s.description}),e.jsx(t,{variant:"outline",className:"w-full",onClick:s.action,children:"Run Utility"})]})},a)))}),e.jsx(o,{open:g,onOpenChange:f,children:e.jsxs(l,{children:[e.jsxs(c,{children:[e.jsx(n,{children:"seed"===b?"Seed Sample Books":"Purge Test Data"}),e.jsx(d,{children:"seed"===b?"Are you sure you want to add sample books to the database? This will create duplicate books if they already exist.":"Are you sure you want to purge test data from the database? This action cannot be undone."})]}),e.jsxs(m,{children:[e.jsx(t,{variant:"outline",onClick:()=>f(!1),children:"Cancel"}),e.jsx(t,{variant:"purge"===b?"destructive":"default",onClick:()=>{"seed"===b?(async()=>{try{p(!0),await i(),r.success("Sample books added to the database successfully!"),f(!1)}catch(e){r.error("Failed to seed books. Please try again.")}finally{p(!1)}})():"purge"===b&&(async()=>{try{r.success("Data purged successfully!"),f(!1)}catch(e){r.error("Failed to purge data. Please try again.")}})()},disabled:j,children:j?e.jsxs(e.Fragment,{children:[e.jsx(a,{size:"sm",className:"mr-2"}),"Processing..."]}):"Confirm"})]})]})})]})};export{j as default};
