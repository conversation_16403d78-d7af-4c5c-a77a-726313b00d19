import{j as e}from"./chunk-CpdlqjqK.js";import{r as s,e as a,c as t,R as i,L as l}from"./chunk-b0HHmiEU.js";import{x as n,N as r,c as o,u as c,y as d,a1 as m,H as u,I as h,S as x,f as g,r as p}from"./index-DwRq5Uwb.js";import{S as f,a as b,b as j,c as v,d as y}from"./chunk-BpJWUrWd.js";import{a4 as N,w,Y as P,_ as k,a5 as C,S,i as I,R as A,W as B}from"./chunk-BGoCADfv.js";import{J as F}from"./chunk-Cw96wKwP.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const L=[{value:"community-distance",label:"Community + Distance",description:"Same community first, then by distance"},{value:"price-low-high",label:"Price: Low to High",description:"Selling price from lowest to highest"},{value:"price-high-low",label:"Price: High to Low",description:"Selling price from highest to lowest"},{value:"rental-low-high",label:"Rental: Low to High",description:"Rental price from lowest to highest"},{value:"rental-high-low",label:"Rental: High to Low",description:"Rental price from highest to lowest"},{value:"distance",label:"Distance",description:"Closest to farthest"},{value:"newest-first",label:"Newest First",description:"Most recently added books first"},{value:"oldest-first",label:"Oldest First",description:"Oldest books first"}],R=e=>e.availability.includes("Sale")&&e.price?e.price:null,$=e=>{var s;if(!e.availability.includes("Rent")||!e.rentalPrice)return null;const a=e.rentalPrice,t=(null==(s=e.rentalPeriod)?void 0:s.toLowerCase())||"per day";return t.includes("week")?a/7:t.includes("month")?a/30:t.includes("year")?a/365:a},M=e=>(e.createdAt instanceof Date?e.createdAt:new Date(e.createdAt)).getTime(),T=e=>L.filter((s=>((e,s)=>{switch(s){case"price-low-high":case"price-high-low":return e.some((e=>null!==R(e)));case"rental-low-high":case"rental-high-low":return e.some((e=>null!==$(e)));case"distance":case"community-distance":return e.some((e=>void 0!==e.distance));default:return!0}})(e,s.value))),E=({sortCriteria:s,onSortChange:a,books:t,disabled:i=!1,className:l=""})=>{const n=T(t),r=L.find((e=>e.value===s));return e.jsx("div",{className:l,children:e.jsxs(f,{value:s,onValueChange:e=>a(e),disabled:i,children:[e.jsx(b,{className:"h-10 w-full",children:e.jsxs("div",{className:"flex items-center gap-2 w-full",children:[e.jsx(N,{className:"h-4 w-4 text-gray-500 flex-shrink-0"}),e.jsx(j,{placeholder:"Sort by...",children:e.jsx("span",{className:"text-sm",children:(null==r?void 0:r.label)||"Sort by..."})})]})}),e.jsx(v,{children:n.map((a=>{const i=(e=>{switch(e){case"price-low-high":case"price-high-low":return t.filter((e=>e.availability.includes("Sale")&&e.price)).length;case"rental-low-high":case"rental-high-low":return t.filter((e=>e.availability.includes("Rent")&&e.rentalPrice)).length;case"distance":case"community-distance":return t.filter((e=>void 0!==e.distance)).length;default:return t.length}})(a.value),l=a.value===s;return e.jsx(y,{value:a.value,className:"cursor-pointer",children:e.jsx("div",{className:"flex items-start justify-between w-full",children:e.jsxs("div",{className:"flex flex-col flex-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"font-medium",children:a.label}),l&&e.jsx(w,{className:"h-4 w-4 text-burgundy-600"})]}),e.jsx("span",{className:"text-xs text-gray-500 mt-1",children:a.description}),i<t.length&&e.jsxs("span",{className:"text-xs text-blue-600 mt-1",children:[i," of ",t.length," books have this data"]})]})})},a.value)}))})]})})},D=({className:s,...a})=>e.jsx("nav",{role:"navigation","aria-label":"pagination",className:n("mx-auto flex w-full justify-center",s),...a});D.displayName="Pagination";const z=s.forwardRef((({className:s,...a},t)=>e.jsx("ul",{ref:t,className:n("flex flex-row items-center gap-1",s),...a})));z.displayName="PaginationContent";const G=s.forwardRef((({className:s,...a},t)=>e.jsx("li",{ref:t,className:n("",s),...a})));G.displayName="PaginationItem";const H=({className:s,isActive:a,size:t="icon",...i})=>e.jsx("a",{"aria-current":a?"page":void 0,className:n(r({variant:a?"outline":"ghost",size:t}),s),...i});H.displayName="PaginationLink";const O=({className:s,...a})=>e.jsxs(H,{"aria-label":"Go to previous page",size:"default",className:n("gap-1 pl-2.5",s),...a,children:[e.jsx(P,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]});O.displayName="PaginationPrevious";const Y=({className:s,...a})=>e.jsxs(H,{"aria-label":"Go to next page",size:"default",className:n("gap-1 pr-2.5",s),...a,children:[e.jsx("span",{children:"Next"}),e.jsx(k,{className:"h-4 w-4"})]});Y.displayName="PaginationNext";const J=({className:s,...a})=>e.jsxs("span",{"aria-hidden":!0,className:n("flex h-9 w-9 items-center justify-center",s),...a,children:[e.jsx(C,{className:"h-4 w-4"}),e.jsx("span",{className:"sr-only",children:"More pages"})]});J.displayName="PaginationEllipsis";const U=(e,s,a=12)=>{const t=Math.max(1,s),i=Math.max(1,Math.ceil(e/a)),l=Math.min(t,i),n=(l-1)*a;return{currentPage:l,totalPages:i,totalItems:e,itemsPerPage:a,startIndex:n,endIndex:Math.min(n+a-1,e-1),hasNextPage:l<i,hasPreviousPage:l>1,pageNumbers:V(l,i)}},V=(e,s)=>{if(s<=7)return Array.from({length:s},((e,s)=>s+1));const a=[];if(e<=4)a.push(1,2,3,4,5),s>6&&a.push(-1),a.push(s);else if(e>=s-3){a.push(1),s>6&&a.push(-1);for(let e=s-4;e<=s;e++)a.push(e)}else a.push(1),a.push(-1),a.push(e-1,e,e+1),a.push(-1),a.push(s);return a},W=e=>{if(0===e.totalItems)return"No books found";const s=e.startIndex+1,a=Math.min(e.endIndex+1,e.totalItems);return 1===e.totalPages?`Showing ${e.totalItems} book${1===e.totalItems?"":"s"}`:`Showing ${s}-${a} of ${e.totalItems} books`},_=(e,s)=>`Go to page ${e} of ${s}`,q=e=>{if(null==e)return 1;const s="string"==typeof e?parseInt(e,10):e;return isNaN(s)||s<1?1:s},K=(e,s,a=!1)=>{const t=new URLSearchParams(e);return a||1===s?t.delete("page"):t.set("page",s.toString()),t},Q=({pagination:s,onPageChange:a,onNextPage:t,onPreviousPage:i,className:l,showInfo:r=!0,compact:c=!1})=>{const{currentPage:d,totalPages:m,hasNextPage:u,hasPreviousPage:h,pageNumbers:x}=s;if(m<=1)return r&&s.totalItems>0?e.jsx("div",{className:n("flex justify-center py-4",l),children:e.jsx("p",{className:"text-sm text-gray-600",children:W(s)})}):null;const g=()=>{h&&i()},p=()=>{u&&t()};return e.jsxs("div",{className:n("flex flex-col items-center space-y-4 py-6",l),children:[r&&e.jsx("div",{className:"text-sm text-gray-600 text-center",children:W(s)}),e.jsx(D,{children:e.jsxs(z,{children:[e.jsx(G,{children:e.jsx(O,{onClick:g,className:n("cursor-pointer",!h&&"pointer-events-none opacity-50"),"aria-disabled":!h})}),!c&&x.map(((s,t)=>e.jsx(G,{children:-1===s?e.jsx(J,{}):e.jsx(H,{onClick:()=>{var e;(e=s)!==d&&e>=1&&e<=m&&a(e)},isActive:s===d,className:"cursor-pointer","aria-label":_(s,m),children:s})},t))),c&&e.jsx(G,{children:e.jsxs("span",{className:"px-3 py-2 text-sm",children:["Page ",d," of ",m]})}),e.jsx(G,{children:e.jsx(Y,{onClick:p,className:n("cursor-pointer",!u&&"pointer-events-none opacity-50"),"aria-disabled":!u})})]})}),e.jsxs("div",{className:"flex sm:hidden items-center space-x-2",children:[e.jsxs(o,{variant:"outline",size:"sm",onClick:g,disabled:!h,className:"flex items-center space-x-1",children:[e.jsx(P,{className:"h-4 w-4"}),e.jsx("span",{children:"Previous"})]}),e.jsxs("span",{className:"px-3 py-1 text-sm bg-gray-100 rounded",children:[d," / ",m]}),e.jsxs(o,{variant:"outline",size:"sm",onClick:p,disabled:!u,className:"flex items-center space-x-1",children:[e.jsx("span",{children:"Next"}),e.jsx(k,{className:"h-4 w-4"})]})]})]})},X="peerbooks-sort-preference",Z=()=>{const[e,a]=s.useState("community-distance");s.useEffect((()=>{try{const e=localStorage.getItem(X);if(e){const s=e;["community-distance","price-low-high","price-high-low","rental-low-high","rental-high-low","distance","newest-first","oldest-first"].includes(s)&&a(s)}}catch(e){}}),[]);const t=s.useCallback((e=>{a(e);try{localStorage.setItem(X,e)}catch(s){}}),[]),i=s.useCallback(((s,a)=>((e,s,a)=>e&&0!==e.length?[...e].sort(((e,t)=>{if("distance"!==s){const s=a&&e.ownerCommunity&&e.ownerCommunity===a,i=a&&t.ownerCommunity&&t.ownerCommunity===a;if(s&&!i)return-1;if(i&&!s)return 1}switch(s){case"community-distance":case"distance":return void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:void 0!==e.distance?-1:void 0!==t.distance?1:M(t)-M(e);case"price-low-high":{const s=R(e),a=R(t);return null!==s&&null!==a?s-a:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:M(t)-M(e)}case"price-high-low":{const s=R(e),a=R(t);return null!==s&&null!==a?a-s:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:M(t)-M(e)}case"rental-low-high":{const s=$(e),a=$(t);return null!==s&&null!==a?s-a:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:M(t)-M(e)}case"rental-high-low":{const s=$(e),a=$(t);return null!==s&&null!==a?a-s:null!==s?-1:null!==a?1:void 0!==e.distance&&void 0!==t.distance?e.distance-t.distance:M(t)-M(e)}case"newest-first":default:return M(t)-M(e);case"oldest-first":return M(e)-M(t)}})):[])(s,e,a)),[e]),l=s.useCallback((()=>{t("community-distance")}),[t]);return{sortCriteria:e,setSortCriteria:t,sortBooks:i,resetSort:l}},ee=(e,i,l,n,r)=>((e,i=[],l={})=>{const{itemsPerPage:n=12,resetOnDependencyChange:r=!0}=l,[o,c]=a();t();const d=q(o.get("page")),[m,u]=s.useState(d),[h,x]=s.useState(e),g=s.useMemo((()=>U(h,m,n)),[h,m,n]);s.useEffect((()=>{const e=q(o.get("page"));e!==m&&u(e)}),[o,m]),s.useEffect((()=>{x(e)}),[e]),s.useEffect((()=>{r&&i.length>0&&m>1&&j()}),i),s.useEffect((()=>{m>g.totalPages&&g.totalPages>0&&p(g.totalPages)}),[m,g.totalPages]);const p=s.useCallback((e=>{const s=Math.max(1,Math.min(e,g.totalPages||1));if(s!==m){u(s);const e=K(o,s);c(e,{replace:!0})}}),[m,g.totalPages,o,c]),f=s.useCallback((()=>{g.hasNextPage&&p(m+1)}),[m,g.hasNextPage,p]),b=s.useCallback((()=>{g.hasPreviousPage&&p(m-1)}),[m,g.hasPreviousPage,p]),j=s.useCallback((()=>{if(1!==m){u(1);const e=K(o,1,!0);c(e,{replace:!0})}}),[m,o,c]),v=s.useCallback((e=>{x(e)}),[]);return{currentPage:m,pagination:g,goToPage:p,goToNextPage:f,goToPreviousPage:b,resetToFirstPage:j,updateTotalItems:v}})(e,[i,l,n,r],{itemsPerPage:12,resetOnDependencyChange:!0}),se=()=>{var a;const{userData:t}=c(),{sortCriteria:n,setSortCriteria:r,sortBooks:f}=Z(),[b,j]=s.useState(""),[v,y]=s.useState("All"),[w,P]=s.useState("All"),[k,C]=s.useState([]),[R,$]=s.useState(0),[M,T]=s.useState(!0),[D,z]=s.useState(null),[G,H]=s.useState(null),O=ee(R,b,v,w,n);s.useEffect((()=>{Y()}),[]);const Y=async()=>{try{T(!0),z(null),H("loading"),F.info("Getting your location to find nearby books...",{duration:3e3,id:"location-toast"});const e=null==t?void 0:t.community,s=await d(!1,e);if(s.some((e=>void 0!==e.distance))){H("success");const s=e?`Books sorted by community (${e} first) and distance`:"Books sorted by distance (closest first)";F.success(s,{id:"location-toast",duration:4e3})}else{H("error");const s=e?`Books sorted by community (${e} first) and newest first`:"Books sorted by newest first";F.info(s,{id:"location-toast",duration:3e3})}C(s)}catch(e){H("error"),e instanceof Error?(z(`Failed to load books: ${e.message}. Please try again.`),(e.message.includes("permission")||e.message.includes("denied"))&&(H("denied"),F.error("Location access denied. Books sorted by newest first.",{id:"location-toast",duration:5e3}))):z("Failed to load books. Please try again.")}finally{T(!1)}},J=()=>{Y()},V=k.filter((e=>{const s=""===b||e.title.toLowerCase().includes(b.toLowerCase())||e.author.toLowerCase().includes(b.toLowerCase()),a="All"===v||e.genre.includes(v),t="All"===w||e.availability.includes(w);return s&&a&&t})),W=f(V,null==t?void 0:t.community);i.useEffect((()=>{O.updateTotalItems(W.length)}),[W.length,O]);const _=((e,s,a=12)=>{const t=U(e.length,s,a),i=t.startIndex,l=i+a;return{items:e.slice(i,l),pagination:t}})(W,O.currentPage,12),q=_.items;return m(q),e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(u,{}),e.jsx("main",{className:"flex-grow bg-beige-50",children:e.jsxs("div",{className:"container mx-auto px-4 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-2",children:"Browse Books"}),e.jsx("p",{className:"text-gray-600",children:"Discover books available for exchange, rent, or purchase"})]}),e.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-md mb-8",children:[e.jsxs("div",{className:"hidden sm:grid sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-2",children:[e.jsx("div",{className:"sm:col-span-2 lg:col-span-1",children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Search"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Genre"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Availability"})}),e.jsx("div",{children:e.jsx("label",{className:"text-xs font-medium text-gray-700 uppercase tracking-wide",children:"Sort By"})})]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"relative sm:col-span-2 lg:col-span-1",children:[e.jsx(S,{className:"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-500"}),e.jsx(h,{type:"text",placeholder:"Search by title or author...",className:"pl-10 h-10",value:b,onChange:e=>j(e.target.value),disabled:M})]}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:v,onChange:e=>y(e.target.value),disabled:M,"aria-label":"Filter by genre",children:["All","Fiction","Classics","Fantasy","Young Adult","Philosophy","Romance","Dystopian"].map((s=>e.jsx("option",{value:s,children:s},s)))})}),e.jsx("div",{children:e.jsx("select",{className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",value:w,onChange:e=>P(e.target.value),disabled:M,"aria-label":"Filter by availability",children:["All","For Rent","For Sale","For Exchange"].map((s=>e.jsx("option",{value:s,children:s},s)))})}),e.jsx("div",{children:e.jsx(E,{sortCriteria:n,onSortChange:r,books:V,disabled:M})})]}),"community-distance"!==n&&e.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:e.jsxs("div",{className:"flex items-center gap-2 text-sm text-burgundy-700 bg-burgundy-50 px-3 py-2 rounded-md",children:[e.jsx(N,{className:"h-4 w-4"}),e.jsx("span",{className:"font-medium",children:"Active sort:"}),e.jsx("span",{children:null==(a=L.find((e=>e.value===n)))?void 0:a.label}),e.jsx("button",{onClick:()=>r("community-distance"),className:"ml-auto text-xs text-burgundy-600 hover:text-burgundy-800 underline",children:"Reset to default"})]})})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row items-start sm:items-center gap-2 text-sm text-gray-600",children:[e.jsx("span",{className:"font-medium",children:W.length>0?e.jsxs(e.Fragment,{children:["Showing ",_.pagination.startIndex+1,"-",Math.min(_.pagination.endIndex+1,W.length)," of ",W.length," books",_.pagination.totalPages>1&&e.jsxs("span",{className:"ml-1",children:["(Page ",_.pagination.currentPage," of ",_.pagination.totalPages,")"]})]}):`0 of ${k.length} books`}),(b||"All"!==v||"All"!==w)&&e.jsx("span",{className:"text-blue-600",children:"(filtered)"})]}),e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsxs("div",{className:"flex items-center",children:["loading"===G&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(I,{className:"h-4 w-4 mr-1 text-gray-400 animate-pulse"}),e.jsx("span",{children:"Getting your location..."})]}),"success"===G&&e.jsxs("div",{className:"flex items-center text-sm text-green-600",children:[e.jsx(I,{className:"h-4 w-4 mr-1 text-green-500"}),e.jsx("span",{children:(null==t?void 0:t.community)?`Books sorted by community (${t.community} first) and distance`:"Books sorted by distance (closest first)"})]}),"error"===G&&e.jsxs("div",{className:"flex items-center text-sm text-gray-600",children:[e.jsx(I,{className:"h-4 w-4 mr-1 text-gray-400"}),e.jsx("span",{children:(null==t?void 0:t.community)?`Books sorted by community (${t.community} first) and newest first`:"Books sorted by newest first"})]}),"denied"===G&&e.jsxs("div",{className:"flex items-center text-sm text-amber-600",children:[e.jsx(I,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("span",{children:"Location access denied. Books sorted by newest first."})]})]}),e.jsx(o,{variant:"outline",onClick:J,disabled:M,className:"text-sm",children:M?e.jsxs(e.Fragment,{children:[e.jsx(A,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(A,{className:"h-4 w-4 mr-2"}),"Refresh Books"]})})]})]}),D&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:D}),e.jsx(o,{variant:"link",onClick:J,className:"text-red-700 p-0 h-auto text-sm",children:"Try Again"})]}),M?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map(((s,a)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[e.jsx(x,{className:"h-64 w-full"}),e.jsxs("div",{className:"p-4",children:[e.jsx(x,{className:"h-6 w-3/4 mb-2"}),e.jsx(x,{className:"h-4 w-1/2 mb-4"}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(x,{className:"h-8 w-20"}),e.jsx(x,{className:"h-8 w-20"})]})]})]},a)))}):W.length>0?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:q.map(((s,a)=>e.jsx(g,{book:s,index:a,priority:a<8},s.id)))}),e.jsx(Q,{pagination:_.pagination,onPageChange:O.goToPage,onNextPage:O.goToNextPage,onPreviousPage:O.goToPreviousPage,className:"mt-8",showInfo:!1})]}):0===k.length?e.jsxs("div",{className:"text-center py-16 bg-beige-50 rounded-lg",children:[e.jsx(B,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-700 mb-2",children:"No Books Available Yet"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Be the first to add books to our community!"}),e.jsx(l,{to:"/add-books",children:e.jsx(o,{children:"Add Your Books"})})]}):e.jsxs("div",{className:"text-center py-16",children:[e.jsx("p",{className:"text-lg text-gray-600 mb-2",children:"No books found matching your criteria"}),e.jsx("p",{className:"text-burgundy-500",children:"Try adjusting your filters or search term"})]})]})}),e.jsx(p,{})]})};export{se as default};
