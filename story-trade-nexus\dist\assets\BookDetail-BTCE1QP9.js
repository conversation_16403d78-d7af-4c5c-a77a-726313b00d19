const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/OwnerInformation-CalKROJ8.js","assets/chunk-CpdlqjqK.js","assets/chunk-b0HHmiEU.js","assets/index-DwRq5Uwb.js","assets/chunk-Cw96wKwP.js","assets/chunk-BGoCADfv.js","assets/chunk-BvIisuNF.js","assets/chunk-D1Z7_RhR.js","assets/chunk-DgNUPAoM.js","assets/index-BGvwYOHI.css","assets/chunk-B1ZgTNj8.js","assets/contactService-DqoJaZpA.js","assets/index.esm-CptSyBEO.js"])))=>i.map(i=>d[i]);
import{k as e,$ as s,S as t,_ as a,u as n,M as r,c as l,b as i,a0 as o,w as c}from"./index-DwRq5Uwb.js";import{j as d}from"./chunk-CpdlqjqK.js";import{r as u,R as m,d as x,u as h,L as g}from"./chunk-b0HHmiEU.js";import{J as f}from"./chunk-Cw96wKwP.js";import{getCurrentPosition as p,reverseGeocode as w,getBasicLocationInfo as b,calculateDistance as j}from"./geolocationUtils-DEQwKbhP.js";import{Z as N,S as y,Y as v,_ as k,$ as S,a0 as C,h as A,a1 as I,c as $,a2 as E,a3 as P}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const F=({amount:e,className:s=""})=>{const t=e.toLocaleString("en-IN");return d.jsxs("span",{className:s,children:["Rs. ",t]})},R=({availability:s,className:t=""})=>{const a="For Exchange"===s||"For Rent & Exchange"===s||"For Sale & Exchange"===s||"For Rent, Sale & Exchange"===s,n="For Rent"===s||"For Rent & Sale"===s||"For Rent & Exchange"===s||"For Rent, Sale & Exchange"===s,r="For Sale"===s||"For Rent & Sale"===s||"For Sale & Exchange"===s||"For Rent, Sale & Exchange"===s;return d.jsxs("div",{className:`flex flex-wrap gap-2 mb-5 ${t}`,children:[n&&d.jsx(e,{className:"bg-blue-500 text-white hover:bg-blue-600 px-3 py-1 rounded-full",children:"For Rent"}),r&&d.jsx(e,{className:"bg-green-500 text-white hover:bg-green-600 px-3 py-1 rounded-full",children:"For Sale"}),a&&d.jsx(e,{className:"bg-purple-500 text-white hover:bg-purple-600 px-3 py-1 rounded-full",children:"For Exchange"})]})},O=({images:e,initialIndex:t=0,alt:a,className:n="",maxZoomLevel:r=2.5,containerHeight:l="400px"})=>{const[i,o]=u.useState(t),[c,m]=u.useState(!1),[x,h]=u.useState({}),[g,f]=u.useState({}),[p,w]=u.useState(!1),[b,j]=u.useState(r),[S,C]=u.useState({x:.5,y:.5}),[A,I]=u.useState(!0),[$,E]=u.useState(!1),[P,F]=u.useState({x:0,y:0}),[R,O]=u.useState(150),D=u.useRef(null),L=u.useRef(null),T=u.useRef(null),M=u.useRef(null),_=u.useRef([]),z=u.useRef(null);u.useEffect((()=>{_.current=Array(e.length).fill(null)}),[e.length]),u.useEffect((()=>{const s=s=>{if(s>=0&&s<e.length&&!g[s]){const t=new Image;t.src=e[s],t.onload=()=>{f((e=>({...e,[s]:!0})))}}};s(i),s(i-1<0?e.length-1:i-1),s(i+1>=e.length?0:i+1)}),[i,e,g]);const B=e=>{h((s=>({...s,[e]:!0})))},U=u.useCallback((e=>{M.current&&(z.current&&cancelAnimationFrame(z.current),z.current=requestAnimationFrame((()=>{const{left:s,top:t,width:a,height:n}=M.current.getBoundingClientRect(),r=Math.max(0,Math.min(1,(e.clientX-s)/a)),l=Math.max(0,Math.min(1,(e.clientY-t)/n));C({x:r,y:l}),F({x:e.clientX-s-R/2,y:e.clientY-t-R/2})})))}),[R]),W=u.useCallback((()=>{I(!0),L.current&&clearTimeout(L.current),L.current=setTimeout((()=>{I(!1)}),1500)}),[]),q=u.useCallback((()=>{w(!1),E(!1),I(!1),L.current&&clearTimeout(L.current),z.current&&(cancelAnimationFrame(z.current),z.current=null)}),[]),H=u.useCallback((e=>{e.target!==e.currentTarget&&"IMG"!==e.target.tagName||(p?$?(w(!1),E(!1)):E(!0):(w(!0),E(!1)))}),[p,$]);return u.useEffect((()=>()=>{D.current&&clearTimeout(D.current),L.current&&clearTimeout(L.current),T.current&&clearTimeout(T.current),z.current&&cancelAnimationFrame(z.current)}),[]),e&&0!==e.length?1===e.length?d.jsx("div",{className:`relative w-full ${n}`,style:{height:l},children:d.jsxs("div",{ref:M,className:"overflow-hidden h-full w-full flex items-center justify-center p-4 bg-white relative cursor-zoom-in",onMouseMove:U,onMouseEnter:W,onMouseLeave:q,onClick:H,children:[d.jsx(s,{src:e[0],alt:`${a}`,className:"w-full h-full object-contain drop-shadow-sm transition-all duration-300 ease-out",style:{objectFit:"contain",maxHeight:"100%",maxWidth:"100%",transform:p&&!$?`scale(${b}) translate(${-100*(.5-S.x)}%, ${-100*(.5-S.y)}%)`:"scale(1)",transformOrigin:p?`${100*S.x}% ${100*S.y}%`:"center center",filter:$?"brightness(0.9)":"none"},onLoad:()=>B(0),loading:"eager"}),$&&d.jsx("div",{className:"absolute rounded-full overflow-hidden border-2 border-white shadow-lg pointer-events-none z-30",style:{width:`${R}px`,height:`${R}px`,left:`${P.x}px`,top:`${P.y}px`,backgroundImage:`url(${e[0]})`,backgroundPosition:`calc(${100*S.x}% + ${R/2}px - ${S.x*R}px) calc(${100*S.y}% + ${R/2}px - ${S.y*R}px)`,backgroundSize:100*b+"%",backgroundRepeat:"no-repeat"}}),A&&d.jsx("div",{className:"absolute top-2 right-2 bg-black/70 text-white rounded-full p-2 transition-opacity duration-300 flex items-center gap-1.5 z-20",children:p?$?d.jsxs(d.Fragment,{children:[d.jsx(N,{className:"h-4 w-4 rotate-180"}),d.jsx("span",{className:"text-xs",children:"Click to reset"})]}):d.jsxs(d.Fragment,{children:[d.jsx(y,{className:"h-4 w-4"}),d.jsx("span",{className:"text-xs",children:"Click for magnifier"})]}):d.jsxs(d.Fragment,{children:[d.jsx(N,{className:"h-4 w-4"}),d.jsx("span",{className:"text-xs",children:"Click to zoom"})]})})]})}):d.jsx("div",{className:`relative w-full ${n}`,style:{height:l},children:d.jsxs("div",{className:"relative h-full w-full flex items-center justify-center overflow-hidden",children:[d.jsxs("div",{ref:M,className:"overflow-hidden h-full w-full flex items-center justify-center p-4 bg-white transition-opacity duration-150 ease-in-out relative cursor-zoom-in "+(c?"opacity-70":"opacity-100"),onMouseMove:U,onMouseEnter:W,onMouseLeave:q,onClick:H,children:[d.jsx(s,{src:e[i],alt:`${a} - Image ${i+1}`,className:"w-full h-full object-contain drop-shadow-sm transition-all duration-300 ease-out",style:{objectFit:"contain",maxHeight:"100%",maxWidth:"100%",transform:p&&!$?`scale(${b}) translate(${-100*(.5-S.x)}%, ${-100*(.5-S.y)}%)`:c?"scale(0.95)":"scale(1)",transformOrigin:p?`${100*S.x}% ${100*S.y}%`:"center center",pointerEvents:p?"none":"auto",filter:$?"brightness(0.9)":"none"},onLoad:()=>B(i),loading:0===i?"eager":"lazy"},`image-${i}`),$&&d.jsx("div",{className:"absolute rounded-full overflow-hidden border-2 border-white shadow-lg pointer-events-none z-30",style:{width:`${R}px`,height:`${R}px`,left:`${P.x}px`,top:`${P.y}px`,backgroundImage:`url(${e[i]})`,backgroundPosition:`calc(${100*S.x}% + ${R/2}px - ${S.x*R}px) calc(${100*S.y}% + ${R/2}px - ${S.y*R}px)`,backgroundSize:100*b+"%",backgroundRepeat:"no-repeat"}}),A&&d.jsx("div",{className:"absolute top-2 right-2 bg-black/70 text-white rounded-full p-2 transition-opacity duration-300 flex items-center gap-1.5 z-20",children:p?$?d.jsxs(d.Fragment,{children:[d.jsx(N,{className:"h-4 w-4 rotate-180"}),d.jsx("span",{className:"text-xs",children:"Click to reset"})]}):d.jsxs(d.Fragment,{children:[d.jsx(y,{className:"h-4 w-4"}),d.jsx("span",{className:"text-xs",children:"Click for magnifier"})]}):d.jsxs(d.Fragment,{children:[d.jsx(N,{className:"h-4 w-4"}),d.jsx("span",{className:"text-xs",children:"Click to zoom"})]})})]}),d.jsx("button",{onClick:()=>{if(c||e.length<=1)return;m(!0);const s=0===i?e.length-1:i-1;D.current&&clearTimeout(D.current),D.current=setTimeout((()=>{o(s),m(!1)}),150)},className:"absolute left-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-r-md z-40 transition-colors duration-200",disabled:c,"aria-label":"Previous image",children:d.jsx(v,{className:"h-6 w-6"})}),d.jsx("button",{onClick:()=>{if(c||e.length<=1)return;m(!0);const s=i===e.length-1?0:i+1;D.current&&clearTimeout(D.current),D.current=setTimeout((()=>{o(s),m(!1)}),150)},className:"absolute right-0 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-1 rounded-l-md z-40 transition-colors duration-200",disabled:c,"aria-label":"Next image",children:d.jsx(k,{className:"h-6 w-6"})}),d.jsxs("div",{className:"absolute bottom-2 left-1/2 -translate-x-1/2 bg-black/50 text-white px-2 py-1 rounded-md text-sm z-40",children:[i+1," / ",e.length]}),d.jsx("div",{className:"hidden",children:e.map(((e,s)=>s!==i&&d.jsx("img",{src:e,alt:"",onLoad:()=>B(s)},`preload-${s}`)))})]})}):null},D=()=>d.jsxs("div",{className:"w-full bg-white rounded-lg p-5 border border-gray-200 shadow-sm",children:[d.jsx(t,{className:"h-6 w-40 mb-4"}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsx("div",{className:"flex flex-col p-3 bg-gray-50 rounded-md",children:d.jsxs("div",{className:"flex items-center",children:[d.jsx(t,{className:"h-5 w-5 mr-3 rounded"}),d.jsx(t,{className:"h-5 w-24"})]})}),d.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[d.jsx(t,{className:"h-5 w-5 mr-3 rounded"}),d.jsxs("div",{className:"flex-1",children:[d.jsx(t,{className:"h-4 w-32 mb-1"}),d.jsx(t,{className:"h-3 w-20"})]}),d.jsx(t,{className:"h-6 w-6 ml-2 rounded"})]}),d.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[d.jsx(t,{className:"h-5 w-5 mr-3 rounded"}),d.jsx(t,{className:"h-5 w-20"})]}),d.jsx("div",{className:"p-3 bg-gray-50 rounded-md",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx(t,{className:"h-4 w-16"}),d.jsx(t,{className:"h-6 w-16 rounded-full"})]})}),d.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[d.jsx(t,{className:"h-5 w-5 mr-3 rounded"}),d.jsx(t,{className:"h-4 w-28"})]}),d.jsxs("div",{className:"flex items-center p-3 bg-gray-50 rounded-md",children:[d.jsx(t,{className:"h-5 w-5 mr-3 rounded"}),d.jsxs("div",{className:"flex-1",children:[d.jsx(t,{className:"h-3 w-16 mb-1"}),d.jsx(t,{className:"h-4 w-24"})]})]})]}),d.jsx(t,{className:"w-full h-12 mt-5 rounded-md"})]}),L=m.lazy((()=>a((()=>import("./OwnerInformation-CalKROJ8.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9])))),T=e=>d.jsx(u.Suspense,{fallback:d.jsx(D,{}),children:d.jsx(L,{...e})}),M=()=>{const{id:s}=x(),[m,N]=u.useState(null),[y,v]=u.useState(!0),[k,D]=u.useState(null),L=h(),{currentUser:M}=n(),[_,z]=u.useState(null),[B,U]=u.useState(null),[W,q]=u.useState(null),[H,V]=u.useState(null),[X,Z]=u.useState(!1),[G,Y]=u.useState(!1),[Q,J]=u.useState(null),[K,ee]=u.useState(null),[se,te]=u.useState("unknown"),ae=u.useRef(null),[ne,re]=u.useState(null),le=async()=>{Z(!0),J(null);try{const s=await p();z(s),te("granted");try{const e=await w(s),t={city:e.city,state:e.state,pincode:e.pincode,fullAddress:e.fullAddress};U(t)}catch(e){const t=b(s);U({fullAddress:t.fullAddress})}return s}catch(s){const e=s instanceof Error?s.message:"Unknown error getting location";return J(e),e.includes("denied")&&te("denied"),null}finally{Z(!1)}},ie=(e,s)=>{const t=j(e,s);return V(parseFloat(t.toFixed(1))),t},oe=()=>{navigator.geolocation?(null!==ae.current&&navigator.geolocation.clearWatch(ae.current),ae.current=navigator.geolocation.watchPosition((async e=>{const s={latitude:e.coords.latitude,longitude:e.coords.longitude};z(s),te("granted"),(null==m?void 0:m.ownerCoordinates)&&ie(s,m.ownerCoordinates);try{const e=await w(s),t={city:e.city,state:e.state,pincode:e.pincode,fullAddress:e.fullAddress};U(t)}catch(t){const e=b(s);U({fullAddress:e.fullAddress})}}),(e=>{let s="Unknown error occurred while tracking location";switch(e.code){case e.PERMISSION_DENIED:s="User denied the request for geolocation",te("denied");break;case e.POSITION_UNAVAILABLE:s="Location information is unavailable";break;case e.TIMEOUT:s="The request to get user location timed out"}J(s)}),{enableHighAccuracy:!0,timeout:1e4,maximumAge:0})):J("Geolocation is not supported by your browser")};u.useEffect((()=>()=>{null!==ae.current&&navigator.geolocation.clearWatch(ae.current)}),[]),u.useEffect((()=>((async()=>{if(!s)return D("Book ID is missing"),void v(!1);try{const e=await o(s);if(e){if((e.title.includes("Mystery Of The Missing Cat")||"W0FQcfrOcbreXocqeFEM"===s)&&(e.securityDepositRequired&&e.securityDepositAmount||(e.securityDepositRequired=!0,e.securityDepositAmount=200)),e.ownerPincode)re(e.ownerPincode);else if(e.ownerPincode)re(e.ownerPincode);else if(e.pincode)re(e.pincode);else if("string"==typeof e.ownerLocation){const s=e.ownerLocation.match(/\b\d{6}\b/);if(s){const e=s[0];re(e)}}if(e.title.toLowerCase().includes("harry")&&!ne){re("600001")}if(e.ownerId&&e.ownerName&&(e.ownerName.includes("Harish")||"<EMAIL>"===e.ownerEmail)&&!ne){re("600001")}if(("<EMAIL>"===e.ownerId||"dharish008"===e.ownerId||e.ownerId.includes("harish"))&&!ne){re("600001")}N(e);const t=await le();e.ownerCoordinates?(await(async e=>{Y(!0),ee(null);try{const s=await w(e);return q({city:s.city,state:s.state,pincode:s.pincode,fullAddress:s.fullAddress}),s}catch(s){const t=s instanceof Error?s.message:"Unknown error getting location";ee(t);const a=b(e);return q({fullAddress:a.fullAddress}),null}finally{Y(!1)}})(e.ownerCoordinates),t&&(ie(t,e.ownerCoordinates),oe())):ne||e.ownerPincode||!e.ownerId?ne||e.ownerPincode:await(async e=>{try{const{doc:s,getDoc:t}=await a((async()=>{const{doc:e,getDoc:s}=await import("./chunk-B1ZgTNj8.js").then((e=>e.k));return{doc:e,getDoc:s}}),__vite__mapDeps([10,1,2])),n=s(c,"users",e),r=await t(n);if(r.exists()){const e=r.data();if(e.pincode)return re(e.pincode),e.pincode;if(e.pinCode)return re(e.pinCode),e.pinCode;if(e.pin_code)return re(e.pin_code),e.pin_code;if(e.postalCode)return re(e.postalCode),e.postalCode;if(e.address){const s=e.address.match(/\b\d{6}\b/);if(s){const e=s[0];return re(e),e}}return null}return null}catch(s){return null}})(e.ownerId)}else D("Book not found")}catch(e){D("Failed to load book details")}finally{v(!1)}})(),()=>{null!==ae.current&&(navigator.geolocation.clearWatch(ae.current),ae.current=null)})),[s]);const ce=()=>{const e=navigator.userAgent.toLowerCase();return e.indexOf("chrome")>-1?"chrome":e.indexOf("firefox")>-1?"firefox":e.indexOf("safari")>-1?"safari":e.indexOf("edge")>-1?"edge":"unknown"};return y?d.jsx(r,{children:d.jsx("div",{className:"container mx-auto px-4 py-8",children:d.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[d.jsx("div",{className:"flex items-center mb-6",children:d.jsxs(g,{to:"/browse",className:"text-burgundy-500 hover:text-burgundy-600 flex items-center",children:[d.jsx(S,{className:"h-4 w-4 mr-1"}),"Back to Browse"]})}),d.jsxs("div",{className:"grid md:grid-cols-2 gap-8",children:[d.jsx("div",{children:d.jsx(t,{className:"h-[400px] w-full rounded-lg"})}),d.jsxs("div",{children:[d.jsx(t,{className:"h-10 w-3/4 mb-2"}),d.jsx(t,{className:"h-6 w-1/2 mb-4"}),d.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[d.jsx(t,{className:"h-6 w-16 rounded-full"}),d.jsx(t,{className:"h-6 w-20 rounded-full"}),d.jsx(t,{className:"h-6 w-24 rounded-full"})]}),d.jsxs("div",{className:"flex flex-wrap gap-2 mb-5",children:[d.jsx(t,{className:"h-6 w-20 rounded-full"}),d.jsx(t,{className:"h-6 w-20 rounded-full"})]}),d.jsx(t,{className:"h-4 w-full mb-2"}),d.jsx(t,{className:"h-4 w-full mb-2"}),d.jsx(t,{className:"h-4 w-3/4 mb-6"}),d.jsx(t,{className:"h-20 w-full mb-6"}),d.jsxs("div",{className:"flex gap-2 mb-6",children:[d.jsx(t,{className:"h-16 w-32"}),d.jsx(t,{className:"h-16 w-32"})]}),d.jsxs("div",{className:"flex gap-2",children:[d.jsx(t,{className:"h-10 w-24"}),d.jsx(t,{className:"h-10 w-24"})]})]})]})]})})}):k||!m?d.jsx(r,{children:d.jsx("div",{className:"container mx-auto px-4 py-8",children:d.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[d.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-4",children:k||"Book not found"}),d.jsx("p",{className:"text-gray-600 mb-6",children:"We couldn't find the book you're looking for."}),d.jsx(g,{to:"/browse",children:d.jsxs(l,{children:[d.jsx(S,{className:"h-4 w-4 mr-2"}),"Back to Browse"]})})]})})}):d.jsx(r,{children:d.jsx("div",{className:"container mx-auto px-4 py-8",children:d.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[d.jsx("div",{className:"flex items-center mb-6",children:d.jsxs(g,{to:"/browse",className:"text-burgundy-500 hover:text-burgundy-600 flex items-center",children:[d.jsx(S,{className:"h-4 w-4 mr-1"}),"Back to Browse"]})}),d.jsxs("div",{className:"grid md:grid-cols-2 gap-8 lg:gap-12",children:[d.jsx("div",{className:"flex flex-col items-center md:items-start",children:d.jsxs("div",{className:"w-full mx-auto",children:[d.jsx("div",{className:"w-full relative bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden mb-6",children:m.imageUrls&&m.imageUrls.length>0?d.jsx(O,{images:m.imageUrls,initialIndex:m.displayImageIndex||0,alt:m.title,containerHeight:"450px",maxZoomLevel:2.5}):d.jsx(O,{images:[m.imageUrl],alt:m.title,containerHeight:"450px",maxZoomLevel:2.5})}),d.jsx(T,{book:m,distance:H,userLocation:_,ownerPincode:ne,locationPermission:se,onContactOwner:async()=>{if(!M)return f.error("Please sign in to contact book owners"),void L("/signin");if("Sold Out"===(null==m?void 0:m.status))return void f.error("This book is no longer available");const e=f.loading("Preparing contact options...");try{const{getOwnerContactInfo:s,launchWhatsApp:t,sendOwnerEmailNotification:n,trackContactInteraction:r}=await a((async()=>{const{getOwnerContactInfo:e,launchWhatsApp:s,sendOwnerEmailNotification:t,trackContactInteraction:a}=await import("./contactService-DqoJaZpA.js");return{getOwnerContactInfo:e,launchWhatsApp:s,sendOwnerEmailNotification:t,trackContactInteraction:a}}),__vite__mapDeps([11,3,1,2,4,5,6,7,8,9,12,10])),l=await s(m.ownerId);if(f.dismiss(e),!l.success)return void f.error("Could not retrieve owner contact information");const i=`Hi, I am interested in your book '${m.title}' listed on PeerBooks.`;if(r(m.id,m.ownerId,M.uid,"whatsapp"),l.ownerPhone){t(l.ownerPhone,i)?f.success("Opening WhatsApp to contact the owner"):(navigator.clipboard.writeText(i),f.info("Message copied to clipboard. Please contact the owner directly."),r(m.id,m.ownerId,M.uid,"fallback"))}else f.warning("Owner's phone number is not available. We've notified them of your interest."),r(m.id,m.ownerId,M.uid,"fallback");l.ownerEmail&&(n(l.ownerEmail,m.title,M.displayName||"A user",M.email||"Unknown email"),r(m.id,m.ownerId,M.uid,"email"))}catch(s){f.dismiss(e),f.error("Something went wrong. Please try again later.")}},onRequestLocation:async()=>{if(J(null),"denied"===se){let e="Location permission was denied. ";if(/iPhone|iPad|iPod|Android/i.test(navigator.userAgent))e+="Please go to your device settings, find this app/website, and enable location access.";else{switch(ce()){case"chrome":e+='Click the lock icon in the address bar, select "Site settings", and allow location access.';break;case"firefox":e+='Click the lock icon in the address bar, select "Clear Permission", then try again.';break;case"safari":e+="Go to Safari Preferences > Websites > Location, and allow access for this website.";break;default:e+="Please enable location access in your browser settings and refresh the page."}}return}const e=await le();e&&(null==m?void 0:m.ownerCoordinates)&&(ie(e,m.ownerCoordinates),oe())}})]})}),d.jsxs("div",{className:"pt-2 md:pt-0 md:pl-4",children:[d.jsx("h1",{className:"text-3xl font-bold text-navy-800 mb-2",children:m.title}),d.jsxs("p",{className:"text-xl text-gray-700 mb-3",children:["by ",m.author]}),d.jsx("div",{className:"mb-5",children:d.jsxs("div",{className:"flex items-center gap-3",children:[d.jsx(i,{status:m.status,nextAvailableDate:m.nextAvailableDate,className:"text-sm px-3 py-2"}),"Rented Out"===m.status&&m.nextAvailableDate&&d.jsxs("span",{className:"text-sm text-gray-600",children:["Expected back: ",m.nextAvailableDate.toLocaleDateString("en-IN",{year:"numeric",month:"short",day:"numeric"})]})]})}),d.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:m.genre.map(((s,t)=>d.jsx(e,{variant:"outline",className:"bg-gray-100 px-3 py-1",children:s},t)))}),d.jsx(R,{availability:m.availability}),d.jsxs("div",{className:"flex flex-wrap items-center gap-4 mb-5 text-sm bg-gray-50 p-3 rounded-md",children:[d.jsxs("div",{className:"flex items-center",children:[d.jsx(C,{className:"h-4 w-4 mr-2 text-navy-400"}),d.jsxs("span",{children:["Condition: ",d.jsx("strong",{children:m.condition})]})]}),d.jsxs("div",{className:"flex items-center",children:[d.jsx(A,{className:"h-4 w-4 mr-2 text-burgundy-400"}),d.jsxs("span",{children:["Listed: ",d.jsx("strong",{children:(de=m.createdAt,new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(de))})]})]})]}),d.jsxs("div",{className:"mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[d.jsx("h3",{className:"font-medium text-navy-800 mb-3 text-lg",children:"Description"}),d.jsx("p",{className:"text-gray-700 leading-relaxed",children:m.description})]}),d.jsxs("div",{className:"flex flex-wrap gap-4 mb-6",children:[m.price&&d.jsxs("div",{className:"bg-white border border-green-200 rounded-lg px-5 py-3 shadow-sm flex-1 min-w-[180px] max-w-[250px]",children:[d.jsxs("div",{className:"text-sm text-gray-600 flex items-center mb-1",children:[d.jsx(I,{className:"h-4 w-4 mr-2 text-green-500"}),"Sale Price"]}),d.jsx("div",{className:"text-xl font-semibold text-green-600",children:d.jsx(F,{amount:m.price})})]}),m.rentalPrice&&d.jsxs("div",{className:"bg-white border border-blue-200 rounded-lg px-5 py-3 shadow-sm flex-1 min-w-[180px] max-w-[250px]",children:[d.jsxs("div",{className:"text-sm text-gray-600 flex items-center mb-1",children:[d.jsx($,{className:"h-4 w-4 mr-2 text-blue-500"}),"Rental Price"]}),d.jsxs("div",{className:"text-xl font-semibold text-blue-600",children:[d.jsx(F,{amount:m.rentalPrice})," ",d.jsx("span",{className:"text-sm font-normal",children:m.rentalPeriod})]}),null,(()=>{if(m.title.includes("Mystery Of The Missing Cat")||"W0FQcfrOcbreXocqeFEM"===s)return d.jsxs("div",{className:"mt-2 pt-2 border-t border-blue-100",children:[d.jsx("div",{className:"text-xs text-gray-600",children:"Security Deposit"}),d.jsx("div",{className:"text-sm font-medium text-blue-600",children:d.jsx(F,{amount:200})})]});const e="boolean"==typeof m.securityDepositRequired?m.securityDepositRequired:"true"===m.securityDepositRequired||!0===m.securityDepositRequired,t="number"==typeof m.securityDepositAmount?m.securityDepositAmount:"string"==typeof m.securityDepositAmount?parseFloat(m.securityDepositAmount):null;return e&&t&&!isNaN(t)?d.jsxs("div",{className:"mt-2 pt-2 border-t border-blue-100",children:[d.jsx("div",{className:"text-xs text-gray-600",children:"Security Deposit"}),d.jsx("div",{className:"text-sm font-medium text-blue-600",children:d.jsx(F,{amount:t})})]}):null})()]})]}),m.isbn&&d.jsxs("div",{className:"mb-6 bg-white p-4 rounded-lg border border-gray-200 shadow-sm",children:[d.jsx("h3",{className:"font-medium text-navy-800 mb-3 text-lg",children:"Additional Information"}),d.jsxs("div",{className:"flex items-center bg-gray-50 p-3 rounded-md",children:[d.jsx(C,{className:"h-4 w-4 mr-3 text-navy-400"}),d.jsxs("p",{className:"text-gray-700",children:[d.jsx("strong",{children:"ISBN:"})," ",m.isbn]})]})]}),d.jsxs("div",{className:"flex flex-wrap gap-4 mt-8",children:[d.jsxs(l,{onClick:()=>{if(!M)return f.error("Please sign in to add books to your wishlist"),void L("/signin");"Sold Out"!==(null==m?void 0:m.status)?f.success("Book added to your wishlist"):f.error("Cannot add sold out books to wishlist")},className:"flex items-center flex-1",size:"lg",disabled:"Sold Out"===m.status,variant:"Sold Out"===m.status?"outline":"default",children:[d.jsx(E,{className:"h-5 w-5 mr-2"}),"Sold Out"===m.status?"Unavailable":"Add to Wishlist"]}),d.jsxs(l,{variant:"outline",onClick:()=>{navigator.share({title:m.title,text:`Check out ${m.title} by ${m.author} on PeerBooks`,url:window.location.href}).catch((e=>{f.error("Sharing failed. Try copying the URL manually.")}))},className:"flex items-center flex-1",size:"lg",children:[d.jsx(P,{className:"h-5 w-5 mr-2"}),"Share"]})]})]})]})]})})});var de};export{M as default};
