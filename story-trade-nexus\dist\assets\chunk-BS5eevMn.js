import{j as s}from"./chunk-CpdlqjqK.js";import{c as e,r as a,L as i}from"./chunk-b0HHmiEU.js";import{u as n,H as l,c as t,x as r,r as o}from"./index-DwRq5Uwb.js";import{X as d,G as c,e as m,H as x,I as h,r as p,l as u,y as j,d as g,J as b}from"./chunk-BGoCADfv.js";const f=({children:f,title:w="Admin Dashboard",description:N="Manage your book-sharing platform from here."})=>{var k;const{currentUser:v,signOut:y}=n(),A=e(),[C,M]=a.useState(!1),S=[{title:"Dashboard",icon:s.jsx(x,{className:"h-5 w-5"}),link:"/admin",description:"Admin dashboard overview"},{title:"Book Approvals",icon:s.jsx(h,{className:"h-5 w-5"}),link:"/admin/books",description:"Review and approve new book submissions"},{title:"User Management",icon:s.jsx(p,{className:"h-5 w-5"}),link:"/admin/users",description:"Manage users and permissions"},{title:"Contact Messages",icon:s.jsx(u,{className:"h-5 w-5"}),link:"/admin/messages",description:"View and manage contact messages from users"},{title:"Feedback",icon:s.jsx(j,{className:"h-5 w-5"}),link:"/admin/feedback",description:"View and manage user feedback and support requests"},{title:"Admin Tools",icon:s.jsx(g,{className:"h-5 w-5"}),link:"/admin/utilities",description:"Administrative utilities and functions"},{title:"Admin Settings",icon:s.jsx(b,{className:"h-5 w-5"}),link:"/admin/settings",description:"Configure admin preferences and system settings"}];return s.jsxs("div",{className:"min-h-screen flex flex-col",children:[s.jsx(l,{}),s.jsxs("main",{className:"flex-grow flex flex-col md:flex-row",children:[s.jsx("div",{className:"md:hidden p-4 bg-white border-b",children:s.jsxs(t,{variant:"outline",size:"icon",onClick:()=>M(!C),className:"ml-auto flex",children:[C?s.jsx(d,{className:"h-5 w-5"}):s.jsx(c,{className:"h-5 w-5"}),s.jsx("span",{className:"sr-only",children:"Toggle menu"})]})}),s.jsxs("aside",{className:r("w-full md:w-64 bg-white shadow-md md:shadow-none transition-all duration-300 ease-in-out","md:block",C?"block":"hidden"),children:[s.jsxs("div",{className:"p-6 border-b",children:[s.jsx("h2",{className:"text-xl font-bold text-navy-800 mb-2",children:"Admin Panel"}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Welcome, ",(null==v?void 0:v.displayName)||(null==(k=null==v?void 0:v.email)?void 0:k.split("@")[0])||"Admin"]})]}),s.jsxs("nav",{className:"p-4 space-y-1",children:[S.map((e=>{return s.jsxs(i,{to:e.link,className:r("flex items-center px-4 py-3 rounded-md transition-colors",(a=e.link,A.pathname===a?"bg-burgundy-50 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100")),title:e.description,children:[s.jsx("span",{className:"mr-3",children:e.icon}),s.jsx("span",{children:e.title})]},e.title);var a})),s.jsxs("button",{onClick:async()=>{try{await y()}catch(s){}},className:"w-full flex items-center px-4 py-3 rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[s.jsx(m,{className:"h-5 w-5 mr-3"}),s.jsx("span",{children:"Sign Out"})]})]})]}),s.jsx("div",{className:"flex-1 p-4 md:p-8 bg-gray-50",children:s.jsx("div",{className:"max-w-5xl mx-auto",children:f})})]}),s.jsx(o,{})]})};export{f as A};
