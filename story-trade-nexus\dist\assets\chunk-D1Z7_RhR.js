var t,e,n,i,r,a,s,o,u,c,l,h,d,f,m,p,y,g,v,w,b,M,P,k,S,O,x,q,D,F,W,C,T,E,A,Q,j,H,R,N=t=>{throw TypeError(t)},K=(t,e,n)=>e.has(t)||N("Cannot "+n),L=(t,e,n)=>(K(t,e,"read from private field"),n?n.call(t):e.get(t)),Y=(t,e,n)=>e.has(t)?N("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),G=(t,e,n,i)=>(K(t,e,"write to private field"),i?i.call(t,n):e.set(t,n),n),U=(t,e,n)=>(K(t,e,"access private method"),n),z=(t,e,n,i)=>({set _(i){G(t,e,i,n)},get _(){return L(t,e,i)}});import{r as I}from"./chunk-b0HHmiEU.js";import{j as B}from"./chunk-CpdlqjqK.js";var X=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},$="undefined"==typeof window||"Deno"in globalThis;function J(){}function _(t,e){return"function"==typeof t?t(e):t}function V(t,e){const{type:n="all",exact:i,fetchStatus:r,predicate:a,queryKey:s,stale:o}=t;if(s)if(i){if(e.queryHash!==tt(s,e.options))return!1}else if(!nt(e.queryKey,s))return!1;if("all"!==n){const t=e.isActive();if("active"===n&&!t)return!1;if("inactive"===n&&t)return!1}return("boolean"!=typeof o||e.isStale()===o)&&((!r||r===e.state.fetchStatus)&&!(a&&!a(e)))}function Z(t,e){const{exact:n,status:i,predicate:r,mutationKey:a}=t;if(a){if(!e.options.mutationKey)return!1;if(n){if(et(e.options.mutationKey)!==et(a))return!1}else if(!nt(e.options.mutationKey,a))return!1}return(!i||e.state.status===i)&&!(r&&!r(e))}function tt(t,e){return((null==e?void 0:e.queryKeyHashFn)||et)(t)}function et(t){return JSON.stringify(t,((t,e)=>at(e)?Object.keys(e).sort().reduce(((t,n)=>(t[n]=e[n],t)),{}):e))}function nt(t,e){return t===e||typeof t==typeof e&&(!(!t||!e||"object"!=typeof t||"object"!=typeof e)&&!Object.keys(e).some((n=>!nt(t[n],e[n]))))}function it(t,e){if(t===e)return t;const n=rt(t)&&rt(e);if(n||at(t)&&at(e)){const i=n?t:Object.keys(t),r=i.length,a=n?e:Object.keys(e),s=a.length,o=n?[]:{};let u=0;for(let c=0;c<s;c++){const r=n?c:a[c];(!n&&i.includes(r)||n)&&void 0===t[r]&&void 0===e[r]?(o[r]=void 0,u++):(o[r]=it(t[r],e[r]),o[r]===t[r]&&void 0!==t[r]&&u++)}return r===s&&u===r?t:o}return e}function rt(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function at(t){if(!st(t))return!1;const e=t.constructor;if(void 0===e)return!0;const n=e.prototype;return!!st(n)&&(!!n.hasOwnProperty("isPrototypeOf")&&Object.getPrototypeOf(t)===Object.prototype)}function st(t){return"[object Object]"===Object.prototype.toString.call(t)}function ot(t,e,n){return"function"==typeof n.structuralSharing?n.structuralSharing(t,e):!1!==n.structuralSharing?it(t,e):e}function ut(t,e,n=0){const i=[...t,e];return n&&i.length>n?i.slice(1):i}function ct(t,e,n=0){const i=[e,...t];return n&&i.length>n?i.slice(0,-1):i}var lt=Symbol();function ht(t,e){return!t.queryFn&&(null==e?void 0:e.initialPromise)?()=>e.initialPromise:t.queryFn&&t.queryFn!==lt?t.queryFn:()=>Promise.reject(new Error(`Missing queryFn: '${t.queryHash}'`))}var dt=new(i=class extends X{constructor(){super(),Y(this,t),Y(this,e),Y(this,n),G(this,n,(t=>{if(!$&&window.addEventListener){const e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}))}onSubscribe(){L(this,e)||this.setEventListener(L(this,n))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=L(this,e))||t.call(this),G(this,e,void 0))}setEventListener(t){var i;G(this,n,t),null==(i=L(this,e))||i.call(this),G(this,e,t((t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})))}setFocused(e){L(this,t)!==e&&(G(this,t,e),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach((e=>{e(t)}))}isFocused(){var e;return"boolean"==typeof L(this,t)?L(this,t):"hidden"!==(null==(e=globalThis.document)?void 0:e.visibilityState)}},t=new WeakMap,e=new WeakMap,n=new WeakMap,i),ft=new(o=class extends X{constructor(){super(),Y(this,r,!0),Y(this,a),Y(this,s),G(this,s,(t=>{if(!$&&window.addEventListener){const e=()=>t(!0),n=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",n)}}}))}onSubscribe(){L(this,a)||this.setEventListener(L(this,s))}onUnsubscribe(){var t;this.hasListeners()||(null==(t=L(this,a))||t.call(this),G(this,a,void 0))}setEventListener(t){var e;G(this,s,t),null==(e=L(this,a))||e.call(this),G(this,a,t(this.setOnline.bind(this)))}setOnline(t){L(this,r)!==t&&(G(this,r,t),this.listeners.forEach((e=>{e(t)})))}isOnline(){return L(this,r)}},r=new WeakMap,a=new WeakMap,s=new WeakMap,o);function mt(t){return Math.min(1e3*2**t,3e4)}function pt(t){return"online"!==(t??"online")||ft.isOnline()}var yt=class extends Error{constructor(t){super("CancelledError"),this.revert=null==t?void 0:t.revert,this.silent=null==t?void 0:t.silent}};function gt(t){return t instanceof yt}function vt(t){let e,n=!1,i=0,r=!1;const a=function(){let t,e;const n=new Promise(((n,i)=>{t=n,e=i}));function i(t){Object.assign(n,t),delete n.resolve,delete n.reject}return n.status="pending",n.catch((()=>{})),n.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},n.reject=t=>{i({status:"rejected",reason:t}),e(t)},n}(),s=()=>dt.isFocused()&&("always"===t.networkMode||ft.isOnline())&&t.canRun(),o=()=>pt(t.networkMode)&&t.canRun(),u=n=>{var i;r||(r=!0,null==(i=t.onSuccess)||i.call(t,n),null==e||e(),a.resolve(n))},c=n=>{var i;r||(r=!0,null==(i=t.onError)||i.call(t,n),null==e||e(),a.reject(n))},l=()=>new Promise((n=>{var i;e=t=>{(r||s())&&n(t)},null==(i=t.onPause)||i.call(t)})).then((()=>{var n;e=void 0,r||null==(n=t.onContinue)||n.call(t)})),h=()=>{if(r)return;let e;const a=0===i?t.initialPromise:void 0;try{e=a??t.fn()}catch(o){e=Promise.reject(o)}Promise.resolve(e).then(u).catch((e=>{var a;if(r)return;const o=t.retry??($?0:3),u=t.retryDelay??mt,d="function"==typeof u?u(i,e):u,f=!0===o||"number"==typeof o&&i<o||"function"==typeof o&&o(i,e);var m;!n&&f?(i++,null==(a=t.onFail)||a.call(t,i,e),(m=d,new Promise((t=>{setTimeout(t,m)}))).then((()=>s()?void 0:l())).then((()=>{n?c(e):h()}))):c(e)}))};return{promise:a,cancel:e=>{var n;r||(c(new yt(e)),null==(n=t.abort)||n.call(t))},continue:()=>(null==e||e(),a),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:o,start:()=>(o()?h():l().then(h),a)}}var wt=function(){let t=[],e=0,n=t=>{t()},i=t=>{t()},r=t=>setTimeout(t,0);const a=i=>{e?t.push(i):r((()=>{n(i)}))};return{batch:a=>{let s;e++;try{s=a()}finally{e--,e||(()=>{const e=t;t=[],e.length&&r((()=>{i((()=>{e.forEach((t=>{n(t)}))}))}))})()}return s},batchCalls:t=>(...e)=>{a((()=>{t(...e)}))},schedule:a,setNotifyFunction:t=>{n=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{r=t}}}(),bt=(c=class{constructor(){Y(this,u)}destroy(){this.clearGcTimeout()}scheduleGc(){var t;this.clearGcTimeout(),"number"==typeof(t=this.gcTime)&&t>=0&&t!==1/0&&G(this,u,setTimeout((()=>{this.optionalRemove()}),this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??($?1/0:3e5))}clearGcTimeout(){L(this,u)&&(clearTimeout(L(this,u)),G(this,u,void 0))}},u=new WeakMap,c),Mt=(v=class extends bt{constructor(t){super(),Y(this,y),Y(this,l),Y(this,h),Y(this,d),Y(this,f),Y(this,m),Y(this,p),G(this,p,!1),G(this,m,t.defaultOptions),this.setOptions(t.options),this.observers=[],G(this,d,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,G(this,l,function(t){const e="function"==typeof t.initialData?t.initialData():t.initialData,n=void 0!==e,i=n?"function"==typeof t.initialDataUpdatedAt?t.initialDataUpdatedAt():t.initialDataUpdatedAt:0;return{data:e,dataUpdateCount:0,dataUpdatedAt:n?i??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}(this.options)),this.state=t.state??L(this,l),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return null==(t=L(this,f))?void 0:t.promise}setOptions(t){this.options={...L(this,m),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||L(this,d).remove(this)}setData(t,e){const n=ot(this.state.data,t,this.options);return U(this,y,g).call(this,{data:n,type:"success",dataUpdatedAt:null==e?void 0:e.updatedAt,manual:null==e?void 0:e.manual}),n}setState(t,e){U(this,y,g).call(this,{type:"setState",state:t,setStateOptions:e})}cancel(t){var e,n;const i=null==(e=L(this,f))?void 0:e.promise;return null==(n=L(this,f))||n.cancel(t),i?i.then(J).catch(J):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(L(this,l))}isActive(){return this.observers.some((t=>{return!1!==(e=t.options.enabled,n=this,"function"==typeof e?e(n):e);var e,n}))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===lt||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some((t=>t.getCurrentResult().isStale)):void 0===this.state.data)}isStaleByTime(t=0){return this.state.isInvalidated||void 0===this.state.data||!function(t,e){return Math.max(t+(e||0)-Date.now(),0)}(this.state.dataUpdatedAt,t)}onFocus(){var t;const e=this.observers.find((t=>t.shouldFetchOnWindowFocus()));null==e||e.refetch({cancelRefetch:!1}),null==(t=L(this,f))||t.continue()}onOnline(){var t;const e=this.observers.find((t=>t.shouldFetchOnReconnect()));null==e||e.refetch({cancelRefetch:!1}),null==(t=L(this,f))||t.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),L(this,d).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter((e=>e!==t)),this.observers.length||(L(this,f)&&(L(this,p)?L(this,f).cancel({revert:!0}):L(this,f).cancelRetry()),this.scheduleGc()),L(this,d).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||U(this,y,g).call(this,{type:"invalidate"})}fetch(t,e){var n,i,r;if("idle"!==this.state.fetchStatus)if(void 0!==this.state.data&&(null==e?void 0:e.cancelRefetch))this.cancel({silent:!0});else if(L(this,f))return L(this,f).continueRetry(),L(this,f).promise;if(t&&this.setOptions(t),!this.options.queryFn){const t=this.observers.find((t=>t.options.queryFn));t&&this.setOptions(t.options)}const a=new AbortController,s=t=>{Object.defineProperty(t,"signal",{enumerable:!0,get:()=>(G(this,p,!0),a.signal)})},o={fetchOptions:e,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{const t=ht(this.options,e),n={queryKey:this.queryKey,meta:this.meta};return s(n),G(this,p,!1),this.options.persister?this.options.persister(t,n,this):t(n)}};s(o),null==(n=this.options.behavior)||n.onFetch(o,this),G(this,h,this.state),"idle"!==this.state.fetchStatus&&this.state.fetchMeta===(null==(i=o.fetchOptions)?void 0:i.meta)||U(this,y,g).call(this,{type:"fetch",meta:null==(r=o.fetchOptions)?void 0:r.meta});const u=t=>{var e,n,i,r;gt(t)&&t.silent||U(this,y,g).call(this,{type:"error",error:t}),gt(t)||(null==(n=(e=L(this,d).config).onError)||n.call(e,t,this),null==(r=(i=L(this,d).config).onSettled)||r.call(i,this.state.data,t,this)),this.scheduleGc()};return G(this,f,vt({initialPromise:null==e?void 0:e.initialPromise,fn:o.fetchFn,abort:a.abort.bind(a),onSuccess:t=>{var e,n,i,r;if(void 0!==t){try{this.setData(t)}catch(a){return void u(a)}null==(n=(e=L(this,d).config).onSuccess)||n.call(e,t,this),null==(r=(i=L(this,d).config).onSettled)||r.call(i,t,this.state.error,this),this.scheduleGc()}else u(new Error(`${this.queryHash} data is undefined`))},onError:u,onFail:(t,e)=>{U(this,y,g).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{U(this,y,g).call(this,{type:"pause"})},onContinue:()=>{U(this,y,g).call(this,{type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0})),L(this,f).start()}},l=new WeakMap,h=new WeakMap,d=new WeakMap,f=new WeakMap,m=new WeakMap,p=new WeakMap,y=new WeakSet,g=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...e,fetchStatus:"paused"};case"continue":return{...e,fetchStatus:"fetching"};case"fetch":return{...e,...(n=e.data,i=this.options,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:pt(i.networkMode)?"fetching":"paused",...void 0===n&&{error:null,status:"pending"}}),fetchMeta:t.meta??null};case"success":return{...e,data:t.data,dataUpdateCount:e.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const r=t.error;return gt(r)&&r.revert&&L(this,h)?{...L(this,h),fetchStatus:"idle"}:{...e,error:r,errorUpdateCount:e.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:e.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...e,isInvalidated:!0};case"setState":return{...e,...t.state}}var n,i})(this.state),wt.batch((()=>{this.observers.forEach((t=>{t.onQueryUpdate()})),L(this,d).notify({query:this,type:"updated",action:t})}))},v);var Pt=(b=class extends X{constructor(t={}){super(),Y(this,w),this.config=t,G(this,w,new Map)}build(t,e,n){const i=e.queryKey,r=e.queryHash??tt(i,e);let a=this.get(r);return a||(a=new Mt({cache:this,queryKey:i,queryHash:r,options:t.defaultQueryOptions(e),state:n,defaultOptions:t.getQueryDefaults(i)}),this.add(a)),a}add(t){L(this,w).has(t.queryHash)||(L(this,w).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const e=L(this,w).get(t.queryHash);e&&(t.destroy(),e===t&&L(this,w).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){wt.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}get(t){return L(this,w).get(t)}getAll(){return[...L(this,w).values()]}find(t){const e={exact:!0,...t};return this.getAll().find((t=>V(e,t)))}findAll(t={}){const e=this.getAll();return Object.keys(t).length>0?e.filter((e=>V(t,e))):e}notify(t){wt.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}onFocus(){wt.batch((()=>{this.getAll().forEach((t=>{t.onFocus()}))}))}onOnline(){wt.batch((()=>{this.getAll().forEach((t=>{t.onOnline()}))}))}},w=new WeakMap,b),kt=(x=class extends bt{constructor(t){super(),Y(this,S),Y(this,M),Y(this,P),Y(this,k),this.mutationId=t.mutationId,G(this,P,t.mutationCache),G(this,M,[]),this.state=t.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){L(this,M).includes(t)||(L(this,M).push(t),this.clearGcTimeout(),L(this,P).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){G(this,M,L(this,M).filter((e=>e!==t))),this.scheduleGc(),L(this,P).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){L(this,M).length||("pending"===this.state.status?this.scheduleGc():L(this,P).remove(this))}continue(){var t;return(null==(t=L(this,k))?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var e,n,i,r,a,s,o,u,c,l,h,d,f,m,p,y,g,v,w,b;G(this,k,vt({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(t,e)=>{U(this,S,O).call(this,{type:"failed",failureCount:t,error:e})},onPause:()=>{U(this,S,O).call(this,{type:"pause"})},onContinue:()=>{U(this,S,O).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>L(this,P).canRun(this)}));const M="pending"===this.state.status,x=!L(this,k).canStart();try{if(!M){U(this,S,O).call(this,{type:"pending",variables:t,isPaused:x}),await(null==(n=(e=L(this,P).config).onMutate)?void 0:n.call(e,t,this));const a=await(null==(r=(i=this.options).onMutate)?void 0:r.call(i,t));a!==this.state.context&&U(this,S,O).call(this,{type:"pending",context:a,variables:t,isPaused:x})}const f=await L(this,k).start();return await(null==(s=(a=L(this,P).config).onSuccess)?void 0:s.call(a,f,t,this.state.context,this)),await(null==(u=(o=this.options).onSuccess)?void 0:u.call(o,f,t,this.state.context)),await(null==(l=(c=L(this,P).config).onSettled)?void 0:l.call(c,f,null,this.state.variables,this.state.context,this)),await(null==(d=(h=this.options).onSettled)?void 0:d.call(h,f,null,t,this.state.context)),U(this,S,O).call(this,{type:"success",data:f}),f}catch(q){try{throw await(null==(m=(f=L(this,P).config).onError)?void 0:m.call(f,q,t,this.state.context,this)),await(null==(y=(p=this.options).onError)?void 0:y.call(p,q,t,this.state.context)),await(null==(v=(g=L(this,P).config).onSettled)?void 0:v.call(g,void 0,q,this.state.variables,this.state.context,this)),await(null==(b=(w=this.options).onSettled)?void 0:b.call(w,void 0,q,t,this.state.context)),q}finally{U(this,S,O).call(this,{type:"error",error:q})}}finally{L(this,P).runNext(this)}}},M=new WeakMap,P=new WeakMap,k=new WeakMap,S=new WeakSet,O=function(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),wt.batch((()=>{L(this,M).forEach((e=>{e.onMutationUpdate(t)})),L(this,P).notify({mutation:this,type:"updated",action:t})}))},x);var St=(F=class extends X{constructor(t={}){super(),Y(this,q),Y(this,D),this.config=t,G(this,q,new Map),G(this,D,Date.now())}build(t,e,n){const i=new kt({mutationCache:this,mutationId:++z(this,D)._,options:t.defaultMutationOptions(e),state:n});return this.add(i),i}add(t){const e=Ot(t),n=L(this,q).get(e)??[];n.push(t),L(this,q).set(e,n),this.notify({type:"added",mutation:t})}remove(t){var e;const n=Ot(t);if(L(this,q).has(n)){const i=null==(e=L(this,q).get(n))?void 0:e.filter((e=>e!==t));i&&(0===i.length?L(this,q).delete(n):L(this,q).set(n,i))}this.notify({type:"removed",mutation:t})}canRun(t){var e;const n=null==(e=L(this,q).get(Ot(t)))?void 0:e.find((t=>"pending"===t.state.status));return!n||n===t}runNext(t){var e;const n=null==(e=L(this,q).get(Ot(t)))?void 0:e.find((e=>e!==t&&e.state.isPaused));return(null==n?void 0:n.continue())??Promise.resolve()}clear(){wt.batch((()=>{this.getAll().forEach((t=>{this.remove(t)}))}))}getAll(){return[...L(this,q).values()].flat()}find(t){const e={exact:!0,...t};return this.getAll().find((t=>Z(e,t)))}findAll(t={}){return this.getAll().filter((e=>Z(t,e)))}notify(t){wt.batch((()=>{this.listeners.forEach((e=>{e(t)}))}))}resumePausedMutations(){const t=this.getAll().filter((t=>t.state.isPaused));return wt.batch((()=>Promise.all(t.map((t=>t.continue().catch(J))))))}},q=new WeakMap,D=new WeakMap,F);function Ot(t){var e;return(null==(e=t.options.scope)?void 0:e.id)??String(t.mutationId)}function xt(t){return{onFetch:(e,n)=>{var i,r,a,s,o;const u=e.options,c=null==(a=null==(r=null==(i=e.fetchOptions)?void 0:i.meta)?void 0:r.fetchMore)?void 0:a.direction,l=(null==(s=e.state.data)?void 0:s.pages)||[],h=(null==(o=e.state.data)?void 0:o.pageParams)||[];let d={pages:[],pageParams:[]},f=0;const m=async()=>{let n=!1;const i=ht(e.options,e.fetchOptions),r=async(t,r,a)=>{if(n)return Promise.reject();if(null==r&&t.pages.length)return Promise.resolve(t);const s={queryKey:e.queryKey,pageParam:r,direction:a?"backward":"forward",meta:e.options.meta};var o;o=s,Object.defineProperty(o,"signal",{enumerable:!0,get:()=>(e.signal.aborted?n=!0:e.signal.addEventListener("abort",(()=>{n=!0})),e.signal)});const u=await i(s),{maxPages:c}=e.options,l=a?ct:ut;return{pages:l(t.pages,u,c),pageParams:l(t.pageParams,r,c)}};if(c&&l.length){const t="backward"===c,e={pages:l,pageParams:h},n=(t?Dt:qt)(u,e);d=await r(e,n,t)}else{const e=t??l.length;do{const t=0===f?h[0]??u.initialPageParam:qt(u,d);if(f>0&&null==t)break;d=await r(d,t),f++}while(f<e)}return d};e.options.persister?e.fetchFn=()=>{var t,i;return null==(i=(t=e.options).persister)?void 0:i.call(t,m,{queryKey:e.queryKey,meta:e.options.meta,signal:e.signal},n)}:e.fetchFn=m}}}function qt(t,{pages:e,pageParams:n}){const i=e.length-1;return e.length>0?t.getNextPageParam(e[i],e,n[i],n):void 0}function Dt(t,{pages:e,pageParams:n}){var i;return e.length>0?null==(i=t.getPreviousPageParam)?void 0:i.call(t,e[0],e,n[0],n):void 0}var Ft=(R=class{constructor(t={}){Y(this,W),Y(this,C),Y(this,T),Y(this,E),Y(this,A),Y(this,Q),Y(this,j),Y(this,H),G(this,W,t.queryCache||new Pt),G(this,C,t.mutationCache||new St),G(this,T,t.defaultOptions||{}),G(this,E,new Map),G(this,A,new Map),G(this,Q,0)}mount(){z(this,Q)._++,1===L(this,Q)&&(G(this,j,dt.subscribe((async t=>{t&&(await this.resumePausedMutations(),L(this,W).onFocus())}))),G(this,H,ft.subscribe((async t=>{t&&(await this.resumePausedMutations(),L(this,W).onOnline())}))))}unmount(){var t,e;z(this,Q)._--,0===L(this,Q)&&(null==(t=L(this,j))||t.call(this),G(this,j,void 0),null==(e=L(this,H))||e.call(this),G(this,H,void 0))}isFetching(t){return L(this,W).findAll({...t,fetchStatus:"fetching"}).length}isMutating(t){return L(this,C).findAll({...t,status:"pending"}).length}getQueryData(t){var e;const n=this.defaultQueryOptions({queryKey:t});return null==(e=L(this,W).get(n.queryHash))?void 0:e.state.data}ensureQueryData(t){const e=this.getQueryData(t.queryKey);if(void 0===e)return this.fetchQuery(t);{const n=this.defaultQueryOptions(t),i=L(this,W).build(this,n);return t.revalidateIfStale&&i.isStaleByTime(_(n.staleTime,i))&&this.prefetchQuery(n),Promise.resolve(e)}}getQueriesData(t){return L(this,W).findAll(t).map((({queryKey:t,state:e})=>[t,e.data]))}setQueryData(t,e,n){const i=this.defaultQueryOptions({queryKey:t}),r=L(this,W).get(i.queryHash),a=function(t,e){return"function"==typeof t?t(e):t}(e,null==r?void 0:r.state.data);if(void 0!==a)return L(this,W).build(this,i).setData(a,{...n,manual:!0})}setQueriesData(t,e,n){return wt.batch((()=>L(this,W).findAll(t).map((({queryKey:t})=>[t,this.setQueryData(t,e,n)]))))}getQueryState(t){var e;const n=this.defaultQueryOptions({queryKey:t});return null==(e=L(this,W).get(n.queryHash))?void 0:e.state}removeQueries(t){const e=L(this,W);wt.batch((()=>{e.findAll(t).forEach((t=>{e.remove(t)}))}))}resetQueries(t,e){const n=L(this,W),i={type:"active",...t};return wt.batch((()=>(n.findAll(t).forEach((t=>{t.reset()})),this.refetchQueries(i,e))))}cancelQueries(t={},e={}){const n={revert:!0,...e},i=wt.batch((()=>L(this,W).findAll(t).map((t=>t.cancel(n)))));return Promise.all(i).then(J).catch(J)}invalidateQueries(t={},e={}){return wt.batch((()=>{if(L(this,W).findAll(t).forEach((t=>{t.invalidate()})),"none"===t.refetchType)return Promise.resolve();const n={...t,type:t.refetchType??t.type??"active"};return this.refetchQueries(n,e)}))}refetchQueries(t={},e){const n={...e,cancelRefetch:(null==e?void 0:e.cancelRefetch)??!0},i=wt.batch((()=>L(this,W).findAll(t).filter((t=>!t.isDisabled())).map((t=>{let e=t.fetch(void 0,n);return n.throwOnError||(e=e.catch(J)),"paused"===t.state.fetchStatus?Promise.resolve():e}))));return Promise.all(i).then(J)}fetchQuery(t){const e=this.defaultQueryOptions(t);void 0===e.retry&&(e.retry=!1);const n=L(this,W).build(this,e);return n.isStaleByTime(_(e.staleTime,n))?n.fetch(e):Promise.resolve(n.state.data)}prefetchQuery(t){return this.fetchQuery(t).then(J).catch(J)}fetchInfiniteQuery(t){return t.behavior=xt(t.pages),this.fetchQuery(t)}prefetchInfiniteQuery(t){return this.fetchInfiniteQuery(t).then(J).catch(J)}ensureInfiniteQueryData(t){return t.behavior=xt(t.pages),this.ensureQueryData(t)}resumePausedMutations(){return ft.isOnline()?L(this,C).resumePausedMutations():Promise.resolve()}getQueryCache(){return L(this,W)}getMutationCache(){return L(this,C)}getDefaultOptions(){return L(this,T)}setDefaultOptions(t){G(this,T,t)}setQueryDefaults(t,e){L(this,E).set(et(t),{queryKey:t,defaultOptions:e})}getQueryDefaults(t){const e=[...L(this,E).values()];let n={};return e.forEach((e=>{nt(t,e.queryKey)&&(n={...n,...e.defaultOptions})})),n}setMutationDefaults(t,e){L(this,A).set(et(t),{mutationKey:t,defaultOptions:e})}getMutationDefaults(t){const e=[...L(this,A).values()];let n={};return e.forEach((e=>{nt(t,e.mutationKey)&&(n={...n,...e.defaultOptions})})),n}defaultQueryOptions(t){if(t._defaulted)return t;const e={...L(this,T).queries,...this.getQueryDefaults(t.queryKey),...t,_defaulted:!0};return e.queryHash||(e.queryHash=tt(e.queryKey,e)),void 0===e.refetchOnReconnect&&(e.refetchOnReconnect="always"!==e.networkMode),void 0===e.throwOnError&&(e.throwOnError=!!e.suspense),!e.networkMode&&e.persister&&(e.networkMode="offlineFirst"),!0!==e.enabled&&e.queryFn===lt&&(e.enabled=!1),e}defaultMutationOptions(t){return(null==t?void 0:t._defaulted)?t:{...L(this,T).mutations,...(null==t?void 0:t.mutationKey)&&this.getMutationDefaults(t.mutationKey),...t,_defaulted:!0}}clear(){L(this,W).clear(),L(this,C).clear()}},W=new WeakMap,C=new WeakMap,T=new WeakMap,E=new WeakMap,A=new WeakMap,Q=new WeakMap,j=new WeakMap,H=new WeakMap,R),Wt=I.createContext(void 0),Ct=({client:t,children:e})=>(I.useEffect((()=>(t.mount(),()=>{t.unmount()})),[t]),B.jsx(Wt.Provider,{value:t,children:e}));const Tt={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function Et(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const At={date:Et({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:Et({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:Et({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Qt={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function jt(t){return(e,n)=>{let i;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&t.formattingValues){const e=t.defaultFormattingWidth||t.defaultWidth,r=(null==n?void 0:n.width)?String(n.width):e;i=t.formattingValues[r]||t.formattingValues[e]}else{const e=t.defaultWidth,r=(null==n?void 0:n.width)?String(n.width):t.defaultWidth;i=t.values[r]||t.values[e]}return i[t.argumentCallback?t.argumentCallback(e):e]}}function Ht(t){return(e,n={})=>{const i=n.width,r=i&&t.matchPatterns[i]||t.matchPatterns[t.defaultMatchWidth],a=e.match(r);if(!a)return null;const s=a[0],o=i&&t.parsePatterns[i]||t.parsePatterns[t.defaultParseWidth],u=Array.isArray(o)?function(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n;return}(o,(t=>t.test(s))):function(t,e){for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n;return}(o,(t=>t.test(s)));let c;c=t.valueCallback?t.valueCallback(u):u,c=n.valueCallback?n.valueCallback(c):c;return{value:c,rest:e.slice(s.length)}}}var Rt;const Nt={code:"en-US",formatDistance:(t,e,n)=>{let i;const r=Tt[t];return i="string"==typeof r?r:1===e?r.one:r.other.replace("{{count}}",e.toString()),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+i:i+" ago":i},formatLong:At,formatRelative:(t,e,n,i)=>Qt[t],localize:{ordinalNumber:(t,e)=>{const n=Number(t),i=n%100;if(i>20||i<10)switch(i%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:jt({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:jt({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:jt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:jt({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:jt({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(Rt={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)},(t,e={})=>{const n=t.match(Rt.matchPattern);if(!n)return null;const i=n[0],r=t.match(Rt.parsePattern);if(!r)return null;let a=Rt.valueCallback?Rt.valueCallback(r[0]):r[0];return a=e.valueCallback?e.valueCallback(a):a,{value:a,rest:t.slice(i.length)}}),era:Ht({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:Ht({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:Ht({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Ht({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Ht({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};let Kt={};function Lt(){return Kt}const Yt=6048e5;function Gt(t){const e=Object.prototype.toString.call(t);return t instanceof Date||"object"==typeof t&&"[object Date]"===e?new t.constructor(+t):"number"==typeof t||"[object Number]"===e||"string"==typeof t||"[object String]"===e?new Date(t):new Date(NaN)}function Ut(t){const e=Gt(t);return e.setHours(0,0,0,0),e}function zt(t){const e=Gt(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function It(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}function Bt(t){const e=Gt(t),n=function(t,e){const n=Ut(t),i=Ut(e),r=+n-zt(n),a=+i-zt(i);return Math.round((r-a)/864e5)}(e,function(t){const e=Gt(t),n=It(t,0);return n.setFullYear(e.getFullYear(),0,1),n.setHours(0,0,0,0),n}(e));return n+1}function Xt(t,e){var n,i,r,a;const s=Lt(),o=(null==e?void 0:e.weekStartsOn)??(null==(i=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:i.weekStartsOn)??s.weekStartsOn??(null==(a=null==(r=s.locale)?void 0:r.options)?void 0:a.weekStartsOn)??0,u=Gt(t),c=u.getDay(),l=(c<o?7:0)+c-o;return u.setDate(u.getDate()-l),u.setHours(0,0,0,0),u}function $t(t){return Xt(t,{weekStartsOn:1})}function Jt(t){const e=Gt(t),n=e.getFullYear(),i=It(t,0);i.setFullYear(n+1,0,4),i.setHours(0,0,0,0);const r=$t(i),a=It(t,0);a.setFullYear(n,0,4),a.setHours(0,0,0,0);const s=$t(a);return e.getTime()>=r.getTime()?n+1:e.getTime()>=s.getTime()?n:n-1}function _t(t){const e=Gt(t),n=+$t(e)-+function(t){const e=Jt(t),n=It(t,0);return n.setFullYear(e,0,4),n.setHours(0,0,0,0),$t(n)}(e);return Math.round(n/Yt)+1}function Vt(t,e){var n,i,r,a;const s=Gt(t),o=s.getFullYear(),u=Lt(),c=(null==e?void 0:e.firstWeekContainsDate)??(null==(i=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:i.firstWeekContainsDate)??u.firstWeekContainsDate??(null==(a=null==(r=u.locale)?void 0:r.options)?void 0:a.firstWeekContainsDate)??1,l=It(t,0);l.setFullYear(o+1,0,c),l.setHours(0,0,0,0);const h=Xt(l,e),d=It(t,0);d.setFullYear(o,0,c),d.setHours(0,0,0,0);const f=Xt(d,e);return s.getTime()>=h.getTime()?o+1:s.getTime()>=f.getTime()?o:o-1}function Zt(t,e){const n=Gt(t),i=+Xt(n,e)-+function(t,e){var n,i,r,a;const s=Lt(),o=(null==e?void 0:e.firstWeekContainsDate)??(null==(i=null==(n=null==e?void 0:e.locale)?void 0:n.options)?void 0:i.firstWeekContainsDate)??s.firstWeekContainsDate??(null==(a=null==(r=s.locale)?void 0:r.options)?void 0:a.firstWeekContainsDate)??1,u=Vt(t,e),c=It(t,0);return c.setFullYear(u,0,o),c.setHours(0,0,0,0),Xt(c,e)}(n,e);return Math.round(i/Yt)+1}function te(t,e){return(t<0?"-":"")+Math.abs(t).toString().padStart(e,"0")}const ee={y(t,e){const n=t.getFullYear(),i=n>0?n:1-n;return te("yy"===e?i%100:i,e.length)},M(t,e){const n=t.getMonth();return"M"===e?String(n+1):te(n+1,2)},d:(t,e)=>te(t.getDate(),e.length),a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return"am"===n?"a.m.":"p.m."}},h:(t,e)=>te(t.getHours()%12||12,e.length),H:(t,e)=>te(t.getHours(),e.length),m:(t,e)=>te(t.getMinutes(),e.length),s:(t,e)=>te(t.getSeconds(),e.length),S(t,e){const n=e.length,i=t.getMilliseconds();return te(Math.trunc(i*Math.pow(10,n-3)),e.length)}},ne="midnight",ie="noon",re="morning",ae="afternoon",se="evening",oe="night",ue={G:function(t,e,n){const i=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(i,{width:"abbreviated"});case"GGGGG":return n.era(i,{width:"narrow"});default:return n.era(i,{width:"wide"})}},y:function(t,e,n){if("yo"===e){const e=t.getFullYear(),i=e>0?e:1-e;return n.ordinalNumber(i,{unit:"year"})}return ee.y(t,e)},Y:function(t,e,n,i){const r=Vt(t,i),a=r>0?r:1-r;if("YY"===e){return te(a%100,2)}return"Yo"===e?n.ordinalNumber(a,{unit:"year"}):te(a,e.length)},R:function(t,e){return te(Jt(t),e.length)},u:function(t,e){return te(t.getFullYear(),e.length)},Q:function(t,e,n){const i=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(i);case"QQ":return te(i,2);case"Qo":return n.ordinalNumber(i,{unit:"quarter"});case"QQQ":return n.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(i,{width:"narrow",context:"formatting"});default:return n.quarter(i,{width:"wide",context:"formatting"})}},q:function(t,e,n){const i=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(i);case"qq":return te(i,2);case"qo":return n.ordinalNumber(i,{unit:"quarter"});case"qqq":return n.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(i,{width:"narrow",context:"standalone"});default:return n.quarter(i,{width:"wide",context:"standalone"})}},M:function(t,e,n){const i=t.getMonth();switch(e){case"M":case"MM":return ee.M(t,e);case"Mo":return n.ordinalNumber(i+1,{unit:"month"});case"MMM":return n.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(i,{width:"narrow",context:"formatting"});default:return n.month(i,{width:"wide",context:"formatting"})}},L:function(t,e,n){const i=t.getMonth();switch(e){case"L":return String(i+1);case"LL":return te(i+1,2);case"Lo":return n.ordinalNumber(i+1,{unit:"month"});case"LLL":return n.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(i,{width:"narrow",context:"standalone"});default:return n.month(i,{width:"wide",context:"standalone"})}},w:function(t,e,n,i){const r=Zt(t,i);return"wo"===e?n.ordinalNumber(r,{unit:"week"}):te(r,e.length)},I:function(t,e,n){const i=_t(t);return"Io"===e?n.ordinalNumber(i,{unit:"week"}):te(i,e.length)},d:function(t,e,n){return"do"===e?n.ordinalNumber(t.getDate(),{unit:"date"}):ee.d(t,e)},D:function(t,e,n){const i=Bt(t);return"Do"===e?n.ordinalNumber(i,{unit:"dayOfYear"}):te(i,e.length)},E:function(t,e,n){const i=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},e:function(t,e,n,i){const r=t.getDay(),a=(r-i.weekStartsOn+8)%7||7;switch(e){case"e":return String(a);case"ee":return te(a,2);case"eo":return n.ordinalNumber(a,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(t,e,n,i){const r=t.getDay(),a=(r-i.weekStartsOn+8)%7||7;switch(e){case"c":return String(a);case"cc":return te(a,e.length);case"co":return n.ordinalNumber(a,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(t,e,n){const i=t.getDay(),r=0===i?7:i;switch(e){case"i":return String(r);case"ii":return te(r,e.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},a:function(t,e,n){const i=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},b:function(t,e,n){const i=t.getHours();let r;switch(r=12===i?ie:0===i?ne:i/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(t,e,n){const i=t.getHours();let r;switch(r=i>=17?se:i>=12?ae:i>=4?re:oe,e){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(t,e,n){if("ho"===e){let e=t.getHours()%12;return 0===e&&(e=12),n.ordinalNumber(e,{unit:"hour"})}return ee.h(t,e)},H:function(t,e,n){return"Ho"===e?n.ordinalNumber(t.getHours(),{unit:"hour"}):ee.H(t,e)},K:function(t,e,n){const i=t.getHours()%12;return"Ko"===e?n.ordinalNumber(i,{unit:"hour"}):te(i,e.length)},k:function(t,e,n){let i=t.getHours();return 0===i&&(i=24),"ko"===e?n.ordinalNumber(i,{unit:"hour"}):te(i,e.length)},m:function(t,e,n){return"mo"===e?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):ee.m(t,e)},s:function(t,e,n){return"so"===e?n.ordinalNumber(t.getSeconds(),{unit:"second"}):ee.s(t,e)},S:function(t,e){return ee.S(t,e)},X:function(t,e,n){const i=t.getTimezoneOffset();if(0===i)return"Z";switch(e){case"X":return le(i);case"XXXX":case"XX":return he(i);default:return he(i,":")}},x:function(t,e,n){const i=t.getTimezoneOffset();switch(e){case"x":return le(i);case"xxxx":case"xx":return he(i);default:return he(i,":")}},O:function(t,e,n){const i=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+ce(i,":");default:return"GMT"+he(i,":")}},z:function(t,e,n){const i=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+ce(i,":");default:return"GMT"+he(i,":")}},t:function(t,e,n){return te(Math.trunc(t.getTime()/1e3),e.length)},T:function(t,e,n){return te(t.getTime(),e.length)}};function ce(t,e=""){const n=t>0?"-":"+",i=Math.abs(t),r=Math.trunc(i/60),a=i%60;return 0===a?n+String(r):n+String(r)+e+te(a,2)}function le(t,e){if(t%60==0){return(t>0?"-":"+")+te(Math.abs(t)/60,2)}return he(t,e)}function he(t,e=""){const n=t>0?"-":"+",i=Math.abs(t);return n+te(Math.trunc(i/60),2)+e+te(i%60,2)}const de=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},fe=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},me={p:fe,P:(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],i=n[1],r=n[2];if(!r)return de(t,e);let a;switch(i){case"P":a=e.dateTime({width:"short"});break;case"PP":a=e.dateTime({width:"medium"});break;case"PPP":a=e.dateTime({width:"long"});break;default:a=e.dateTime({width:"full"})}return a.replace("{{date}}",de(i,e)).replace("{{time}}",fe(r,e))}},pe=/^D+$/,ye=/^Y+$/,ge=["D","DD","YY","YYYY"];function ve(t){if(!(e=t,e instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e)||"number"==typeof t))return!1;var e;const n=Gt(t);return!isNaN(Number(n))}const we=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,be=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Me=/^'([^]*?)'?$/,Pe=/''/g,ke=/[a-zA-Z]/;function Se(t,e,n){var i,r,a,s;const o=Lt(),u=o.locale??Nt,c=o.firstWeekContainsDate??(null==(r=null==(i=o.locale)?void 0:i.options)?void 0:r.firstWeekContainsDate)??1,l=o.weekStartsOn??(null==(s=null==(a=o.locale)?void 0:a.options)?void 0:s.weekStartsOn)??0,h=Gt(t);if(!ve(h))throw new RangeError("Invalid time value");let d=e.match(be).map((t=>{const e=t[0];if("p"===e||"P"===e){return(0,me[e])(t,u.formatLong)}return t})).join("").match(we).map((t=>{if("''"===t)return{isToken:!1,value:"'"};const e=t[0];if("'"===e)return{isToken:!1,value:Oe(t)};if(ue[e])return{isToken:!0,value:t};if(e.match(ke))throw new RangeError("Format string contains an unescaped latin alphabet character `"+e+"`");return{isToken:!1,value:t}}));u.localize.preprocessor&&(d=u.localize.preprocessor(h,d));const f={firstWeekContainsDate:c,weekStartsOn:l,locale:u};return d.map((n=>{if(!n.isToken)return n.value;const i=n.value;(function(t){return ye.test(t)}(i)||function(t){return pe.test(t)}(i))&&function(t,e,n){const i=function(t,e,n){const i="Y"===t[0]?"years":"days of the month";return`Use \`${t.toLowerCase()}\` instead of \`${t}\` (in \`${e}\`) for formatting ${i} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(t,e,n);if(ge.includes(t))throw new RangeError(i)}(i,e,String(t));return(0,ue[i[0]])(h,i,u.localize,f)})).join("")}function Oe(t){const e=t.match(Me);return e?e[1].replace(Pe,"'"):t}export{Ft as Q,Ct as a,Se as f};
