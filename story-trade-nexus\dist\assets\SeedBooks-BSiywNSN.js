import{r as s,j as e,H as u,h as d,L as h,G as m,O as b,J as n}from"./index-BsSeXuDn.js";const p=()=>{const[a,o]=s.useState(!1),[i,r]=s.useState(!1),[l,t]=s.useState(null),c=async()=>{if(confirm("Are you sure you want to seed the database with real books? This will add duplicate books if they already exist.")){o(!0),t(null),r(!1);try{await b(),r(!0),n.success("Real books added to the database successfully!")}catch(x){console.error("Error seeding books:",x),t("Failed to seed books. See console for details."),n.error("Failed to seed books")}finally{o(!1)}}};return e.jsxs("div",{className:"min-h-screen flex flex-col",children:[e.jsx(u,{}),e.jsx("main",{className:"flex-grow",children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8",children:[e.jsx("h1",{className:"text-3xl font-playfair font-bold text-navy-800 mb-4",children:"Seed Database"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"This utility page allows you to seed the database with real books for testing purposes. Use this only in development environments."}),l&&e.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:l})}),i&&e.jsx("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6",children:e.jsx("p",{children:"Real books added to the database successfully!"})}),e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[e.jsx(d,{onClick:c,disabled:a,className:"w-full sm:w-auto",children:a?"Adding Real Books...":"Add Real Books"}),e.jsx(h,{to:"/browse",children:e.jsx(d,{variant:"outline",className:"w-full sm:w-auto",children:"Browse Books"})})]})]})})}),e.jsx(m,{})]})};export{p as default};
