/**
 * LazyOwnerInformation Component
 * 
 * Lazy-loaded wrapper for the Owner Information section
 * Uses React.lazy() and Suspense for performance optimization
 */

import React, { Suspense } from 'react';
import OwnerInformationSkeleton from './OwnerInformationSkeleton';
import { Book } from '@/types';
import { GeoCoordinates } from '@/lib/geolocationUtils';

// Lazy load the OwnerInformation component
const OwnerInformation = React.lazy(() => import('./OwnerInformation'));

interface LazyOwnerInformationProps {
  book: Book;
  distance: number | null;
  userLocation: GeoCoordinates | null;
  ownerPincode: string | null;
  locationPermission: 'granted' | 'denied' | 'unknown';
  onContactOwner: () => void;
  onRequestLocation: () => void;
}

const LazyOwnerInformation: React.FC<LazyOwnerInformationProps> = (props) => {
  return (
    <Suspense fallback={<OwnerInformationSkeleton />}>
      <OwnerInformation {...props} />
    </Suspense>
  );
};

export default LazyOwnerInformation;
