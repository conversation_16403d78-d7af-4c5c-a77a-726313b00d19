import{f as M,u as S,r as l,j as e,i as n,h as F,k as E,l as H,m as h,am as k,t as T}from"./index-BiRTTmW2.js";import{S as C,g as L,a as O,m as U}from"./StarRating-CcAiefPG.js";import{D as B,a as q,b as z,c as P,d as V}from"./dialog-2rEEUDTj.js";import{C as d,a as i,b as c,c as m}from"./card-CzQaGID-.js";import{A as G}from"./AdminLayout-DbLrXmoL.js";import{C as _}from"./circle-alert-Pl6AF7JV.js";import{E as $}from"./eye-CKny-2Ce.js";import{E as J}from"./eye-off-BudsEXlt.js";import{f as j}from"./format-cwXK75ha.js";import"./users-DHm-dyBk.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=M("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),re=()=>{var v;S();const[o,u]=l.useState([]),[r,p]=l.useState(null),[x,f]=l.useState(!0),[a,A]=l.useState(null),[R,y]=l.useState(!1),b=async()=>{try{f(!0);const[s,t]=await Promise.all([L(),O()]);u(s),p(t)}catch(s){console.error("Error loading feedback:",s),T({title:"Error",description:"Failed to load feedback data. Please try again.",variant:"destructive"})}finally{f(!1)}};l.useEffect(()=>{b()},[]);const D=async s=>{if(A(s),y(!0),!s.isRead&&s.id)try{await U(s.id),u(t=>t.map(g=>g.id===s.id?{...g,isRead:!0,readAt:new Date}:g)),r&&p(t=>t?{...t,unreadCount:t.unreadCount-1}:null)}catch(t){console.error("Error marking feedback as read:",t)}},N=s=>({"Bug Report":"bg-red-100 text-red-800","Feature Request":"bg-blue-100 text-blue-800","General Feedback":"bg-green-100 text-green-800","Technical Support":"bg-yellow-100 text-yellow-800","Account Issues":"bg-purple-100 text-purple-800"})[s]||"bg-gray-100 text-gray-800",w=o.filter(s=>!s.isRead).length;return e.jsxs(G,{title:"Feedback Management",description:"View and manage user feedback submissions",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Feedback Management"}),e.jsxs("p",{className:"text-gray-600",children:["View and respond to user feedback and support requests",w>0&&e.jsxs(n,{variant:"destructive",className:"ml-2",children:[w," unread"]})]})]}),e.jsx(F,{onClick:b,variant:"outline",className:"mt-4 md:mt-0",disabled:x,children:x?e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),r&&e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[e.jsxs(d,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Total Submissions"}),e.jsx(h,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(m,{children:e.jsx("div",{className:"text-2xl font-bold",children:r.totalSubmissions})})]}),e.jsxs(d,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Unread"}),e.jsx(_,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(m,{children:e.jsx("div",{className:"text-2xl font-bold text-red-600",children:r.unreadCount})})]}),e.jsxs(d,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Average Rating"}),e.jsx(k,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsxs(m,{children:[e.jsx("div",{className:"text-2xl font-bold",children:r.averageRating||"N/A"}),r.averageRating>0&&e.jsx(C,{value:r.averageRating,readonly:!0,size:"sm",showText:!1})]})]}),e.jsxs(d,{children:[e.jsxs(i,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Most Common"}),e.jsx(K,{className:"h-4 w-4 text-muted-foreground"})]}),e.jsx(m,{children:e.jsx("div",{className:"text-sm font-bold",children:((v=Object.entries(r.categoryBreakdown).sort(([,s],[,t])=>t-s)[0])==null?void 0:v[0])||"N/A"})})]})]}),e.jsx("div",{className:"space-y-4",children:x?Array.from({length:3}).map((s,t)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-4 animate-pulse",children:[e.jsxs("div",{className:"flex justify-between items-start mb-2",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4"}),e.jsx("div",{className:"h-6 bg-gray-200 rounded w-20"})]}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-full mb-1"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-3/4"})]},t)):o.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(h,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No feedback yet"}),e.jsx("p",{className:"text-gray-500",children:"Feedback submissions will appear here when users submit them."})]}):o.map(s=>e.jsxs("div",{className:`bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer ${s.isRead?"":"border-l-4 border-burgundy-500"}`,onClick:()=>D(s),children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-2",children:[e.jsxs("div",{className:"flex items-center gap-2 mb-2 md:mb-0",children:[e.jsx("h3",{className:"font-medium text-navy-800",children:s.subject}),!s.isRead&&e.jsx(n,{variant:"default",children:"New"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(n,{className:N(s.category),children:s.category}),s.rating&&e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx(k,{className:"h-4 w-4 fill-yellow-400 text-yellow-400"}),e.jsx("span",{className:"text-sm text-gray-600",children:s.rating})]})]})]}),e.jsxs("div",{className:"flex items-center gap-4 text-sm text-gray-600 mb-2",children:[e.jsx("span",{className:"font-medium",children:s.name}),e.jsx("span",{children:s.email}),e.jsx("span",{children:j(new Date(s.createdAt.seconds*1e3),"MMM dd, yyyy HH:mm")})]}),e.jsx("p",{className:"text-gray-700 line-clamp-2",children:s.message})]},s.id))}),e.jsx(B,{open:R,onOpenChange:y,children:e.jsxs(q,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[e.jsxs(z,{children:[e.jsxs(P,{className:"flex items-center gap-2",children:[e.jsx(h,{className:"h-5 w-5"}),a==null?void 0:a.subject]}),e.jsxs(V,{children:["Feedback submitted by ",a==null?void 0:a.name," on"," ",a&&j(new Date(a.createdAt.seconds*1e3),"MMMM dd, yyyy at HH:mm")]})]}),a&&e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Name:"}),e.jsx("p",{children:a.name})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Email:"}),e.jsx("p",{children:a.email})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Category:"}),e.jsx(n,{className:N(a.category),children:a.category})]}),a.rating&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Rating:"}),e.jsx(C,{value:a.rating,readonly:!0,size:"sm"})]})]}),e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"Message:"}),e.jsx("div",{className:"mt-2 p-3 bg-gray-50 rounded-md",children:e.jsx("p",{className:"whitespace-pre-wrap",children:a.message})})]}),a.userAgent&&e.jsxs("div",{children:[e.jsx("span",{className:"font-medium text-gray-700",children:"User Agent:"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:a.userAgent})]}),e.jsx("div",{className:"flex items-center gap-2 pt-4 border-t",children:a.isRead?e.jsxs("div",{className:"flex items-center gap-2 text-green-600",children:[e.jsx($,{className:"h-4 w-4"}),e.jsxs("span",{className:"text-sm",children:["Read ",a.readAt&&j(new Date(a.readAt.seconds*1e3),"MMM dd, yyyy HH:mm")]})]}):e.jsxs("div",{className:"flex items-center gap-2 text-gray-500",children:[e.jsx(J,{className:"h-4 w-4"}),e.jsx("span",{className:"text-sm",children:"Unread"})]})})]})]})})]})};export{re as default};
