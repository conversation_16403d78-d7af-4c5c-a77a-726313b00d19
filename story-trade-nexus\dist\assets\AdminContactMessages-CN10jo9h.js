import{j as s}from"./chunk-CpdlqjqK.js";import{r as e}from"./chunk-b0HHmiEU.js";import{f as a}from"./chunk-D1Z7_RhR.js";import{u as r,t as i,k as t,c as d}from"./index-DwRq5Uwb.js";import{g as l,m as n,a as c}from"./chunk-jAw1jmB0.js";import{D as m,a as o,b as x,c as h,d as j}from"./chunk-BjtUNXro.js";import{A as g}from"./chunk-BS5eevMn.js";import{k as u,R as p,l as N,M as f,g as v,E as y,m as w}from"./chunk-BGoCADfv.js";import"./chunk-Cw96wKwP.js";import"./chunk-BvIisuNF.js";import"./chunk-DgNUPAoM.js";const b=()=>{r();const[b,k]=e.useState([]),[R,M]=e.useState(!0),[C,A]=e.useState(null),[F,S]=e.useState(!1),[D,E]=e.useState(!1);e.useEffect((()=>{P()}),[]);const P=async()=>{try{M(!0);const s=await l();k(s)}catch(s){i({title:"Error",description:"Failed to load contact messages. Please try again.",variant:"destructive"})}finally{M(!1)}},L=async s=>{try{E(!0),await n(s),k(b.map((e=>e.id===s?{...e,isRead:!0,readAt:new Date}:e))),C&&C.id===s&&A({...C,isRead:!0,readAt:new Date}),i({title:"Success",description:"Message marked as read.",variant:"default"})}catch(e){i({title:"Error",description:"Failed to mark message as read. Please try again.",variant:"destructive"})}finally{E(!1)}},U=async s=>{try{E(!0),await c(s),k(b.map((e=>e.id===s?{...e,isRead:!1,readAt:null}:e))),C&&C.id===s&&A({...C,isRead:!1,readAt:null}),i({title:"Success",description:"Message marked as unread.",variant:"default"})}catch(e){i({title:"Error",description:"Failed to mark message as unread. Please try again.",variant:"destructive"})}finally{E(!1)}},V=()=>{S(!1),A(null)},z=s=>{if(!s)return"N/A";const e=s.toDate?s.toDate():new Date(s);return a(e,"MMM d, yyyy h:mm a")},O=b.filter((s=>!s.isRead)).length;return s.jsxs(g,{title:"Contact Messages",description:"View and manage contact messages from users",children:[s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Contact Messages"}),s.jsxs("p",{className:"text-gray-600",children:["View and respond to messages from users",O>0&&s.jsxs(t,{variant:"destructive",className:"ml-2",children:[O," unread"]})]})]}),s.jsx(d,{onClick:P,variant:"outline",className:"mt-4 md:mt-0",disabled:R,children:R?s.jsxs(s.Fragment,{children:[s.jsx(u,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):s.jsxs(s.Fragment,{children:[s.jsx(p,{className:"mr-2 h-4 w-4"}),"Refresh"]})})]}),R?s.jsxs("div",{className:"flex justify-center items-center py-12",children:[s.jsx(u,{className:"h-8 w-8 animate-spin text-burgundy-500"}),s.jsx("span",{className:"ml-2 text-gray-600",children:"Loading messages..."})]}):0===b.length?s.jsxs("div",{className:"bg-white rounded-lg shadow-md p-8 text-center",children:[s.jsx(N,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("h2",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No Messages Yet"}),s.jsx("p",{className:"text-gray-600",children:"There are no contact messages from users yet. Messages will appear here when users send them through the Contact Us page."})]}):s.jsx("div",{className:"space-y-4",children:b.map((e=>s.jsx("div",{className:"bg-white rounded-lg shadow-md p-4 transition-all hover:shadow-lg cursor-pointer "+(e.isRead?"":"border-l-4 border-burgundy-500"),onClick:()=>(s=>{A(s),S(!0),!s.isRead&&s.id&&L(s.id)})(e),children:s.jsxs("div",{className:"flex flex-col md:flex-row justify-between",children:[s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center mb-2",children:[s.jsx(f,{className:"h-4 w-4 text-gray-500 mr-2"}),s.jsx("span",{className:"text-navy-800 font-medium",children:e.email}),!e.isRead&&s.jsx(t,{variant:"default",className:"ml-2",children:"New"})]}),s.jsxs("div",{className:"flex items-center mb-2",children:[s.jsx(v,{className:"h-4 w-4 text-gray-500 mr-2"}),s.jsx("span",{className:"text-gray-600",children:e.phone})]}),s.jsx("p",{className:"text-gray-700 line-clamp-2 mb-2",children:e.message}),s.jsxs("div",{className:"text-xs text-gray-500",children:["Received: ",z(e.createdAt)]})]}),s.jsx("div",{className:"mt-4 md:mt-0 md:ml-4 flex items-center",children:s.jsx(d,{variant:"ghost",size:"sm",onClick:s=>{s.stopPropagation(),e.id&&(e.isRead?U(e.id):L(e.id))},disabled:D,children:e.isRead?s.jsxs(s.Fragment,{children:[s.jsx(y,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):s.jsxs(s.Fragment,{children:[s.jsx(w,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})})]})},e.id)))}),s.jsx(m,{open:F,onOpenChange:V,children:s.jsxs(o,{className:"sm:max-w-lg",children:[s.jsxs(x,{children:[s.jsx(h,{children:"Message Details"}),s.jsx(j,{children:"Contact message from user"})]}),C&&s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[s.jsx("div",{className:"font-medium",children:"From:"}),s.jsx("div",{className:"col-span-2",children:C.email})]}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[s.jsx("div",{className:"font-medium",children:"Phone:"}),s.jsx("div",{className:"col-span-2",children:C.phone})]}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[s.jsx("div",{className:"font-medium",children:"Received:"}),s.jsx("div",{className:"col-span-2",children:z(C.createdAt)})]}),s.jsxs("div",{className:"grid grid-cols-3 gap-4 py-2 border-b",children:[s.jsx("div",{className:"font-medium",children:"Status:"}),s.jsx("div",{className:"col-span-2",children:C.isRead?s.jsxs("span",{className:"text-green-600 flex items-center",children:[s.jsx(w,{className:"h-4 w-4 mr-1"}),"Read ",C.readAt&&`(${z(C.readAt)})`]}):s.jsxs("span",{className:"text-burgundy-600 flex items-center",children:[s.jsx(y,{className:"h-4 w-4 mr-1"}),"Unread"]})})]}),s.jsxs("div",{className:"py-2",children:[s.jsx("div",{className:"font-medium mb-2",children:"Message:"}),s.jsx("div",{className:"bg-gray-50 p-4 rounded-md whitespace-pre-wrap",children:C.message})]}),s.jsxs("div",{className:"flex justify-end space-x-2",children:[s.jsx(d,{variant:"outline",onClick:V,children:"Close"}),s.jsx(d,{variant:C.isRead?"outline":"default",onClick:()=>{C.id&&(C.isRead?U(C.id):L(C.id))},disabled:D,children:D?s.jsx(u,{className:"h-4 w-4 animate-spin"}):C.isRead?s.jsxs(s.Fragment,{children:[s.jsx(y,{className:"h-4 w-4 mr-2"}),"Mark as unread"]}):s.jsxs(s.Fragment,{children:[s.jsx(w,{className:"h-4 w-4 mr-2"}),"Mark as read"]})})]})]})]})})]})};export{b as default};
