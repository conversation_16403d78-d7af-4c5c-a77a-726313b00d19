import{j as a}from"./chunk-CpdlqjqK.js";import{r as s}from"./chunk-b0HHmiEU.js";import{x as e}from"./index-DwRq5Uwb.js";const r=s.forwardRef((({className:s,...r},d)=>a.jsx("div",{ref:d,className:e("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})));r.displayName="Card";const d=s.forwardRef((({className:s,...r},d)=>a.jsx("div",{ref:d,className:e("flex flex-col space-y-1.5 p-6",s),...r})));d.displayName="CardHeader";const o=s.forwardRef((({className:s,...r},d)=>a.jsx("h3",{ref:d,className:e("text-2xl font-semibold leading-none tracking-tight",s),...r})));o.displayName="CardTitle";const t=s.forwardRef((({className:s,...r},d)=>a.jsx("p",{ref:d,className:e("text-sm text-muted-foreground",s),...r})));t.displayName="CardDescription";const m=s.forwardRef((({className:s,...r},d)=>a.jsx("div",{ref:d,className:e("p-6 pt-0",s),...r})));m.displayName="CardContent";s.forwardRef((({className:s,...r},d)=>a.jsx("div",{ref:d,className:e("flex items-center p-6 pt-0",s),...r}))).displayName="CardFooter";export{r as C,d as a,o as b,m as c,t as d};
