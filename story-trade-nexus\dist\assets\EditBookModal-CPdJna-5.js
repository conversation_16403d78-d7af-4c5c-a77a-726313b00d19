import{j as e,t as i,u as t}from"./chunk-CpdlqjqK.js";import{r as a}from"./chunk-b0HHmiEU.js";import{x as r,X as s,I as l,a as n,Y as d}from"./index-DwRq5Uwb.js";import{T as o}from"./chunk-DQKmWaJS.js";import{S as c,a as u,b as h,c as x,d as m}from"./chunk-BpJWUrWd.js";import{w as p,X as v,f as j}from"./chunk-BGoCADfv.js";import{J as b}from"./chunk-Cw96wKwP.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const g=a.forwardRef((({className:a,...s},l)=>e.jsx(i,{ref:l,className:r("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",a),...s,children:e.jsx(t,{className:r("flex items-center justify-center text-current"),children:e.jsx(p,{className:"h-4 w-4"})})})));g.displayName=i.displayName;const y=["New","Like New","Good","Fair"],f=["For Rent","For Exchange","For Sale","For Rent & Sale","For Rent & Exchange","For Sale & Exchange","For Rent, Sale & Exchange"],D=["Available","Sold Out","Rented Out"],k=["per day","per week","per month"],N=["Fiction","Non-Fiction","Mystery","Romance","Science Fiction","Fantasy","Biography","History","Self-Help","Business","Technology","Health","Travel","Cooking","Art","Religion","Philosophy","Poetry","Drama","Children","Young Adult","Educational","Reference"],F=({book:i,isOpen:t,onClose:r,onBookUpdated:p})=>{const[F,A]=a.useState({title:"",author:"",isbn:"",genre:[],condition:"Good",description:"",availability:"For Exchange",price:void 0,rentalPrice:void 0,rentalPeriod:"per week",securityDepositRequired:!1,securityDepositAmount:void 0,imageUrls:[],status:"Available",nextAvailableDate:void 0}),[S,C]=a.useState(!1),[w,P]=a.useState([]);a.useEffect((()=>{i&&(A({title:i.title,author:i.author,isbn:i.isbn||"",genre:i.genre,condition:i.condition,description:i.description,availability:i.availability,price:i.price,rentalPrice:i.rentalPrice,rentalPeriod:i.rentalPeriod||"per week",securityDepositRequired:i.securityDepositRequired||!1,securityDepositAmount:i.securityDepositAmount,imageUrls:i.imageUrls||[i.imageUrl],status:i.status||"Available",nextAvailableDate:i.nextAvailableDate}),P(i.genre))}),[i]);const R=(e,i)=>{A((t=>({...t,[e]:i})))};return t?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsx("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto",children:e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsx("h2",{className:"text-2xl font-playfair font-bold text-navy-800",children:"Edit Book"}),e.jsx("button",{onClick:r,className:"text-gray-400 hover:text-gray-500",disabled:S,children:e.jsx(v,{className:"h-6 w-6"})})]}),e.jsxs("form",{onSubmit:async e=>{var t;if(e.preventDefault(),F.title.trim()&&F.author.trim())if(0!==w.length){C(!0);try{const e={title:F.title.trim(),author:F.author.trim(),isbn:(null==(t=F.isbn)?void 0:t.trim())||void 0,genre:w,condition:F.condition,description:F.description.trim(),availability:F.availability,price:F.price,rentalPrice:F.rentalPrice,rentalPeriod:F.rentalPeriod,securityDepositRequired:F.securityDepositRequired,securityDepositAmount:F.securityDepositAmount,imageUrls:F.imageUrls,status:F.status,nextAvailableDate:F.nextAvailableDate};F.imageUrls&&F.imageUrls.length>0&&(e.imageUrl=F.imageUrls[0]),await d(i.id,e);const a={...i,...e};p(a),b.success("Book updated successfully"),r()}catch(a){b.error("Failed to update book")}finally{C(!1)}}else b.error("Please select at least one genre");else b.error("Title and author are required")},className:"space-y-6",children:[e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(s,{htmlFor:"title",children:"Book Title *"}),e.jsx(l,{id:"title",value:F.title,onChange:e=>R("title",e.target.value),placeholder:"Enter book title",disabled:S,required:!0})]}),e.jsxs("div",{children:[e.jsx(s,{htmlFor:"author",children:"Author *"}),e.jsx(l,{id:"author",value:F.author,onChange:e=>R("author",e.target.value),placeholder:"Enter author name",disabled:S,required:!0})]}),e.jsxs("div",{children:[e.jsx(s,{htmlFor:"isbn",children:"ISBN (Optional)"}),e.jsx(l,{id:"isbn",value:F.isbn,onChange:e=>R("isbn",e.target.value),placeholder:"Enter ISBN",disabled:S})]}),e.jsxs("div",{children:[e.jsx(s,{htmlFor:"condition",children:"Condition *"}),e.jsxs(c,{value:F.condition,onValueChange:e=>R("condition",e),disabled:S,children:[e.jsx(u,{children:e.jsx(h,{placeholder:"Select condition"})}),e.jsx(x,{children:y.map((i=>e.jsx(m,{value:i,children:i},i)))})]})]}),e.jsxs("div",{children:[e.jsx(s,{htmlFor:"status",children:"Current Status"}),e.jsxs(c,{value:F.status,onValueChange:e=>R("status",e),disabled:S,children:[e.jsx(u,{children:e.jsx(h,{placeholder:"Select status"})}),e.jsx(x,{children:D.map((i=>e.jsx(m,{value:i,children:i},i)))})]})]}),"Rented Out"===F.status&&e.jsxs("div",{children:[e.jsx(s,{htmlFor:"nextAvailableDate",children:"Expected Return Date"}),e.jsx(l,{id:"nextAvailableDate",type:"date",value:F.nextAvailableDate?F.nextAvailableDate.toISOString().split("T")[0]:"",onChange:e=>R("nextAvailableDate",e.target.value?new Date(e.target.value):void 0),disabled:S})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx(s,{htmlFor:"availability",children:"Availability Type *"}),e.jsxs(c,{value:F.availability,onValueChange:e=>R("availability",e),disabled:S,children:[e.jsx(u,{children:e.jsx(h,{placeholder:"Select availability"})}),e.jsx(x,{children:f.map((i=>e.jsx(m,{value:i,children:i},i)))})]})]}),F.availability.includes("Sale")&&e.jsxs("div",{children:[e.jsx(s,{htmlFor:"price",children:"Sale Price (₹)"}),e.jsx(l,{id:"price",type:"number",value:F.price||"",onChange:e=>R("price",e.target.value?Number(e.target.value):void 0),placeholder:"Enter sale price",disabled:S})]}),F.availability.includes("Rent")&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx(s,{htmlFor:"rentalPrice",children:"Rental Price (₹)"}),e.jsx(l,{id:"rentalPrice",type:"number",value:F.rentalPrice||"",onChange:e=>R("rentalPrice",e.target.value?Number(e.target.value):void 0),placeholder:"Enter rental price",disabled:S})]}),e.jsxs("div",{children:[e.jsx(s,{htmlFor:"rentalPeriod",children:"Rental Period"}),e.jsxs(c,{value:F.rentalPeriod,onValueChange:e=>R("rentalPeriod",e),disabled:S,children:[e.jsx(u,{children:e.jsx(h,{placeholder:"Select period"})}),e.jsx(x,{children:k.map((i=>e.jsx(m,{value:i,children:i},i)))})]})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(g,{id:"securityDeposit",checked:F.securityDepositRequired,onCheckedChange:e=>R("securityDepositRequired",e),disabled:S}),e.jsx(s,{htmlFor:"securityDeposit",children:"Require Security Deposit"})]}),F.securityDepositRequired&&e.jsxs("div",{children:[e.jsx(s,{htmlFor:"securityDepositAmount",children:"Security Deposit Amount (₹)"}),e.jsx(l,{id:"securityDepositAmount",type:"number",value:F.securityDepositAmount||"",onChange:e=>R("securityDepositAmount",e.target.value?Number(e.target.value):void 0),placeholder:"Enter deposit amount",disabled:S})]})]})]})]}),e.jsxs("div",{children:[e.jsx(s,{htmlFor:"description",children:"Description *"}),e.jsx(o,{id:"description",value:F.description,onChange:e=>R("description",e.target.value),placeholder:"Describe the book's content, condition, and any other relevant details",rows:4,disabled:S,required:!0})]}),e.jsxs("div",{children:[e.jsx(s,{children:"Genres * (Select at least one)"}),e.jsx("div",{className:"grid grid-cols-3 md:grid-cols-4 gap-2 mt-2",children:N.map((i=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(g,{id:`genre-${i}`,checked:w.includes(i),onCheckedChange:()=>(e=>{const i=w.includes(e)?w.filter((i=>i!==e)):[...w,e];P(i),R("genre",i)})(i),disabled:S}),e.jsx(s,{htmlFor:`genre-${i}`,className:"text-sm",children:i})]},i)))})]}),e.jsxs("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[e.jsx(n,{type:"button",variant:"outline",onClick:r,disabled:S,children:"Cancel"}),e.jsx(n,{type:"submit",disabled:S,className:"flex items-center",children:S?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Updating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(j,{className:"h-4 w-4 mr-2"}),"Update Book"]})})]})]})]})})}):null};export{F as EditBookModal};
