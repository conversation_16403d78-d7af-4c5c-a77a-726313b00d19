const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.esm-ehpEbksy.js","assets/index.esm2017-H7c5Bkvh.js"])))=>i.map(i=>d[i]);
import{K as D,_ as P,N as S,B as I,r as a,aj as z,j as e,h as i,Y as m,l as B,L,U as _,X as O,ak as $,J as l,al as U}from"./index-BiRTTmW2.js";import{D as V,a as G,b as J,c as H,d as K,e as X}from"./dialog-2rEEUDTj.js";import{T as Y}from"./textarea-BZWEd9Tf.js";import{A as q}from"./AdminLayout-DbLrXmoL.js";import{P as T}from"./plus-DKFfwrMX.js";import{E as M}from"./eye-CKny-2Ce.js";import"./users-DHm-dyBk.js";const Q=async()=>{try{await D();const{collection:t,addDoc:c,serverTimestamp:o}=await P(async()=>{const{collection:r,addDoc:j,serverTimestamp:d}=await import("./index.esm-ehpEbksy.js");return{collection:r,addDoc:j,serverTimestamp:d}},__vite__mapDeps([0,1]));console.log("Creating test pending book");const x=t(S,"books"),h={title:"Test Pending Book",author:"Test Author",isbn:"1234567890123",genre:["Fiction","Test"],condition:"Good",description:"This is a test book created for testing the admin approval workflow.",imageUrl:"https://via.placeholder.com/150?text=Test+Book",perceivedValue:5,price:299,availability:"For Sale & Exchange",ownerId:"testuser123",ownerName:"Test User",ownerLocation:"Test Location",ownerRating:4.5,distance:2.5,createdAt:o(),updatedAt:o(),approvalStatus:I.Pending},n=await c(x,h);return console.log("Test pending book created successfully with ID:",n.id),n.id}catch(t){throw console.error("Error creating test pending book:",t),t}},ie=()=>{const[t,c]=a.useState([]),[o,x]=a.useState(!0),[h,n]=a.useState(null),[r,j]=a.useState(null),[d,b]=a.useState(""),[R,g]=a.useState(!1),[k,y]=a.useState(!1),[u,N]=a.useState(!1),[p,w]=a.useState(!1);a.useEffect(()=>{f()},[]);const f=async()=>{try{x(!0),n(null),console.log("Fetching pending books...");const s=await z();console.log(`Received ${s.length} pending books`),c(s)}catch(s){console.error("Error fetching pending books:",s),s instanceof Error?n(`Failed to load pending books: ${s.message}`):n("Failed to load pending books. Please try again."),s instanceof Error&&console.error("Error details:",{message:s.message,stack:s.stack,name:s.name})}finally{x(!1)}},A=async s=>{try{y(!0),await $(s.id),l.success(`"${s.title}" has been approved`),c(t.filter(v=>v.id!==s.id))}catch(v){console.error("Error approving book:",v),l.error("Failed to approve book. Please try again.")}finally{y(!1)}},F=s=>{j(s),b(""),g(!0)},E=async()=>{if(r)try{N(!0),await U(r.id,d),l.success(`"${r.title}" has been rejected`),c(t.filter(s=>s.id!==r.id)),g(!1)}catch(s){console.error("Error rejecting book:",s),l.error("Failed to reject book. Please try again.")}finally{N(!1)}},C=async()=>{try{w(!0);const s=await Q();l.success("Test pending book created successfully"),f()}catch(s){console.error("Error creating test book:",s),l.error("Failed to create test book. Please try again.")}finally{w(!1)}};return e.jsxs(q,{title:"Book Approvals",description:"Review and approve new book submissions",children:[e.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:"Book Approvals"}),e.jsx("p",{className:"text-gray-600",children:"Review and approve new book submissions"})]}),e.jsxs("div",{className:"flex gap-2 mt-4 md:mt-0",children:[e.jsx(i,{variant:"outline",onClick:C,disabled:p||o,className:"flex items-center",children:p?e.jsxs(e.Fragment,{children:[e.jsx(m,{size:"sm",className:"mr-2"}),"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Create Test Book"]})}),e.jsx(i,{variant:"outline",onClick:f,disabled:o,children:o?e.jsxs(e.Fragment,{children:[e.jsx(B,{className:"h-4 w-4 mr-2 animate-spin"}),"Loading..."]}):e.jsxs(e.Fragment,{children:[e.jsx(B,{className:"h-4 w-4 mr-2"}),"Refresh"]})})]})]}),h&&e.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6",children:[e.jsx("p",{children:h}),e.jsxs("details",{className:"mt-2",children:[e.jsx("summary",{className:"text-sm cursor-pointer",children:"Debug Information"}),e.jsxs("div",{className:"mt-2 text-xs",children:[e.jsx("p",{children:"If you're seeing this error, try the following:"}),e.jsxs("ol",{className:"list-decimal pl-5 mt-1 space-y-1",children:[e.jsx("li",{children:'Click the "Create Test Book" button to add a test pending book'}),e.jsx("li",{children:"Check your Firebase permissions and rules"}),e.jsx("li",{children:"Verify that your Firestore database has the correct structure"}),e.jsx("li",{children:"Check the browser console for more detailed error messages"})]})]})]})]}),o?e.jsxs("div",{className:"flex justify-center items-center py-12",children:[e.jsx(m,{size:"lg"}),e.jsx("span",{className:"ml-2 text-gray-600",children:"Loading pending books..."})]}):t.length>0?e.jsx("div",{className:"space-y-6",children:t.map(s=>e.jsx("div",{className:"bg-white rounded-lg shadow-md p-4",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-4",children:[e.jsx("div",{className:"w-32 h-32 flex-shrink-0",children:e.jsx("img",{src:s.imageUrl||"https://via.placeholder.com/150?text=No+Image",alt:s.title,className:"w-full h-full object-cover rounded-md"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h2",{className:"text-xl font-medium text-navy-800",children:s.title}),e.jsxs("p",{className:"text-gray-600 mb-2",children:["by ",s.author]}),e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2 mb-2",children:[e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Owner:"})," ",s.ownerName]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Genre:"})," ",Array.isArray(s.genre)?s.genre.join(", "):s.genre]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Condition:"})," ",s.condition]}),e.jsxs("div",{children:[e.jsx("span",{className:"text-sm text-gray-500",children:"Availability:"})," ",s.availability]})]}),e.jsx("p",{className:"text-sm text-gray-700 mb-3 line-clamp-2",children:s.description}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(L,{to:`/books/${s.id}`,target:"_blank",rel:"noopener noreferrer",children:e.jsxs(i,{variant:"outline",size:"sm",className:"flex items-center gap-1",children:[e.jsx(M,{className:"h-4 w-4"}),"View Details"]})}),e.jsxs(i,{variant:"default",size:"sm",className:"flex items-center gap-1 bg-green-600 hover:bg-green-700",onClick:()=>A(s),disabled:k,children:[k?e.jsx(m,{size:"sm",className:"mr-1"}):e.jsx(_,{className:"h-4 w-4"}),"Approve"]}),e.jsxs(i,{variant:"destructive",size:"sm",className:"flex items-center gap-1",onClick:()=>F(s),disabled:u,children:[e.jsx(O,{className:"h-4 w-4"}),"Reject"]})]})]})]})},s.id))}):e.jsxs("div",{className:"bg-gray-50 rounded-lg p-8 text-center",children:[e.jsx("h2",{className:"text-xl font-medium text-gray-700 mb-2",children:"No pending books"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"All books have been reviewed. Check back later for new submissions."}),e.jsx(i,{variant:"outline",onClick:C,disabled:p,className:"mt-2",children:p?e.jsxs(e.Fragment,{children:[e.jsx(m,{size:"sm",className:"mr-2"}),"Creating Test Book..."]}):e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"h-4 w-4 mr-2"}),"Create Test Book for Approval"]})}),e.jsx("p",{className:"text-xs text-gray-500 mt-4",children:"Click the button above to create a test book with pending approval status. This is useful for testing the approval workflow."})]}),e.jsx(V,{open:R,onOpenChange:g,children:e.jsxs(G,{children:[e.jsxs(J,{children:[e.jsx(H,{children:"Reject Book"}),e.jsxs(K,{children:['Please provide a reason for rejecting "',r==null?void 0:r.title,'". This will be visible to the user who submitted the book.']})]}),e.jsx(Y,{placeholder:"Enter rejection reason...",value:d,onChange:s=>b(s.target.value),className:"min-h-[100px]"}),e.jsxs(X,{children:[e.jsx(i,{variant:"outline",onClick:()=>g(!1),children:"Cancel"}),e.jsx(i,{variant:"destructive",onClick:E,disabled:!d.trim()||u,children:u?e.jsxs(e.Fragment,{children:[e.jsx(m,{size:"sm",className:"mr-2"}),"Rejecting..."]}):"Confirm Rejection"})]})]})})]})};export{ie as default};
