/**
 * LCP Performance Monitor Component
 * 
 * This component monitors Largest Contentful Paint (LCP) performance
 * and provides insights into the effectiveness of preload optimizations.
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */

import React, { useEffect, useState } from 'react';
import { monitorLCPPerformance } from '@/lib/headManager';

interface LCPMetrics {
  value: number;
  element?: Element;
  url?: string;
  timestamp: number;
  wasPreloaded: boolean;
}

interface LCPMonitorProps {
  enabled?: boolean;
  onLCPMeasured?: (metrics: LCPMetrics) => void;
  showDebugInfo?: boolean;
}

const LCPMonitor: React.FC<LCPMonitorProps> = ({
  enabled = true,
  onLCPMeasured,
  showDebugInfo = false
}) => {
  const [lcpMetrics, setLcpMetrics] = useState<LCPMetrics | null>(null);
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    if (!enabled || typeof window === 'undefined') {
      return;
    }

    // Check if PerformanceObserver is supported
    const supported = 'PerformanceObserver' in window;
    setIsSupported(supported);

    if (!supported) {
      console.warn('[LCP Monitor] PerformanceObserver not supported in this browser');
      return;
    }

    let observer: PerformanceObserver | null = null;

    try {
      observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1] as any;

        if (lastEntry) {
          // Check if the LCP element was preloaded
          let wasPreloaded = false;
          if (lastEntry.element && lastEntry.element.tagName === 'IMG') {
            const imgSrc = (lastEntry.element as HTMLImageElement).src;
            wasPreloaded = !!document.querySelector(`link[rel="preload"][href="${imgSrc}"]`);
          }

          const metrics: LCPMetrics = {
            value: lastEntry.startTime,
            element: lastEntry.element,
            url: lastEntry.url,
            timestamp: Date.now(),
            wasPreloaded
          };

          setLcpMetrics(metrics);
          onLCPMeasured?.(metrics);

          // Log performance data
          console.log(`[LCP Monitor] LCP: ${metrics.value.toFixed(2)}ms`, {
            element: metrics.element?.tagName,
            url: metrics.url,
            wasPreloaded: metrics.wasPreloaded,
            elementClasses: metrics.element?.className,
          });

          // Provide feedback on preload effectiveness
          if (metrics.element?.tagName === 'IMG') {
            if (metrics.wasPreloaded) {
              console.log('✅ [LCP Monitor] LCP image was preloaded - optimization working!');
            } else {
              console.log('⚠️ [LCP Monitor] LCP image was NOT preloaded - consider adding preload');
            }
          }
        }
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });

      // Also start the general LCP monitoring from headManager
      monitorLCPPerformance();

    } catch (error) {
      console.error('[LCP Monitor] Failed to initialize LCP monitoring:', error);
    }

    // Cleanup
    return () => {
      observer?.disconnect();
    };
  }, [enabled, onLCPMeasured]);

  // Don't render anything in production unless debug info is requested
  if (!showDebugInfo || process.env.NODE_ENV === 'production') {
    return null;
  }

 /** return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs font-mono z-50 max-w-xs">
      <div className="font-bold mb-2">🚀 LCP Monitor</div>
      
      {!isSupported ? (
        <div className="text-red-300">PerformanceObserver not supported</div>
      ) : !lcpMetrics ? (
        <div className="text-yellow-300">Waiting for LCP measurement...</div>
      ) : (
        <div className="space-y-1">
          <div className={`font-bold ${lcpMetrics.value <= 2500 ? 'text-green-300' : lcpMetrics.value <= 4000 ? 'text-yellow-300' : 'text-red-300'}`}>
            LCP: {lcpMetrics.value.toFixed(0)}ms
          </div>
          
          <div className="text-gray-300">
            Element: {lcpMetrics.element?.tagName || 'Unknown'}
          </div>
          
          {lcpMetrics.element?.tagName === 'IMG' && (
            <div className={`${lcpMetrics.wasPreloaded ? 'text-green-300' : 'text-red-300'}`}>
              {lcpMetrics.wasPreloaded ? '✅ Preloaded' : '❌ Not preloaded'}
            </div>
          )}
          
          <div className="text-xs text-gray-400 mt-2">
            Good: &lt;2.5s | Needs improvement: 2.5-4s | Poor: &gt;4s
          </div>
        </div>
      )}
    </div>
  **/);
};

export default LCPMonitor;
