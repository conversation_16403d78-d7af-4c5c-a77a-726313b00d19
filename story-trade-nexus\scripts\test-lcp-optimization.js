/**
 * LCP Optimization Testing Script
 *
 * This script helps validate the LCP optimization implementation
 * by checking for proper preload links and image attributes.
 *
 * Usage: node scripts/test-lcp-optimization.js
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

// Helper function for colored console output
const log = {
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  header: (msg) => console.log(`${colors.bold}${colors.blue}\n🚀 ${msg}${colors.reset}`)
};

// Test configuration
const testConfig = {
  srcDir: path.join(__dirname, '..', 'src'),
  requiredFiles: [
    'lib/headManager.ts',
    'hooks/useLCPOptimization.ts',
    'components/LCPMonitor.tsx'
  ],
  modifiedFiles: [
    'components/LazyImage.tsx',
    'components/FeaturedBooks.tsx',
    'pages/BrowseBooks.tsx',
    'components/BookCard.tsx',
    'App.tsx'
  ]
};

/**
 * Check if all required files exist
 */
function checkRequiredFiles() {
  log.header('Checking Required Files');

  let allFilesExist = true;

  testConfig.requiredFiles.forEach(file => {
    const filePath = path.join(testConfig.srcDir, file);
    if (fs.existsSync(filePath)) {
      log.success(`Found: ${file}`);
    } else {
      log.error(`Missing: ${file}`);
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

/**
 * Check if modified files contain expected LCP optimization code
 */
function checkModifiedFiles() {
  log.header('Checking Modified Files for LCP Optimization');

  const checks = [
    {
      file: 'components/LazyImage.tsx',
      patterns: [
        'fetchPriority',
        'priority.*boolean',
        'loading.*eager.*lazy'
      ],
      description: 'LazyImage component with fetchPriority support'
    },
    {
      file: 'components/FeaturedBooks.tsx',
      patterns: [
        'useHomepageLCPOptimization',
        'priority.*index.*4'
      ],
      description: 'FeaturedBooks with LCP optimization hooks'
    },
    {
      file: 'pages/BrowseBooks.tsx',
      patterns: [
        'useBrowseBooksLCPOptimization',
        'priority.*index.*8'
      ],
      description: 'BrowseBooks with LCP optimization hooks'
    },
    {
      file: 'components/BookCard.tsx',
      patterns: [
        'priority.*boolean',
        'index.*number',
        'shouldPrioritize',
        'fetchPriority'
      ],
      description: 'BookCard with priority loading support'
    },
    {
      file: 'App.tsx',
      patterns: [
        'LCPMonitor',
        'process.env.NODE_ENV.*development'
      ],
      description: 'App component with LCP monitoring'
    }
  ];

  let allChecksPass = true;

  checks.forEach(check => {
    const filePath = path.join(testConfig.srcDir, check.file);

    if (!fs.existsSync(filePath)) {
      log.error(`File not found: ${check.file}`);
      allChecksPass = false;
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const passedPatterns = [];
    const failedPatterns = [];

    check.patterns.forEach(pattern => {
      const regex = new RegExp(pattern, 'i');
      if (regex.test(content)) {
        passedPatterns.push(pattern);
      } else {
        failedPatterns.push(pattern);
      }
    });

    if (failedPatterns.length === 0) {
      log.success(`${check.description}`);
    } else {
      log.warning(`${check.description} - Missing patterns: ${failedPatterns.join(', ')}`);
      allChecksPass = false;
    }
  });

  return allChecksPass;
}

/**
 * Check for TypeScript compilation issues
 */
function checkTypeScriptTypes() {
  log.header('Checking TypeScript Types');

  const typeFiles = [
    'lib/headManager.ts',
    'hooks/useLCPOptimization.ts'
  ];

  let typesValid = true;

  typeFiles.forEach(file => {
    const filePath = path.join(testConfig.srcDir, file);

    if (!fs.existsSync(filePath)) {
      log.error(`Type file not found: ${file}`);
      typesValid = false;
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');

    // Check for essential TypeScript interfaces
    const requiredInterfaces = [
      'PreloadLinkConfig',
      'ImagePreloadConfig',
      'LCPOptimizationOptions'
    ];

    const foundInterfaces = requiredInterfaces.filter(interfaceName => {
      return content.includes(`interface ${interfaceName}`);
    });

    if (foundInterfaces.length === requiredInterfaces.length) {
      log.success(`TypeScript interfaces found in ${file}`);
    } else {
      const missing = requiredInterfaces.filter(i => !foundInterfaces.includes(i));
      log.warning(`Missing interfaces in ${file}: ${missing.join(', ')}`);
      typesValid = false;
    }
  });

  return typesValid;
}

/**
 * Check for proper import/export structure
 */
function checkImportsExports() {
  log.header('Checking Import/Export Structure');

  const importChecks = [
    {
      file: 'components/FeaturedBooks.tsx',
      imports: ['useHomepageLCPOptimization'],
      description: 'FeaturedBooks imports LCP hook'
    },
    {
      file: 'pages/BrowseBooks.tsx',
      imports: ['useBrowseBooksLCPOptimization'],
      description: 'BrowseBooks imports LCP hook'
    },
    {
      file: 'App.tsx',
      imports: ['LCPMonitor'],
      description: 'App imports LCP monitor'
    }
  ];

  let importsValid = true;

  importChecks.forEach(check => {
    const filePath = path.join(testConfig.srcDir, check.file);

    if (!fs.existsSync(filePath)) {
      log.error(`File not found: ${check.file}`);
      importsValid = false;
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    const missingImports = check.imports.filter(imp => !content.includes(imp));

    if (missingImports.length === 0) {
      log.success(check.description);
    } else {
      log.error(`${check.description} - Missing: ${missingImports.join(', ')}`);
      importsValid = false;
    }
  });

  return importsValid;
}

/**
 * Generate implementation summary
 */
function generateSummary(results) {
  log.header('Implementation Summary');

  const totalChecks = Object.keys(results).length;
  const passedChecks = Object.values(results).filter(Boolean).length;
  const successRate = Math.round((passedChecks / totalChecks) * 100);

  console.log(`\n📊 Test Results: ${passedChecks}/${totalChecks} checks passed (${successRate}%)\n`);

  Object.entries(results).forEach(([check, passed]) => {
    if (passed) {
      log.success(check);
    } else {
      log.error(check);
    }
  });

  if (successRate === 100) {
    log.success('\n🎉 All LCP optimization checks passed! Implementation is ready.');
    console.log(`
${colors.green}Next steps:${colors.reset}
1. Start the development server: npm run dev
2. Open browser DevTools and check the Console for LCP optimization logs
3. Run Lighthouse performance audit to measure LCP improvements
4. Test on different viewport sizes and network conditions
    `);
  } else {
    log.warning('\n⚠️  Some checks failed. Please review the implementation.');
    console.log(`
${colors.yellow}Troubleshooting:${colors.reset}
1. Check the failed items above
2. Ensure all files are properly saved
3. Verify import paths are correct
4. Run TypeScript compilation to check for errors
    `);
  }

  return successRate === 100;
}

/**
 * Main test function
 */
function runTests() {
  console.log(`${colors.bold}${colors.blue}
╔══════════════════════════════════════════════════════════════╗
║                    LCP Optimization Test Suite              ║
║                         PeerBooks                           ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}`);

  const results = {
    'Required Files Exist': checkRequiredFiles(),
    'Modified Files Updated': checkModifiedFiles(),
    'TypeScript Types Valid': checkTypeScriptTypes(),
    'Imports/Exports Correct': checkImportsExports()
  };

  const allTestsPassed = generateSummary(results);

  // Exit with appropriate code
  process.exit(allTestsPassed ? 0 : 1);
}

// Run the tests
runTests();
