import{r as e,R as t,a as n,G as r,b as o}from"./chunk-b0HHmiEU.js";var a={exports:{}},i={},s=e,c=Symbol.for("react.element"),l=Symbol.for("react.fragment"),u=Object.prototype.hasOwnProperty,d=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,f={key:!0,ref:!0,__self:!0,__source:!0};function p(e,t,n){var r,o={},a=null,i=null;for(r in void 0!==n&&(a=""+n),void 0!==t.key&&(a=""+t.key),void 0!==t.ref&&(i=t.ref),t)u.call(t,r)&&!f.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:c,type:e,key:a,ref:i,props:o,_owner:d.current}}i.Fragment=l,i.jsx=p,i.jsxs=p,a.exports=i;var v=a.exports;function h(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(null==e||e(r),!1===n||!r.defaultPrevented)return null==t?void 0:t(r)}}function m(...e){return t=>e.forEach((e=>function(e,t){"function"==typeof e?e(t):null!=e&&(e.current=t)}(e,t)))}function g(...t){return e.useCallback(m(...t),t)}function w(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var y=e.forwardRef(((t,n)=>{const{children:r,...o}=t,a=e.Children.toArray(r),i=a.find(C);if(i){const t=i.props.children,r=a.map((n=>n===i?e.Children.count(t)>1?e.Children.only(null):e.isValidElement(t)?t.props.children:null:n));return v.jsx(x,{...o,ref:n,children:e.isValidElement(t)?e.cloneElement(t,void 0,r):null})}return v.jsx(x,{...o,ref:n,children:r})}));y.displayName="Slot";var x=e.forwardRef(((t,n)=>{const{children:r,...o}=t;if(e.isValidElement(r)){const t=function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(r);return e.cloneElement(r,{...E(o,r.props),ref:n?m(n,t):t})}return e.Children.count(r)>1?e.Children.only(null):null}));x.displayName="SlotClone";var b=({children:e})=>v.jsx(v.Fragment,{children:e});function C(t){return e.isValidElement(t)&&t.type===b}function E(e,t){const n={...t};for(const r in t){const o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}function R(n){const r=n+"CollectionProvider",[o,a]=function(t,n=[]){let r=[];const o=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=(null==r?void 0:r[t])||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return o.scopeName=t,[function(n,o){const a=e.createContext(o),i=r.length;function s(n){const{scope:r,children:o,...s}=n,c=(null==r?void 0:r[t][i])||a,l=e.useMemo((()=>s),Object.values(s));return v.jsx(c.Provider,{value:l,children:o})}return r=[...r,o],s.displayName=n+"Provider",[s,function(r,s){const c=(null==s?void 0:s[t][i])||a,l=e.useContext(c);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},w(o,...n)]}(r),[i,s]=o(r,{collectionRef:{current:null},itemMap:new Map}),c=e=>{const{scope:n,children:r}=e,o=t.useRef(null),a=t.useRef(new Map).current;return v.jsx(i,{scope:n,itemMap:a,collectionRef:o,children:r})};c.displayName=r;const l=n+"CollectionSlot",u=t.forwardRef(((e,t)=>{const{scope:n,children:r}=e,o=g(t,s(l,n).collectionRef);return v.jsx(y,{ref:o,children:r})}));u.displayName=l;const d=n+"CollectionItemSlot",f="data-radix-collection-item",p=t.forwardRef(((e,n)=>{const{scope:r,children:o,...a}=e,i=t.useRef(null),c=g(n,i),l=s(d,r);return t.useEffect((()=>(l.itemMap.set(i,{ref:i,...a}),()=>{l.itemMap.delete(i)}))),v.jsx(y,{[f]:"",ref:c,children:o})}));return p.displayName=d,[{Provider:c,Slot:u,ItemSlot:p},function(e){const r=s(n+"CollectionConsumer",e);return t.useCallback((()=>{const e=r.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll(`[${f}]`));return Array.from(r.itemMap.values()).sort(((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current)))}),[r.collectionRef,r.itemMap])},a]}function _(t,n=[]){let r=[];const o=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=(null==r?void 0:r[t])||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return o.scopeName=t,[function(n,o){const a=e.createContext(o),i=r.length;r=[...r,o];const s=n=>{var r;const{scope:o,children:s,...c}=n,l=(null==(r=null==o?void 0:o[t])?void 0:r[i])||a,u=e.useMemo((()=>c),Object.values(c));return v.jsx(l.Provider,{value:u,children:s})};return s.displayName=n+"Provider",[s,function(r,s){var c;const l=(null==(c=null==s?void 0:s[t])?void 0:c[i])||a,u=e.useContext(l);if(u)return u;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},D(o,...n)]}function D(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var T=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce(((t,n)=>{const r=e.forwardRef(((e,t)=>{const{asChild:r,...o}=e,a=r?y:n;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),v.jsx(a,{...o,ref:t})}));return r.displayName=`Primitive.${n}`,{...t,[n]:r}}),{});function P(e,t){e&&n.flushSync((()=>e.dispatchEvent(t)))}function S(t){const n=e.useRef(t);return e.useEffect((()=>{n.current=t})),e.useMemo((()=>(...e)=>{var t;return null==(t=n.current)?void 0:t.call(n,...e)}),[])}var j,M="dismissableLayer.update",k="dismissableLayer.pointerDownOutside",N="dismissableLayer.focusOutside",O=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),A=e.forwardRef(((t,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:i,onInteractOutside:s,onDismiss:c,...l}=t,u=e.useContext(O),[d,f]=e.useState(null),p=(null==d?void 0:d.ownerDocument)??(null==globalThis?void 0:globalThis.document),[,m]=e.useState({}),w=g(n,(e=>f(e))),y=Array.from(u.layers),[x]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),b=y.indexOf(x),C=d?y.indexOf(d):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,R=C>=b,_=function(t,n=(null==globalThis?void 0:globalThis.document)){const r=S(t),o=e.useRef(!1),a=e.useRef((()=>{}));return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let t=function(){F(k,r,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout((()=>{n.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}}),[n,r]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...u.branches].some((e=>e.contains(t)));R&&!n&&(null==a||a(e),null==s||s(e),e.defaultPrevented||null==c||c())}),p),D=function(t,n=(null==globalThis?void 0:globalThis.document)){const r=S(t),o=e.useRef(!1);return e.useEffect((()=>{const e=e=>{if(e.target&&!o.current){F(N,r,{originalEvent:e},{discrete:!1})}};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)}),[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...u.branches].some((e=>e.contains(t)))||(null==i||i(e),null==s||s(e),e.defaultPrevented||null==c||c())}),p);return function(t,n=(null==globalThis?void 0:globalThis.document)){const r=S(t);e.useEffect((()=>{const e=e=>{"Escape"===e.key&&r(e)};return n.addEventListener("keydown",e,{capture:!0}),()=>n.removeEventListener("keydown",e,{capture:!0})}),[r,n])}((e=>{C===u.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))}),p),e.useEffect((()=>{if(d)return r&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(j=p.body.style.pointerEvents,p.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(d)),u.layers.add(d),L(),()=>{r&&1===u.layersWithOutsidePointerEventsDisabled.size&&(p.body.style.pointerEvents=j)}}),[d,p,r,u]),e.useEffect((()=>()=>{d&&(u.layers.delete(d),u.layersWithOutsidePointerEventsDisabled.delete(d),L())}),[d,u]),e.useEffect((()=>{const e=()=>m({});return document.addEventListener(M,e),()=>document.removeEventListener(M,e)}),[]),v.jsx(T.div,{...l,ref:w,style:{pointerEvents:E?R?"auto":"none":void 0,...t.style},onFocusCapture:h(t.onFocusCapture,D.onFocusCapture),onBlurCapture:h(t.onBlurCapture,D.onBlurCapture),onPointerDownCapture:h(t.onPointerDownCapture,_.onPointerDownCapture)})}));A.displayName="DismissableLayer";var I=e.forwardRef(((t,n)=>{const r=e.useContext(O),o=e.useRef(null),a=g(n,o);return e.useEffect((()=>{const e=o.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}}),[r.branches]),v.jsx(T.div,{...t,ref:a})}));function L(){const e=new CustomEvent(M);document.dispatchEvent(e)}function F(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?P(o,a):o.dispatchEvent(a)}I.displayName="DismissableLayerBranch";var K=A,W=I,B=Boolean(null==globalThis?void 0:globalThis.document)?e.useLayoutEffect:()=>{},H=e.forwardRef(((t,n)=>{var o;const{container:a,...i}=t,[s,c]=e.useState(!1);B((()=>c(!0)),[]);const l=a||s&&(null==(o=null==globalThis?void 0:globalThis.document)?void 0:o.body);return l?r.createPortal(v.jsx(T.div,{...i,ref:n}),l):null}));H.displayName="Portal";var $=t=>{const{present:n,children:r}=t,o=function(t){const[n,r]=e.useState(),o=e.useRef({}),a=e.useRef(t),i=e.useRef("none"),s=t?"mounted":"unmounted",[c,l]=function(t,n){return e.useReducer(((e,t)=>n[e][t]??e),t)}(s,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return e.useEffect((()=>{const e=V(o.current);i.current="mounted"===c?e:"none"}),[c]),B((()=>{const e=o.current,n=a.current;if(n!==t){const r=i.current,o=V(e);if(t)l("MOUNT");else if("none"===o||"none"===(null==e?void 0:e.display))l("UNMOUNT");else{l(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}a.current=t}}),[t,l]),B((()=>{if(n){let e;const t=n.ownerDocument.defaultView??window,r=r=>{const i=V(o.current).includes(r.animationName);if(r.target===n&&i&&(l("ANIMATION_END"),!a.current)){const r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout((()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)}))}},s=e=>{e.target===n&&(i.current=V(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}l("ANIMATION_END")}),[n,l]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:e.useCallback((e=>{e&&(o.current=getComputedStyle(e)),r(e)}),[])}}(n),a="function"==typeof r?r({present:o.isPresent}):e.Children.only(r),i=g(o.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(a));return"function"==typeof r||o.isPresent?e.cloneElement(a,{ref:i}):null};function V(e){return(null==e?void 0:e.animationName)||"none"}function U({prop:t,defaultProp:n,onChange:r=()=>{}}){const[o,a]=function({defaultProp:t,onChange:n}){const r=e.useState(t),[o]=r,a=e.useRef(o),i=S(n);return e.useEffect((()=>{a.current!==o&&(i(o),a.current=o)}),[o,a,i]),r}({defaultProp:n,onChange:r}),i=void 0!==t,s=i?t:o,c=S(r);return[s,e.useCallback((e=>{if(i){const n="function"==typeof e?e(t):e;n!==t&&c(n)}else a(e)}),[i,t,a,c])]}$.displayName="Presence";var z=e.forwardRef(((e,t)=>v.jsx(T.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})));z.displayName="VisuallyHidden";var X=z,Y="ToastProvider",[q,G,Z]=R("Toast"),[J,Q]=_("Toast",[Z]),[ee,te]=J(Y),ne=t=>{const{__scopeToast:n,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:s}=t,[c,l]=e.useState(null),[u,d]=e.useState(0),f=e.useRef(!1),p=e.useRef(!1);return r.trim(),v.jsx(q.Provider,{scope:n,children:v.jsx(ee,{scope:n,label:r,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:u,viewport:c,onViewportChange:l,onToastAdd:e.useCallback((()=>d((e=>e+1))),[]),onToastRemove:e.useCallback((()=>d((e=>e-1))),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:s})})};ne.displayName=Y;var re="ToastViewport",oe=["F8"],ae="toast.viewportPause",ie="toast.viewportResume",se=e.forwardRef(((t,n)=>{const{__scopeToast:r,hotkey:o=oe,label:a="Notifications ({hotkey})",...i}=t,s=te(re,r),c=G(r),l=e.useRef(null),u=e.useRef(null),d=e.useRef(null),f=e.useRef(null),p=g(n,f,s.onViewportChange),h=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),m=s.toastCount>0;e.useEffect((()=>{const e=e=>{var t;0!==o.length&&o.every((t=>e[t]||e.code===t))&&(null==(t=f.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[o]),e.useEffect((()=>{const e=l.current,t=f.current;if(m&&e&&t){const n=()=>{if(!s.isClosePausedRef.current){const e=new CustomEvent(ae);t.dispatchEvent(e),s.isClosePausedRef.current=!0}},r=()=>{if(s.isClosePausedRef.current){const e=new CustomEvent(ie);t.dispatchEvent(e),s.isClosePausedRef.current=!1}},o=t=>{!e.contains(t.relatedTarget)&&r()},a=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",a),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",a),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}}),[m,s.isClosePausedRef]);const w=e.useCallback((({tabbingDirection:e})=>{const t=c().map((t=>{const n=t.ref.current,r=[n,..._e(n)];return"forwards"===e?r:r.reverse()}));return("forwards"===e?t.reverse():t).flat()}),[c]);return e.useEffect((()=>{const e=f.current;if(e){const t=t=>{var n,r,o;const a=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!a){const a=document.activeElement,i=t.shiftKey;if(t.target===e&&i)return void(null==(n=u.current)||n.focus());const s=w({tabbingDirection:i?"backwards":"forwards"}),c=s.findIndex((e=>e===a));De(s.slice(c+1))?t.preventDefault():i?null==(r=u.current)||r.focus():null==(o=d.current)||o.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}}),[c,w]),v.jsxs(W,{ref:l,role:"region","aria-label":a.replace("{hotkey}",h),tabIndex:-1,style:{pointerEvents:m?void 0:"none"},children:[m&&v.jsx(le,{ref:u,onFocusFromOutsideViewport:()=>{De(w({tabbingDirection:"forwards"}))}}),v.jsx(q.Slot,{scope:r,children:v.jsx(T.ol,{tabIndex:-1,...i,ref:p})}),m&&v.jsx(le,{ref:d,onFocusFromOutsideViewport:()=>{De(w({tabbingDirection:"backwards"}))}})]})}));se.displayName=re;var ce="ToastFocusProxy",le=e.forwardRef(((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,a=te(ce,n);return v.jsx(z,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{var t;const n=e.relatedTarget;!(null==(t=a.viewport)?void 0:t.contains(n))&&r()}})}));le.displayName=ce;var ue="Toast",de=e.forwardRef(((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:a,...i}=e,[s=!0,c]=U({prop:r,defaultProp:o,onChange:a});return v.jsx($,{present:n||s,children:v.jsx(ve,{open:s,...i,ref:t,onClose:()=>c(!1),onPause:S(e.onPause),onResume:S(e.onResume),onSwipeStart:h(e.onSwipeStart,(e=>{e.currentTarget.setAttribute("data-swipe","start")})),onSwipeMove:h(e.onSwipeMove,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${n}px`)})),onSwipeCancel:h(e.onSwipeCancel,(e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")})),onSwipeEnd:h(e.onSwipeEnd,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${n}px`),c(!1)}))})})}));de.displayName=ue;var[fe,pe]=J(ue,{onClose(){}}),ve=e.forwardRef(((t,r)=>{const{__scopeToast:o,type:a="foreground",duration:i,open:s,onClose:c,onEscapeKeyDown:l,onPause:u,onResume:d,onSwipeStart:f,onSwipeMove:p,onSwipeCancel:m,onSwipeEnd:w,...y}=t,x=te(ue,o),[b,C]=e.useState(null),E=g(r,(e=>C(e))),R=e.useRef(null),_=e.useRef(null),D=i||x.duration,P=e.useRef(0),j=e.useRef(D),M=e.useRef(0),{onToastAdd:k,onToastRemove:N}=x,O=S((()=>{var e;(null==b?void 0:b.contains(document.activeElement))&&(null==(e=x.viewport)||e.focus()),c()})),A=e.useCallback((e=>{e&&e!==1/0&&(window.clearTimeout(M.current),P.current=(new Date).getTime(),M.current=window.setTimeout(O,e))}),[O]);e.useEffect((()=>{const e=x.viewport;if(e){const t=()=>{A(j.current),null==d||d()},n=()=>{const e=(new Date).getTime()-P.current;j.current=j.current-e,window.clearTimeout(M.current),null==u||u()};return e.addEventListener(ae,n),e.addEventListener(ie,t),()=>{e.removeEventListener(ae,n),e.removeEventListener(ie,t)}}}),[x.viewport,D,u,d,A]),e.useEffect((()=>{s&&!x.isClosePausedRef.current&&A(D)}),[s,D,x.isClosePausedRef,A]),e.useEffect((()=>(k(),()=>N())),[k,N]);const I=e.useMemo((()=>b?Ce(b):null),[b]);return x.viewport?v.jsxs(v.Fragment,{children:[I&&v.jsx(he,{__scopeToast:o,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:I}),v.jsx(fe,{scope:o,onClose:O,children:n.createPortal(v.jsx(q.ItemSlot,{scope:o,children:v.jsx(K,{asChild:!0,onEscapeKeyDown:h(l,(()=>{x.isFocusedToastEscapeKeyDownRef.current||O(),x.isFocusedToastEscapeKeyDownRef.current=!1})),children:v.jsx(T.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":x.swipeDirection,...y,ref:E,style:{userSelect:"none",touchAction:"none",...t.style},onKeyDown:h(t.onKeyDown,(e=>{"Escape"===e.key&&(null==l||l(e.nativeEvent),e.nativeEvent.defaultPrevented||(x.isFocusedToastEscapeKeyDownRef.current=!0,O()))})),onPointerDown:h(t.onPointerDown,(e=>{0===e.button&&(R.current={x:e.clientX,y:e.clientY})})),onPointerMove:h(t.onPointerMove,(e=>{if(!R.current)return;const t=e.clientX-R.current.x,n=e.clientY-R.current.y,r=Boolean(_.current),o=["left","right"].includes(x.swipeDirection),a=["left","up"].includes(x.swipeDirection)?Math.min:Math.max,i=o?a(0,t):0,s=o?0:a(0,n),c="touch"===e.pointerType?10:2,l={x:i,y:s},u={originalEvent:e,delta:l};r?(_.current=l,Ee("toast.swipeMove",p,u,{discrete:!1})):Re(l,x.swipeDirection,c)?(_.current=l,Ee("toast.swipeStart",f,u,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>c||Math.abs(n)>c)&&(R.current=null)})),onPointerUp:h(t.onPointerUp,(e=>{const t=_.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),_.current=null,R.current=null,t){const n=e.currentTarget,r={originalEvent:e,delta:t};Re(t,x.swipeDirection,x.swipeThreshold)?Ee("toast.swipeEnd",w,r,{discrete:!0}):Ee("toast.swipeCancel",m,r,{discrete:!0}),n.addEventListener("click",(e=>e.preventDefault()),{once:!0})}}))})})}),x.viewport)})]}):null})),he=t=>{const{__scopeToast:n,children:r,...o}=t,a=te(ue,n),[i,s]=e.useState(!1),[c,l]=e.useState(!1);return function(e=()=>{}){const t=S(e);B((()=>{let e=0,n=0;return e=window.requestAnimationFrame((()=>n=window.requestAnimationFrame(t))),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}}),[t])}((()=>s(!0))),e.useEffect((()=>{const e=window.setTimeout((()=>l(!0)),1e3);return()=>window.clearTimeout(e)}),[]),c?null:v.jsx(H,{asChild:!0,children:v.jsx(z,{...o,children:i&&v.jsxs(v.Fragment,{children:[a.label," ",r]})})})},me=e.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e;return v.jsx(T.div,{...r,ref:t})}));me.displayName="ToastTitle";var ge=e.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e;return v.jsx(T.div,{...r,ref:t})}));ge.displayName="ToastDescription";var we=e.forwardRef(((e,t)=>{const{altText:n,...r}=e;return n.trim()?v.jsx(be,{altText:n,asChild:!0,children:v.jsx(xe,{...r,ref:t})}):null}));we.displayName="ToastAction";var ye="ToastClose",xe=e.forwardRef(((e,t)=>{const{__scopeToast:n,...r}=e,o=pe(ye,n);return v.jsx(be,{asChild:!0,children:v.jsx(T.button,{type:"button",...r,ref:t,onClick:h(e.onClick,o.onClose)})})}));xe.displayName=ye;var be=e.forwardRef(((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return v.jsx(T.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})}));function Ce(e){const t=[];return Array.from(e.childNodes).forEach((e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const n=e.ariaHidden||e.hidden||"none"===e.style.display,r=""===e.dataset.radixToastAnnounceExclude;if(!n)if(r){const n=e.dataset.radixToastAnnounceAlt;n&&t.push(n)}else t.push(...Ce(e))}})),t}function Ee(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?P(o,a):o.dispatchEvent(a)}var Re=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),a=r>o;return"left"===t||"right"===t?a&&r>n:!a&&o>n};function _e(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function De(e){const t=document.activeElement;return e.some((e=>e===t||(e.focus(),document.activeElement!==t)))}var Te=ne,Pe=se,Se=de,je=me,Me=ge,ke=we,Ne=xe,Oe=o["useId".toString()]||(()=>{}),Ae=0;function Ie(t){const[n,r]=e.useState(Oe());return B((()=>{r((e=>e??String(Ae++)))}),[t]),n?`radix-${n}`:""}const Le=["top","right","bottom","left"],Fe=Math.min,Ke=Math.max,We=Math.round,Be=Math.floor,He=e=>({x:e,y:e}),$e={left:"right",right:"left",bottom:"top",top:"bottom"},Ve={start:"end",end:"start"};function Ue(e,t,n){return Ke(e,Fe(t,n))}function ze(e,t){return"function"==typeof e?e(t):e}function Xe(e){return e.split("-")[0]}function Ye(e){return e.split("-")[1]}function qe(e){return"x"===e?"y":"x"}function Ge(e){return"y"===e?"height":"width"}function Ze(e){return["top","bottom"].includes(Xe(e))?"y":"x"}function Je(e){return qe(Ze(e))}function Qe(e){return e.replace(/start|end/g,(e=>Ve[e]))}function et(e){return e.replace(/left|right|bottom|top/g,(e=>$e[e]))}function tt(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function nt(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function rt(e,t,n){let{reference:r,floating:o}=e;const a=Ze(t),i=Je(t),s=Ge(i),c=Xe(t),l="y"===a,u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[s]/2-o[s]/2;let p;switch(c){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(Ye(t)){case"start":p[i]-=f*(n&&l?-1:1);break;case"end":p[i]+=f*(n&&l?-1:1)}return p}async function ot(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:a,rects:i,elements:s,strategy:c}=e,{boundary:l="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=ze(t,e),v=tt(p),h=s[f?"floating"===d?"reference":"floating":d],m=nt(await a.getClippingRect({element:null==(n=await(null==a.isElement?void 0:a.isElement(h)))||n?h:h.contextElement||await(null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating)),boundary:l,rootBoundary:u,strategy:c})),g="floating"===d?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,w=await(null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating)),y=await(null==a.isElement?void 0:a.isElement(w))&&await(null==a.getScale?void 0:a.getScale(w))||{x:1,y:1},x=nt(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:g,offsetParent:w,strategy:c}):g);return{top:(m.top-x.top+v.top)/y.y,bottom:(x.bottom-m.bottom+v.bottom)/y.y,left:(m.left-x.left+v.left)/y.x,right:(x.right-m.right+v.right)/y.x}}function at(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function it(e){return Le.some((t=>e[t]>=0))}function st(){return"undefined"!=typeof window}function ct(e){return dt(e)?(e.nodeName||"").toLowerCase():"#document"}function lt(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ut(e){var t;return null==(t=(dt(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function dt(e){return!!st()&&(e instanceof Node||e instanceof lt(e).Node)}function ft(e){return!!st()&&(e instanceof Element||e instanceof lt(e).Element)}function pt(e){return!!st()&&(e instanceof HTMLElement||e instanceof lt(e).HTMLElement)}function vt(e){return!(!st()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof lt(e).ShadowRoot)}function ht(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=bt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function mt(e){return["table","td","th"].includes(ct(e))}function gt(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function wt(e){const t=yt(),n=ft(e)?bt(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function yt(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function xt(e){return["html","body","#document"].includes(ct(e))}function bt(e){return lt(e).getComputedStyle(e)}function Ct(e){return ft(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Et(e){if("html"===ct(e))return e;const t=e.assignedSlot||e.parentNode||vt(e)&&e.host||ut(e);return vt(t)?t.host:t}function Rt(e){const t=Et(e);return xt(t)?e.ownerDocument?e.ownerDocument.body:e.body:pt(t)&&ht(t)?t:Rt(t)}function _t(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Rt(e),a=o===(null==(r=e.ownerDocument)?void 0:r.body),i=lt(o);if(a){const e=Dt(i);return t.concat(i,i.visualViewport||[],ht(o)?o:[],e&&n?_t(e):[])}return t.concat(o,_t(o,[],n))}function Dt(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Tt(e){const t=bt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=pt(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,s=We(n)!==a||We(r)!==i;return s&&(n=a,r=i),{width:n,height:r,$:s}}function Pt(e){return ft(e)?e:e.contextElement}function St(e){const t=Pt(e);if(!pt(t))return He(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=Tt(t);let i=(a?We(n.width):n.width)/r,s=(a?We(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}const jt=He(0);function Mt(e){const t=lt(e);return yt()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:jt}function kt(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),a=Pt(e);let i=He(1);t&&(r?ft(r)&&(i=St(r)):i=St(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==lt(e))&&t}(a,n,r)?Mt(a):He(0);let c=(o.left+s.x)/i.x,l=(o.top+s.y)/i.y,u=o.width/i.x,d=o.height/i.y;if(a){const e=lt(a),t=r&&ft(r)?lt(r):r;let n=e,o=Dt(n);for(;o&&r&&t!==n;){const e=St(o),t=o.getBoundingClientRect(),r=bt(o),a=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,l*=e.y,u*=e.x,d*=e.y,c+=a,l+=i,n=lt(o),o=Dt(n)}}return nt({width:u,height:d,x:c,y:l})}function Nt(e,t){const n=Ct(e).scrollLeft;return t?t.left+n:kt(ut(e)).left+n}function Ot(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=lt(e),r=ut(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,s=0,c=0;if(o){a=o.width,i=o.height;const e=yt();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,c=o.offsetTop)}return{width:a,height:i,x:s,y:c}}(e,n);else if("document"===t)r=function(e){const t=ut(e),n=Ct(e),r=e.ownerDocument.body,o=Ke(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=Ke(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Nt(e);const s=-n.scrollTop;return"rtl"===bt(r).direction&&(i+=Ke(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:s}}(ut(e));else if(ft(t))r=function(e,t){const n=kt(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=pt(e)?St(e):He(1);return{width:e.clientWidth*a.x,height:e.clientHeight*a.y,x:o*a.x,y:r*a.y}}(t,n);else{const n=Mt(e);r={...t,x:t.x-n.x,y:t.y-n.y}}return nt(r)}function At(e,t){const n=Et(e);return!(n===t||!ft(n)||xt(n))&&("fixed"===bt(n).position||At(n,t))}function It(e,t,n){const r=pt(t),o=ut(t),a="fixed"===n,i=kt(e,!0,a,t);let s={scrollLeft:0,scrollTop:0};const c=He(0);if(r||!r&&!a)if(("body"!==ct(t)||ht(o))&&(s=Ct(t)),r){const e=kt(t,!0,a,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=Nt(o));let l=0,u=0;if(o&&!r&&!a){const e=o.getBoundingClientRect();u=e.top+s.scrollTop,l=e.left+s.scrollLeft-Nt(o,e)}return{x:i.left+s.scrollLeft-c.x-l,y:i.top+s.scrollTop-c.y-u,width:i.width,height:i.height}}function Lt(e){return"static"===bt(e).position}function Ft(e,t){if(!pt(e)||"fixed"===bt(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ut(e)===n&&(n=n.ownerDocument.body),n}function Kt(e,t){const n=lt(e);if(gt(e))return n;if(!pt(e)){let t=Et(e);for(;t&&!xt(t);){if(ft(t)&&!Lt(t))return t;t=Et(t)}return n}let r=Ft(e,t);for(;r&&mt(r)&&Lt(r);)r=Ft(r,t);return r&&xt(r)&&Lt(r)&&!wt(r)?n:r||function(e){let t=Et(e);for(;pt(t)&&!xt(t);){if(wt(t))return t;if(gt(t))return null;t=Et(t)}return null}(e)||n}const Wt={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a="fixed"===o,i=ut(r),s=!!t&&gt(t.floating);if(r===i||s&&a)return n;let c={scrollLeft:0,scrollTop:0},l=He(1);const u=He(0),d=pt(r);if((d||!d&&!a)&&(("body"!==ct(r)||ht(i))&&(c=Ct(r)),pt(r))){const e=kt(r);l=St(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}return{width:n.width*l.x,height:n.height*l.y,x:n.x*l.x-c.scrollLeft*l.x+u.x,y:n.y*l.y-c.scrollTop*l.y+u.y}},getDocumentElement:ut,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const a=[..."clippingAncestors"===n?gt(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=_t(e,[],!1).filter((e=>ft(e)&&"body"!==ct(e))),o=null;const a="fixed"===bt(e).position;let i=a?Et(e):e;for(;ft(i)&&!xt(i);){const t=bt(i),n=wt(i);n||"fixed"!==t.position||(o=null),(a?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||ht(i)&&!n&&At(e,i))?r=r.filter((e=>e!==i)):o=t,i=Et(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=a[0],s=a.reduce(((e,n)=>{const r=Ot(t,n,o);return e.top=Ke(r.top,e.top),e.right=Fe(r.right,e.right),e.bottom=Fe(r.bottom,e.bottom),e.left=Ke(r.left,e.left),e}),Ot(t,i,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:Kt,getElementRects:async function(e){const t=this.getOffsetParent||Kt,n=this.getDimensions,r=await n(e.floating);return{reference:It(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=Tt(e);return{width:t,height:n}},getScale:St,isElement:ft,isRTL:function(e){return"rtl"===bt(e).direction}};function Bt(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:c=!1}=r,l=Pt(e),u=o||a?[...l?_t(l):[],..._t(t)]:[];u.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)}));const d=l&&s?function(e,t){let n,r=null;const o=ut(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function i(s,c){void 0===s&&(s=!1),void 0===c&&(c=1),a();const{left:l,top:u,width:d,height:f}=e.getBoundingClientRect();if(s||t(),!d||!f)return;const p={rootMargin:-Be(u)+"px "+-Be(o.clientWidth-(l+d))+"px "+-Be(o.clientHeight-(u+f))+"px "+-Be(l)+"px",threshold:Ke(0,Fe(1,c))||1};let v=!0;function h(e){const t=e[0].intersectionRatio;if(t!==c){if(!v)return i();t?i(!1,t):n=setTimeout((()=>{i(!1,1e-7)}),1e3)}v=!1}try{r=new IntersectionObserver(h,{...p,root:o.ownerDocument})}catch(m){r=new IntersectionObserver(h,p)}r.observe(e)}(!0),a}(l,n):null;let f,p=-1,v=null;i&&(v=new ResizeObserver((e=>{let[r]=e;r&&r.target===l&&v&&(v.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame((()=>{var e;null==(e=v)||e.observe(t)}))),n()})),l&&!c&&v.observe(l),v.observe(t));let h=c?kt(e):null;return c&&function t(){const r=kt(e);!h||r.x===h.x&&r.y===h.y&&r.width===h.width&&r.height===h.height||n();h=r,f=requestAnimationFrame(t)}(),n(),()=>{var e;u.forEach((e=>{o&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)})),null==d||d(),null==(e=v)||e.disconnect(),v=null,c&&cancelAnimationFrame(f)}}const Ht=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:i,middlewareData:s}=t,c=await async function(e,t){const{placement:n,platform:r,elements:o}=e,a=await(null==r.isRTL?void 0:r.isRTL(o.floating)),i=Xe(n),s=Ye(n),c="y"===Ze(n),l=["left","top"].includes(i)?-1:1,u=a&&c?-1:1,d=ze(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:v}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof v&&(p="end"===s?-1*v:v),c?{x:p*u,y:f*l}:{x:f*l,y:p*u}}(t,e);return i===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+c.x,y:a+c.y,data:{...c,placement:i}}}}},$t=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=ze(e,t),l={x:n,y:r},u=await ot(t,c),d=Ze(Xe(o)),f=qe(d);let p=l[f],v=l[d];if(a){const e="y"===f?"bottom":"right";p=Ue(p+u["y"===f?"top":"left"],p,p-u[e])}if(i){const e="y"===d?"bottom":"right";v=Ue(v+u["y"===d?"top":"left"],v,v-u[e])}const h=s.fn({...t,[f]:p,[d]:v});return{...h,data:{x:h.x-n,y:h.y-r,enabled:{[f]:a,[d]:i}}}}}},Vt=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:s,platform:c,elements:l}=t,{mainAxis:u=!0,crossAxis:d=!0,fallbackPlacements:f,fallbackStrategy:p="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:h=!0,...m}=ze(e,t);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};const g=Xe(o),w=Ze(s),y=Xe(s)===s,x=await(null==c.isRTL?void 0:c.isRTL(l.floating)),b=f||(y||!h?[et(s)]:function(e){const t=et(e);return[Qe(e),t,Qe(t)]}(s)),C="none"!==v;!f&&C&&b.push(...function(e,t,n,r){const o=Ye(e);let a=function(e,t,n){const r=["left","right"],o=["right","left"],a=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?a:i;default:return[]}}(Xe(e),"start"===n,r);return o&&(a=a.map((e=>e+"-"+o)),t&&(a=a.concat(a.map(Qe)))),a}(s,h,v,x));const E=[s,...b],R=await ot(t,m),_=[];let D=(null==(r=a.flip)?void 0:r.overflows)||[];if(u&&_.push(R[g]),d){const e=function(e,t,n){void 0===n&&(n=!1);const r=Ye(e),o=Je(e),a=Ge(o);let i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=et(i)),[i,et(i)]}(o,i,x);_.push(R[e[0]],R[e[1]])}if(D=[...D,{placement:o,overflows:_}],!_.every((e=>e<=0))){var T,P;const e=((null==(T=a.flip)?void 0:T.index)||0)+1,t=E[e];if(t)return{data:{index:e,overflows:D},reset:{placement:t}};let n=null==(P=D.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:P.placement;if(!n)switch(p){case"bestFit":{var S;const e=null==(S=D.filter((e=>{if(C){const t=Ze(e.placement);return t===w||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:S[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}}}},Ut=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:a,platform:i,elements:s}=t,{apply:c=()=>{},...l}=ze(e,t),u=await ot(t,l),d=Xe(o),f=Ye(o),p="y"===Ze(o),{width:v,height:h}=a.floating;let m,g;"top"===d||"bottom"===d?(m=d,g=f===(await(null==i.isRTL?void 0:i.isRTL(s.floating))?"start":"end")?"left":"right"):(g=d,m="end"===f?"top":"bottom");const w=h-u.top-u.bottom,y=v-u.left-u.right,x=Fe(h-u[m],w),b=Fe(v-u[g],y),C=!t.middlewareData.shift;let E=x,R=b;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(R=y),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(E=w),C&&!f){const e=Ke(u.left,0),t=Ke(u.right,0),n=Ke(u.top,0),r=Ke(u.bottom,0);p?R=v-2*(0!==e||0!==t?e+t:Ke(u.left,u.right)):E=h-2*(0!==n||0!==r?n+r:Ke(u.top,u.bottom))}await c({...t,availableWidth:R,availableHeight:E});const _=await i.getDimensions(s.floating);return v!==_.width||h!==_.height?{reset:{rects:!0}}:{}}}},zt=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ze(e,t);switch(r){case"referenceHidden":{const e=at(await ot(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:it(e)}}}case"escaped":{const e=at(await ot(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:it(e)}}}default:return{}}}}},Xt=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:i,elements:s,middlewareData:c}=t,{element:l,padding:u=0}=ze(e,t)||{};if(null==l)return{};const d=tt(u),f={x:n,y:r},p=Je(o),v=Ge(p),h=await i.getDimensions(l),m="y"===p,g=m?"top":"left",w=m?"bottom":"right",y=m?"clientHeight":"clientWidth",x=a.reference[v]+a.reference[p]-f[p]-a.floating[v],b=f[p]-a.reference[p],C=await(null==i.getOffsetParent?void 0:i.getOffsetParent(l));let E=C?C[y]:0;E&&await(null==i.isElement?void 0:i.isElement(C))||(E=s.floating[y]||a.floating[v]);const R=x/2-b/2,_=E/2-h[v]/2-1,D=Fe(d[g],_),T=Fe(d[w],_),P=D,S=E-h[v]-T,j=E/2-h[v]/2+R,M=Ue(P,j,S),k=!c.arrow&&null!=Ye(o)&&j!==M&&a.reference[v]/2-(j<P?D:T)-h[v]/2<0,N=k?j<P?j-P:j-S:0;return{[p]:f[p]+N,data:{[p]:M,centerOffset:j-M-N,...k&&{alignmentOffset:N}},reset:k}}}),Yt=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:a,middlewareData:i}=t,{offset:s=0,mainAxis:c=!0,crossAxis:l=!0}=ze(e,t),u={x:n,y:r},d=Ze(o),f=qe(d);let p=u[f],v=u[d];const h=ze(s,t),m="number"==typeof h?{mainAxis:h,crossAxis:0}:{mainAxis:0,crossAxis:0,...h};if(c){const e="y"===f?"height":"width",t=a.reference[f]-a.floating[e]+m.mainAxis,n=a.reference[f]+a.reference[e]-m.mainAxis;p<t?p=t:p>n&&(p=n)}if(l){var g,w;const e="y"===f?"width":"height",t=["top","left"].includes(Xe(o)),n=a.reference[d]-a.floating[e]+(t&&(null==(g=i.offset)?void 0:g[d])||0)+(t?0:m.crossAxis),r=a.reference[d]+a.reference[e]+(t?0:(null==(w=i.offset)?void 0:w[d])||0)-(t?m.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[f]:p,[d]:v}}}},qt=(e,t,n)=>{const r=new Map,o={platform:Wt,...n},a={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,s=a.filter(Boolean),c=await(null==i.isRTL?void 0:i.isRTL(t));let l=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:u,y:d}=rt(l,r,c),f=r,p={},v=0;for(let h=0;h<s.length;h++){const{name:n,fn:a}=s[h],{x:m,y:g,data:w,reset:y}=await a({x:u,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:l,platform:i,elements:{reference:e,floating:t}});u=null!=m?m:u,d=null!=g?g:d,p={...p,[n]:{...p[n],...w}},y&&v<=50&&(v++,"object"==typeof y&&(y.placement&&(f=y.placement),y.rects&&(l=!0===y.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):y.rects),({x:u,y:d}=rt(l,f,c))),h=-1)}return{x:u,y:d,placement:f,strategy:o,middlewareData:p}})(e,t,{...o,platform:a})};var Gt="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function Zt(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!Zt(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!Zt(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function Jt(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Qt(e,t){const n=Jt(e);return Math.round(t*n)/n}function en(t){const n=e.useRef(t);return Gt((()=>{n.current=t})),n}const tn=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?Xt({element:n.current,padding:r}).fn(t):{}:n?Xt({element:n,padding:r}).fn(t):{};var o}}),nn=(e,t)=>({...Ht(e),options:[e,t]}),rn=(e,t)=>({...$t(e),options:[e,t]}),on=(e,t)=>({...Yt(e),options:[e,t]}),an=(e,t)=>({...Vt(e),options:[e,t]}),sn=(e,t)=>({...Ut(e),options:[e,t]}),cn=(e,t)=>({...zt(e),options:[e,t]}),ln=(e,t)=>({...tn(e),options:[e,t]});var un=e.forwardRef(((e,t)=>{const{children:n,width:r=10,height:o=5,...a}=e;return v.jsx(T.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:v.jsx("polygon",{points:"0,0 30,0 15,10"})})}));un.displayName="Arrow";var dn=un;function fn(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}function pn(t){const[n,r]=e.useState(void 0);return B((()=>{if(t){r({width:t.offsetWidth,height:t.offsetHeight});const e=new ResizeObserver((e=>{if(!Array.isArray(e))return;if(!e.length)return;const n=e[0];let o,a;if("borderBoxSize"in n){const e=n.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,a=t.blockSize}else o=t.offsetWidth,a=t.offsetHeight;r({width:o,height:a})}));return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}r(void 0)}),[t]),n}var vn="Popper",[hn,mn]=function(t,n=[]){let r=[];const o=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=(null==r?void 0:r[t])||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return o.scopeName=t,[function(n,o){const a=e.createContext(o),i=r.length;function s(n){const{scope:r,children:o,...s}=n,c=(null==r?void 0:r[t][i])||a,l=e.useMemo((()=>s),Object.values(s));return v.jsx(c.Provider,{value:l,children:o})}return r=[...r,o],s.displayName=n+"Provider",[s,function(r,s){const c=(null==s?void 0:s[t][i])||a,l=e.useContext(c);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},fn(o,...n)]}(vn),[gn,wn]=hn(vn),yn=t=>{const{__scopePopper:n,children:r}=t,[o,a]=e.useState(null);return v.jsx(gn,{scope:n,anchor:o,onAnchorChange:a,children:r})};yn.displayName=vn;var xn="PopperAnchor",bn=e.forwardRef(((t,n)=>{const{__scopePopper:r,virtualRef:o,...a}=t,i=wn(xn,r),s=e.useRef(null),c=g(n,s);return e.useEffect((()=>{i.onAnchorChange((null==o?void 0:o.current)||s.current)})),o?null:v.jsx(T.div,{...a,ref:c})}));bn.displayName=xn;var Cn="PopperContent",[En,Rn]=hn(Cn),_n=e.forwardRef(((t,r)=>{var o,a,i,s,c,l;const{__scopePopper:u,side:d="bottom",sideOffset:f=0,align:p="center",alignOffset:h=0,arrowPadding:m=0,avoidCollisions:w=!0,collisionBoundary:y=[],collisionPadding:x=0,sticky:b="partial",hideWhenDetached:C=!1,updatePositionStrategy:E="optimized",onPlaced:R,..._}=t,D=wn(Cn,u),[P,j]=e.useState(null),M=g(r,(e=>j(e))),[k,N]=e.useState(null),O=pn(k),A=(null==O?void 0:O.width)??0,I=(null==O?void 0:O.height)??0,L=d+("center"!==p?"-"+p:""),F="number"==typeof x?x:{top:0,right:0,bottom:0,left:0,...x},K=Array.isArray(y)?y:[y],W=K.length>0,H={padding:F,boundary:K.filter(Sn),altBoundary:W},{refs:$,floatingStyles:V,placement:U,isPositioned:z,middlewareData:X}=function(t){void 0===t&&(t={});const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i,elements:{reference:s,floating:c}={},transform:l=!0,whileElementsMounted:u,open:d}=t,[f,p]=e.useState({x:0,y:0,strategy:o,placement:r,middlewareData:{},isPositioned:!1}),[v,h]=e.useState(a);Zt(v,a)||h(a);const[m,g]=e.useState(null),[w,y]=e.useState(null),x=e.useCallback((e=>{e!==R.current&&(R.current=e,g(e))}),[]),b=e.useCallback((e=>{e!==_.current&&(_.current=e,y(e))}),[]),C=s||m,E=c||w,R=e.useRef(null),_=e.useRef(null),D=e.useRef(f),T=null!=u,P=en(u),S=en(i),j=en(d),M=e.useCallback((()=>{if(!R.current||!_.current)return;const e={placement:r,strategy:o,middleware:v};S.current&&(e.platform=S.current),qt(R.current,_.current,e).then((e=>{const t={...e,isPositioned:!1!==j.current};k.current&&!Zt(D.current,t)&&(D.current=t,n.flushSync((()=>{p(t)})))}))}),[v,r,o,S,j]);Gt((()=>{!1===d&&D.current.isPositioned&&(D.current.isPositioned=!1,p((e=>({...e,isPositioned:!1}))))}),[d]);const k=e.useRef(!1);Gt((()=>(k.current=!0,()=>{k.current=!1})),[]),Gt((()=>{if(C&&(R.current=C),E&&(_.current=E),C&&E){if(P.current)return P.current(C,E,M);M()}}),[C,E,M,P,T]);const N=e.useMemo((()=>({reference:R,floating:_,setReference:x,setFloating:b})),[x,b]),O=e.useMemo((()=>({reference:C,floating:E})),[C,E]),A=e.useMemo((()=>{const e={position:o,left:0,top:0};if(!O.floating)return e;const t=Qt(O.floating,f.x),n=Qt(O.floating,f.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...Jt(O.floating)>=1.5&&{willChange:"transform"}}:{position:o,left:t,top:n}}),[o,l,O.floating,f.x,f.y]);return e.useMemo((()=>({...f,update:M,refs:N,elements:O,floatingStyles:A})),[f,M,N,O,A])}({strategy:"fixed",placement:L,whileElementsMounted:(...e)=>Bt(...e,{animationFrame:"always"===E}),elements:{reference:D.anchor},middleware:[nn({mainAxis:f+I,alignmentAxis:h}),w&&rn({mainAxis:!0,crossAxis:!1,limiter:"partial"===b?on():void 0,...H}),w&&an({...H}),sn({...H,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{const{width:o,height:a}=t.reference,i=e.floating.style;i.setProperty("--radix-popper-available-width",`${n}px`),i.setProperty("--radix-popper-available-height",`${r}px`),i.setProperty("--radix-popper-anchor-width",`${o}px`),i.setProperty("--radix-popper-anchor-height",`${a}px`)}}),k&&ln({element:k,padding:m}),jn({arrowWidth:A,arrowHeight:I}),C&&cn({strategy:"referenceHidden",...H})]}),[Y,q]=Mn(U),G=S(R);B((()=>{z&&(null==G||G())}),[z,G]);const Z=null==(o=X.arrow)?void 0:o.x,J=null==(a=X.arrow)?void 0:a.y,Q=0!==(null==(i=X.arrow)?void 0:i.centerOffset),[ee,te]=e.useState();return B((()=>{P&&te(window.getComputedStyle(P).zIndex)}),[P]),v.jsx("div",{ref:$.setFloating,"data-radix-popper-content-wrapper":"",style:{...V,transform:z?V.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ee,"--radix-popper-transform-origin":[null==(s=X.transformOrigin)?void 0:s.x,null==(c=X.transformOrigin)?void 0:c.y].join(" "),...(null==(l=X.hide)?void 0:l.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:t.dir,children:v.jsx(En,{scope:u,placedSide:Y,onArrowChange:N,arrowX:Z,arrowY:J,shouldHideArrow:Q,children:v.jsx(T.div,{"data-side":Y,"data-align":q,..._,ref:M,style:{..._.style,animation:z?void 0:"none"}})})})}));_n.displayName=Cn;var Dn="PopperArrow",Tn={top:"bottom",right:"left",bottom:"top",left:"right"},Pn=e.forwardRef((function(e,t){const{__scopePopper:n,...r}=e,o=Rn(Dn,n),a=Tn[o.placedSide];return v.jsx("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:v.jsx(dn,{...r,ref:t,style:{...r.style,display:"block"}})})}));function Sn(e){return null!==e}Pn.displayName=Dn;var jn=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o;const{placement:a,rects:i,middlewareData:s}=t,c=0!==(null==(n=s.arrow)?void 0:n.centerOffset),l=c?0:e.arrowWidth,u=c?0:e.arrowHeight,[d,f]=Mn(a),p={start:"0%",center:"50%",end:"100%"}[f],v=((null==(r=s.arrow)?void 0:r.x)??0)+l/2,h=((null==(o=s.arrow)?void 0:o.y)??0)+u/2;let m="",g="";return"bottom"===d?(m=c?p:`${v}px`,g=-u+"px"):"top"===d?(m=c?p:`${v}px`,g=`${i.floating.height+u}px`):"right"===d?(m=-u+"px",g=c?p:`${h}px`):"left"===d&&(m=`${i.floating.width+u}px`,g=c?p:`${h}px`),{data:{x:m,y:g}}}});function Mn(e){const[t,n="center"]=e.split("-");return[t,n]}var kn=yn,Nn=bn,On=_n,An=Pn,[In,Ln]=_("Tooltip",[mn]),Fn=mn(),Kn="TooltipProvider",Wn=700,Bn="tooltip.open",[Hn,$n]=In(Kn),Vn=t=>{const{__scopeTooltip:n,delayDuration:r=Wn,skipDelayDuration:o=300,disableHoverableContent:a=!1,children:i}=t,[s,c]=e.useState(!0),l=e.useRef(!1),u=e.useRef(0);return e.useEffect((()=>{const e=u.current;return()=>window.clearTimeout(e)}),[]),v.jsx(Hn,{scope:n,isOpenDelayed:s,delayDuration:r,onOpen:e.useCallback((()=>{window.clearTimeout(u.current),c(!1)}),[]),onClose:e.useCallback((()=>{window.clearTimeout(u.current),u.current=window.setTimeout((()=>c(!0)),o)}),[o]),isPointerInTransitRef:l,onPointerInTransitChange:e.useCallback((e=>{l.current=e}),[]),disableHoverableContent:a,children:i})};Vn.displayName=Kn;var Un="Tooltip",[zn,Xn]=In(Un),Yn=t=>{const{__scopeTooltip:n,children:r,open:o,defaultOpen:a=!1,onOpenChange:i,disableHoverableContent:s,delayDuration:c}=t,l=$n(Un,t.__scopeTooltip),u=Fn(n),[d,f]=e.useState(null),p=Ie(),h=e.useRef(0),m=s??l.disableHoverableContent,g=c??l.delayDuration,w=e.useRef(!1),[y=!1,x]=U({prop:o,defaultProp:a,onChange:e=>{e?(l.onOpen(),document.dispatchEvent(new CustomEvent(Bn))):l.onClose(),null==i||i(e)}}),b=e.useMemo((()=>y?w.current?"delayed-open":"instant-open":"closed"),[y]),C=e.useCallback((()=>{window.clearTimeout(h.current),h.current=0,w.current=!1,x(!0)}),[x]),E=e.useCallback((()=>{window.clearTimeout(h.current),h.current=0,x(!1)}),[x]),R=e.useCallback((()=>{window.clearTimeout(h.current),h.current=window.setTimeout((()=>{w.current=!0,x(!0),h.current=0}),g)}),[g,x]);return e.useEffect((()=>()=>{h.current&&(window.clearTimeout(h.current),h.current=0)}),[]),v.jsx(kn,{...u,children:v.jsx(zn,{scope:n,contentId:p,open:y,stateAttribute:b,trigger:d,onTriggerChange:f,onTriggerEnter:e.useCallback((()=>{l.isOpenDelayed?R():C()}),[l.isOpenDelayed,R,C]),onTriggerLeave:e.useCallback((()=>{m?E():(window.clearTimeout(h.current),h.current=0)}),[E,m]),onOpen:C,onClose:E,disableHoverableContent:m,children:r})})};Yn.displayName=Un;var qn="TooltipTrigger",Gn=e.forwardRef(((t,n)=>{const{__scopeTooltip:r,...o}=t,a=Xn(qn,r),i=$n(qn,r),s=Fn(r),c=g(n,e.useRef(null),a.onTriggerChange),l=e.useRef(!1),u=e.useRef(!1),d=e.useCallback((()=>l.current=!1),[]);return e.useEffect((()=>()=>document.removeEventListener("pointerup",d)),[d]),v.jsx(Nn,{asChild:!0,...s,children:v.jsx(T.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...o,ref:c,onPointerMove:h(t.onPointerMove,(e=>{"touch"!==e.pointerType&&(u.current||i.isPointerInTransitRef.current||(a.onTriggerEnter(),u.current=!0))})),onPointerLeave:h(t.onPointerLeave,(()=>{a.onTriggerLeave(),u.current=!1})),onPointerDown:h(t.onPointerDown,(()=>{l.current=!0,document.addEventListener("pointerup",d,{once:!0})})),onFocus:h(t.onFocus,(()=>{l.current||a.onOpen()})),onBlur:h(t.onBlur,a.onClose),onClick:h(t.onClick,a.onClose)})})}));Gn.displayName=qn;var[Zn,Jn]=In("TooltipPortal",{forceMount:void 0}),Qn="TooltipContent",er=e.forwardRef(((e,t)=>{const n=Jn(Qn,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...a}=e,i=Xn(Qn,e.__scopeTooltip);return v.jsx($,{present:r||i.open,children:i.disableHoverableContent?v.jsx(or,{side:o,...a,ref:t}):v.jsx(tr,{side:o,...a,ref:t})})})),tr=e.forwardRef(((t,n)=>{const r=Xn(Qn,t.__scopeTooltip),o=$n(Qn,t.__scopeTooltip),a=e.useRef(null),i=g(n,a),[s,c]=e.useState(null),{trigger:l,onClose:u}=r,d=a.current,{onPointerInTransitChange:f}=o,p=e.useCallback((()=>{c(null),f(!1)}),[f]),h=e.useCallback(((e,t)=>{const n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,function(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(n,r,o,a)){case a:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}(r,n.getBoundingClientRect())),a=function(e){const t=e.slice();return t.sort(((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0)),function(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const n=e[r];for(;t.length>=2;){const e=t[t.length-1],r=t[t.length-2];if(!((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x)))break;t.pop()}t.push(n)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const t=e[r];for(;n.length>=2;){const e=n[n.length-1],r=n[n.length-2];if(!((e.x-r.x)*(t.y-r.y)>=(e.y-r.y)*(t.x-r.x)))break;n.pop()}n.push(t)}return n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}(t)}([...o,...function(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())]);c(a),f(!0)}),[f]);return e.useEffect((()=>()=>p()),[p]),e.useEffect((()=>{if(l&&d){const e=e=>h(e,d),t=e=>h(e,l);return l.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{l.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}}),[l,d,h,p]),e.useEffect((()=>{if(s){const e=e=>{const t=e.target,n={x:e.clientX,y:e.clientY},r=(null==l?void 0:l.contains(t))||(null==d?void 0:d.contains(t)),o=!function(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const e=t[a].x,s=t[a].y,c=t[i].x,l=t[i].y;s>r!=l>r&&n<(c-e)*(r-s)/(l-s)+e&&(o=!o)}return o}(n,s);r?p():o&&(p(),u())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}}),[l,d,s,u,p]),v.jsx(or,{...t,ref:i})})),[nr,rr]=In(Un,{isInside:!1}),or=e.forwardRef(((t,n)=>{const{__scopeTooltip:r,children:o,"aria-label":a,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=t,l=Xn(Qn,r),u=Fn(r),{onClose:d}=l;return e.useEffect((()=>(document.addEventListener(Bn,d),()=>document.removeEventListener(Bn,d))),[d]),e.useEffect((()=>{if(l.trigger){const e=e=>{const t=e.target;(null==t?void 0:t.contains(l.trigger))&&d()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}}),[l.trigger,d]),v.jsx(A,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:d,children:v.jsxs(On,{"data-state":l.stateAttribute,...u,...c,ref:n,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[v.jsx(b,{children:o}),v.jsx(nr,{scope:r,isInside:!0,children:v.jsx(X,{id:l.contentId,role:"tooltip",children:a||o})})]})})}));er.displayName=Qn;var ar="TooltipArrow";e.forwardRef(((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Fn(n);return rr(ar,n).isInside?null:v.jsx(An,{...o,...r,ref:t})})).displayName=ar;var ir=Vn,sr=Yn,cr=Gn,lr=er,ur="Avatar",[dr,fr]=_(ur),[pr,vr]=dr(ur),hr=e.forwardRef(((t,n)=>{const{__scopeAvatar:r,...o}=t,[a,i]=e.useState("idle");return v.jsx(pr,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:v.jsx(T.span,{...o,ref:n})})}));hr.displayName=ur;var mr="AvatarImage",gr=e.forwardRef(((t,n)=>{const{__scopeAvatar:r,src:o,onLoadingStatusChange:a=()=>{},...i}=t,s=vr(mr,r),c=function(t,n){const[r,o]=e.useState("idle");return B((()=>{if(!t)return void o("error");let e=!0;const r=new window.Image,a=t=>()=>{e&&o(t)};return o("loading"),r.onload=a("loaded"),r.onerror=a("error"),r.src=t,n&&(r.referrerPolicy=n),()=>{e=!1}}),[t,n]),r}(o,i.referrerPolicy),l=S((e=>{a(e),s.onImageLoadingStatusChange(e)}));return B((()=>{"idle"!==c&&l(c)}),[c,l]),"loaded"===c?v.jsx(T.img,{...i,ref:n,src:o}):null}));gr.displayName=mr;var wr="AvatarFallback",yr=e.forwardRef(((t,n)=>{const{__scopeAvatar:r,delayMs:o,...a}=t,i=vr(wr,r),[s,c]=e.useState(void 0===o);return e.useEffect((()=>{if(void 0!==o){const e=window.setTimeout((()=>c(!0)),o);return()=>window.clearTimeout(e)}}),[o]),s&&"loaded"!==i.imageLoadingStatus?v.jsx(T.span,{...a,ref:n}):null}));yr.displayName=wr;var xr=hr,br=gr,Cr=yr,Er=e.createContext(void 0);function Rr(t){const n=e.useContext(Er);return t||n||"ltr"}var _r=0;function Dr(){e.useEffect((()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Tr()),document.body.insertAdjacentElement("beforeend",e[1]??Tr()),_r++,()=>{1===_r&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),_r--}}),[])}function Tr(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Pr="focusScope.autoFocusOnMount",Sr="focusScope.autoFocusOnUnmount",jr={bubbles:!1,cancelable:!0},Mr=e.forwardRef(((t,n)=>{const{loop:r=!1,trapped:o=!1,onMountAutoFocus:a,onUnmountAutoFocus:i,...s}=t,[c,l]=e.useState(null),u=S(a),d=S(i),f=e.useRef(null),p=g(n,(e=>l(e))),h=e.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;e.useEffect((()=>{if(o){let e=function(e){if(h.paused||!c)return;const t=e.target;c.contains(t)?f.current=t:Ar(f.current,{select:!0})},t=function(e){if(h.paused||!c)return;const t=e.relatedTarget;null!==t&&(c.contains(t)||Ar(f.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&Ar(c)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return c&&r.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}}),[o,c,h.paused]),e.useEffect((()=>{if(c){Ir.add(h);const t=document.activeElement;if(!c.contains(t)){const n=new CustomEvent(Pr,jr);c.addEventListener(Pr,u),c.dispatchEvent(n),n.defaultPrevented||(!function(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(Ar(r,{select:t}),document.activeElement!==n)return}((e=kr(c),e.filter((e=>"A"!==e.tagName))),{select:!0}),document.activeElement===t&&Ar(c))}return()=>{c.removeEventListener(Pr,u),setTimeout((()=>{const e=new CustomEvent(Sr,jr);c.addEventListener(Sr,d),c.dispatchEvent(e),e.defaultPrevented||Ar(t??document.body,{select:!0}),c.removeEventListener(Sr,d),Ir.remove(h)}),0)}}var e}),[c,u,d,h]);const m=e.useCallback((e=>{if(!r&&!o)return;if(h.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){const t=e.currentTarget,[o,a]=function(e){const t=kr(e),n=Nr(t,e),r=Nr(t.reverse(),e);return[n,r]}(t);o&&a?e.shiftKey||n!==a?e.shiftKey&&n===o&&(e.preventDefault(),r&&Ar(a,{select:!0})):(e.preventDefault(),r&&Ar(o,{select:!0})):n===t&&e.preventDefault()}}),[r,o,h.paused]);return v.jsx(T.div,{tabIndex:-1,...s,ref:p,onKeyDown:m})}));function kr(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Nr(e,t){for(const n of e)if(!Or(n,{upTo:t}))return n}function Or(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function Ar(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}Mr.displayName="FocusScope";var Ir=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null==n||n.pause()),e=Lr(e,t),e.unshift(t)},remove(t){var n;e=Lr(e,t),null==(n=e[0])||n.resume()}}}();function Lr(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}function Fr(...t){const n=t[0];if(1===t.length)return n;const r=()=>{const r=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const o=r.reduce(((e,{useScope:n,scopeName:r})=>({...e,...n(t)[`__scope${r}`]})),{});return e.useMemo((()=>({[`__scope${n.scopeName}`]:o})),[o])}};return r.scopeName=n.scopeName,r}var Kr="rovingFocusGroup.onEntryFocus",Wr={bubbles:!1,cancelable:!0},Br="RovingFocusGroup",[Hr,$r,Vr]=R(Br),[Ur,zr]=function(t,n=[]){let r=[];const o=()=>{const n=r.map((t=>e.createContext(t)));return function(r){const o=(null==r?void 0:r[t])||n;return e.useMemo((()=>({[`__scope${t}`]:{...r,[t]:o}})),[r,o])}};return o.scopeName=t,[function(n,o){const a=e.createContext(o),i=r.length;function s(n){const{scope:r,children:o,...s}=n,c=(null==r?void 0:r[t][i])||a,l=e.useMemo((()=>s),Object.values(s));return v.jsx(c.Provider,{value:l,children:o})}return r=[...r,o],s.displayName=n+"Provider",[s,function(r,s){const c=(null==s?void 0:s[t][i])||a,l=e.useContext(c);if(l)return l;if(void 0!==o)return o;throw new Error(`\`${r}\` must be used within \`${n}\``)}]},Fr(o,...n)]}(Br,[Vr]),[Xr,Yr]=Ur(Br),qr=e.forwardRef(((e,t)=>v.jsx(Hr.Provider,{scope:e.__scopeRovingFocusGroup,children:v.jsx(Hr.Slot,{scope:e.__scopeRovingFocusGroup,children:v.jsx(Gr,{...e,ref:t})})})));qr.displayName=Br;var Gr=e.forwardRef(((t,n)=>{const{__scopeRovingFocusGroup:r,orientation:o,loop:a=!1,dir:i,currentTabStopId:s,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:l,onEntryFocus:u,preventScrollOnEntryFocus:d=!1,...f}=t,p=e.useRef(null),m=g(n,p),w=Rr(i),[y=null,x]=U({prop:s,defaultProp:c,onChange:l}),[b,C]=e.useState(!1),E=S(u),R=$r(r),_=e.useRef(!1),[D,P]=e.useState(0);return e.useEffect((()=>{const e=p.current;if(e)return e.addEventListener(Kr,E),()=>e.removeEventListener(Kr,E)}),[E]),v.jsx(Xr,{scope:r,orientation:o,dir:w,loop:a,currentTabStopId:y,onItemFocus:e.useCallback((e=>x(e)),[x]),onItemShiftTab:e.useCallback((()=>C(!0)),[]),onFocusableItemAdd:e.useCallback((()=>P((e=>e+1))),[]),onFocusableItemRemove:e.useCallback((()=>P((e=>e-1))),[]),children:v.jsx(T.div,{tabIndex:b||0===D?-1:0,"data-orientation":o,...f,ref:m,style:{outline:"none",...t.style},onMouseDown:h(t.onMouseDown,(()=>{_.current=!0})),onFocus:h(t.onFocus,(e=>{const t=!_.current;if(e.target===e.currentTarget&&t&&!b){const t=new CustomEvent(Kr,Wr);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){const e=R().filter((e=>e.focusable));eo([e.find((e=>e.active)),e.find((e=>e.id===y)),...e].filter(Boolean).map((e=>e.ref.current)),d)}}_.current=!1})),onBlur:h(t.onBlur,(()=>C(!1)))})})})),Zr="RovingFocusGroupItem",Jr=e.forwardRef(((t,n)=>{const{__scopeRovingFocusGroup:r,focusable:o=!0,active:a=!1,tabStopId:i,...s}=t,c=Ie(),l=i||c,u=Yr(Zr,r),d=u.currentTabStopId===l,f=$r(r),{onFocusableItemAdd:p,onFocusableItemRemove:m}=u;return e.useEffect((()=>{if(o)return p(),()=>m()}),[o,p,m]),v.jsx(Hr.ItemSlot,{scope:r,id:l,focusable:o,active:a,children:v.jsx(T.span,{tabIndex:d?0:-1,"data-orientation":u.orientation,...s,ref:n,onMouseDown:h(t.onMouseDown,(e=>{o?u.onItemFocus(l):e.preventDefault()})),onFocus:h(t.onFocus,(()=>u.onItemFocus(l))),onKeyDown:h(t.onKeyDown,(e=>{if("Tab"===e.key&&e.shiftKey)return void u.onItemShiftTab();if(e.target!==e.currentTarget)return;const t=function(e,t,n){const r=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);return"vertical"===t&&["ArrowLeft","ArrowRight"].includes(r)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(r)?void 0:Qr[r]}(e,u.orientation,u.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=f().filter((e=>e.focusable)).map((e=>e.ref.current));if("last"===t)o.reverse();else if("prev"===t||"next"===t){"prev"===t&&o.reverse();const a=o.indexOf(e.currentTarget);o=u.loop?(r=a+1,(n=o).map(((e,t)=>n[(r+t)%n.length]))):o.slice(a+1)}setTimeout((()=>eo(o)))}var n,r}))})})}));Jr.displayName=Zr;var Qr={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function eo(e,t=!1){const n=document.activeElement;for(const r of e){if(r===n)return;if(r.focus({preventScroll:t}),document.activeElement!==n)return}}var to=qr,no=Jr,ro=new WeakMap,oo=new WeakMap,ao={},io=0,so=function(e){return e&&(e.host||so(e.parentNode))},co=function(e,t,n,r){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=so(t);return n&&e.contains(n)?n:null})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);ao[n]||(ao[n]=new WeakMap);var a=ao[n],i=[],s=new Set,c=new Set(o),l=function(e){e&&!s.has(e)&&(s.add(e),l(e.parentNode))};o.forEach(l);var u=function(e){e&&!c.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(s.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,c=(ro.get(e)||0)+1,l=(a.get(e)||0)+1;ro.set(e,c),a.set(e,l),i.push(e),1===c&&o&&oo.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){}}))};return u(t),s.clear(),io++,function(){i.forEach((function(e){var t=ro.get(e)-1,o=a.get(e)-1;ro.set(e,t),a.set(e,o),t||(oo.has(e)||e.removeAttribute(r),oo.delete(e)),o||e.removeAttribute(n)})),--io||(ro=new WeakMap,ro=new WeakMap,oo=new WeakMap,ao={})}},lo=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body}(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),co(r,o,n,"aria-hidden")):function(){return null}},uo=function(){return uo=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},uo.apply(this,arguments)};function fo(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}"function"==typeof SuppressedError&&SuppressedError;var po="right-scroll-bar-position",vo="width-before-scroll-bar";function ho(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var mo="undefined"!=typeof window?e.useLayoutEffect:e.useEffect,go=new WeakMap;function wo(t,n){var r,o,a,i=(r=null,o=function(e){return t.forEach((function(t){return ho(t,e)}))},(a=e.useState((function(){return{value:r,callback:o,facade:{get current(){return a.value},set current(e){var t=a.value;t!==e&&(a.value=e,a.callback(e,t))}}}}))[0]).callback=o,a.facade);return mo((function(){var e=go.get(i);if(e){var n=new Set(e),r=new Set(t),o=i.current;n.forEach((function(e){r.has(e)||ho(e,null)})),r.forEach((function(e){n.has(e)||ho(e,o)}))}go.set(i,t)}),[t]),i}function yo(e){return e}var xo=function(t){var n=t.sideCar,r=fo(t,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=n.read();if(!o)throw new Error("Sidecar medium not found");return e.createElement(o,uo({},r))};xo.isSideCarExport=!0;var bo=function(e){void 0===e&&(e={});var t=function(e,t){void 0===t&&(t=yo);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}}}(null);return t.options=uo({async:!0,ssr:!1},e),t}(),Co=function(){},Eo=e.forwardRef((function(t,n){var r=e.useRef(null),o=e.useState({onScrollCapture:Co,onWheelCapture:Co,onTouchMoveCapture:Co}),a=o[0],i=o[1],s=t.forwardProps,c=t.children,l=t.className,u=t.removeScrollBar,d=t.enabled,f=t.shards,p=t.sideCar,v=t.noIsolation,h=t.inert,m=t.allowPinchZoom,g=t.as,w=void 0===g?"div":g,y=t.gapMode,x=fo(t,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),b=p,C=wo([r,n]),E=uo(uo({},x),a);return e.createElement(e.Fragment,null,d&&e.createElement(b,{sideCar:bo,removeScrollBar:u,shards:f,noIsolation:v,inert:h,setCallbacks:i,allowPinchZoom:!!m,lockRef:r,gapMode:y}),s?e.cloneElement(e.Children.only(c),uo(uo({},E),{ref:C})):e.createElement(w,uo({},E,{className:l,ref:C}),c))}));Eo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},Eo.classNames={fullWidth:vo,zeroRight:po};function Ro(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=function(){if("undefined"!=typeof __webpack_nonce__)return __webpack_nonce__}();return t&&e.setAttribute("nonce",t),e}var _o=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=Ro())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Do=function(){var t,n=(t=_o(),function(n,r){e.useEffect((function(){return t.add(n),function(){t.remove()}}),[n&&r])});return function(e){var t=e.styles,r=e.dynamic;return n(t,r),null}},To={left:0,top:0,right:0,gap:0},Po=function(e){return parseInt(e||"",10)||0},So=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return To;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[Po(n),Po(r),Po(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},jo=Do(),Mo="data-scroll-locked",ko=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,s=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(s,"px ").concat(r,";\n  }\n  body[").concat(Mo,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(s,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(s,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(po," {\n    right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(vo," {\n    margin-right: ").concat(s,"px ").concat(r,";\n  }\n  \n  .").concat(po," .").concat(po," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(vo," .").concat(vo," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(Mo,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(s,"px;\n  }\n")},No=function(){var e=parseInt(document.body.getAttribute(Mo)||"0",10);return isFinite(e)?e:0},Oo=function(t){var n=t.noRelative,r=t.noImportant,o=t.gapMode,a=void 0===o?"margin":o;e.useEffect((function(){return document.body.setAttribute(Mo,(No()+1).toString()),function(){var e=No()-1;e<=0?document.body.removeAttribute(Mo):document.body.setAttribute(Mo,e.toString())}}),[]);var i=e.useMemo((function(){return So(a)}),[a]);return e.createElement(jo,{styles:ko(i,!n,a,r?"":"!important")})},Ao=!1;if("undefined"!=typeof window)try{var Io=Object.defineProperty({},"passive",{get:function(){return Ao=!0,!0}});window.addEventListener("test",Io,Io),window.removeEventListener("test",Io,Io)}catch(zu){Ao=!1}var Lo=!!Ao&&{passive:!1},Fo=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},Ko=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Wo(e,r)){var o=Bo(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Wo=function(e,t){return"v"===e?function(e){return Fo(e,"overflowY")}(t):function(e){return Fo(e,"overflowX")}(t)},Bo=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},Ho=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},$o=function(e){return[e.deltaX,e.deltaY]},Vo=function(e){return e&&"current"in e?e.current:e},Uo=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},zo=0,Xo=[];function Yo(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const qo=(Go=function(t){var n=e.useRef([]),r=e.useRef([0,0]),o=e.useRef(),a=e.useState(zo++)[0],i=e.useState(Do)[0],s=e.useRef(t);e.useEffect((function(){s.current=t}),[t]),e.useEffect((function(){if(t.inert){document.body.classList.add("block-interactivity-".concat(a));var e=function(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([t.lockRef.current],(t.shards||[]).map(Vo),!0).filter(Boolean);return e.forEach((function(e){return e.classList.add("allow-interactivity-".concat(a))})),function(){document.body.classList.remove("block-interactivity-".concat(a)),e.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(a))}))}}}),[t.inert,t.lockRef.current,t.shards]);var c=e.useCallback((function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!s.current.allowPinchZoom;var n,a=Ho(e),i=r.current,c="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],u=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=Ko(d,u);if(!f)return!0;if(f?n=d:(n="v"===d?"h":"v",f=Ko(d,u)),!f)return!1;if(!o.current&&"changedTouches"in e&&(c||l)&&(o.current=n),!n)return!0;var p=o.current||n;return function(e,t,n,r,o){var a=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),i=a*r,s=n.target,c=t.contains(s),l=!1,u=i>0,d=0,f=0;do{var p=Bo(e,s),v=p[0],h=p[1]-p[2]-a*v;(v||h)&&Wo(e,s)&&(d+=h,f+=v),s=s instanceof ShadowRoot?s.host:s.parentNode}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return u&&(Math.abs(d)<1||!o)?l=!0:u||!(Math.abs(f)<1)&&o||(l=!0),l}(p,t,e,"h"===p?c:l,!0)}),[]),l=e.useCallback((function(e){var t=e;if(Xo.length&&Xo[Xo.length-1]===i){var r="deltaY"in t?$o(t):Ho(t),o=n.current.filter((function(e){return e.name===t.type&&(e.target===t.target||t.target===e.shadowParent)&&(n=e.delta,o=r,n[0]===o[0]&&n[1]===o[1]);var n,o}))[0];if(o&&o.should)t.cancelable&&t.preventDefault();else if(!o){var a=(s.current.shards||[]).map(Vo).filter(Boolean).filter((function(e){return e.contains(t.target)}));(a.length>0?c(t,a[0]):!s.current.noIsolation)&&t.cancelable&&t.preventDefault()}}}),[]),u=e.useCallback((function(e,t,r,o){var a={name:e,delta:t,target:r,should:o,shadowParent:Yo(r)};n.current.push(a),setTimeout((function(){n.current=n.current.filter((function(e){return e!==a}))}),1)}),[]),d=e.useCallback((function(e){r.current=Ho(e),o.current=void 0}),[]),f=e.useCallback((function(e){u(e.type,$o(e),e.target,c(e,t.lockRef.current))}),[]),p=e.useCallback((function(e){u(e.type,Ho(e),e.target,c(e,t.lockRef.current))}),[]);e.useEffect((function(){return Xo.push(i),t.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,Lo),document.addEventListener("touchmove",l,Lo),document.addEventListener("touchstart",d,Lo),function(){Xo=Xo.filter((function(e){return e!==i})),document.removeEventListener("wheel",l,Lo),document.removeEventListener("touchmove",l,Lo),document.removeEventListener("touchstart",d,Lo)}}),[]);var v=t.removeScrollBar,h=t.inert;return e.createElement(e.Fragment,null,h?e.createElement(i,{styles:Uo(a)}):null,v?e.createElement(Oo,{gapMode:t.gapMode}):null)},bo.useMedium(Go),xo);var Go,Zo=e.forwardRef((function(t,n){return e.createElement(Eo,uo({},t,{ref:n,sideCar:qo}))}));Zo.classNames=Eo.classNames;var Jo=["Enter"," "],Qo=["ArrowUp","PageDown","End"],ea=["ArrowDown","PageUp","Home",...Qo],ta={ltr:[...Jo,"ArrowRight"],rtl:[...Jo,"ArrowLeft"]},na={ltr:["ArrowLeft"],rtl:["ArrowRight"]},ra="Menu",[oa,aa,ia]=R(ra),[sa,ca]=_(ra,[ia,mn,zr]),la=mn(),ua=zr(),[da,fa]=sa(ra),[pa,va]=sa(ra),ha=t=>{const{__scopeMenu:n,open:r=!1,children:o,dir:a,onOpenChange:i,modal:s=!0}=t,c=la(n),[l,u]=e.useState(null),d=e.useRef(!1),f=S(i),p=Rr(a);return e.useEffect((()=>{const e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}}),[]),v.jsx(kn,{...c,children:v.jsx(da,{scope:n,open:r,onOpenChange:f,content:l,onContentChange:u,children:v.jsx(pa,{scope:n,onClose:e.useCallback((()=>f(!1)),[f]),isUsingKeyboardRef:d,dir:p,modal:s,children:o})})})};ha.displayName=ra;var ma=e.forwardRef(((e,t)=>{const{__scopeMenu:n,...r}=e,o=la(n);return v.jsx(Nn,{...o,...r,ref:t})}));ma.displayName="MenuAnchor";var ga="MenuPortal",[wa,ya]=sa(ga,{forceMount:void 0}),xa=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=fa(ga,t);return v.jsx(wa,{scope:t,forceMount:n,children:v.jsx($,{present:n||a.open,children:v.jsx(H,{asChild:!0,container:o,children:r})})})};xa.displayName=ga;var ba="MenuContent",[Ca,Ea]=sa(ba),Ra=e.forwardRef(((e,t)=>{const n=ya(ba,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=fa(ba,e.__scopeMenu),i=va(ba,e.__scopeMenu);return v.jsx(oa.Provider,{scope:e.__scopeMenu,children:v.jsx($,{present:r||a.open,children:v.jsx(oa.Slot,{scope:e.__scopeMenu,children:i.modal?v.jsx(_a,{...o,ref:t}):v.jsx(Da,{...o,ref:t})})})})})),_a=e.forwardRef(((t,n)=>{const r=fa(ba,t.__scopeMenu),o=e.useRef(null),a=g(n,o);return e.useEffect((()=>{const e=o.current;if(e)return lo(e)}),[]),v.jsx(Ta,{...t,ref:a,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:h(t.onFocusOutside,(e=>e.preventDefault()),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})})),Da=e.forwardRef(((e,t)=>{const n=fa(ba,e.__scopeMenu);return v.jsx(Ta,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})})),Ta=e.forwardRef(((t,n)=>{const{__scopeMenu:r,loop:o=!1,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:l,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,onDismiss:m,disableOutsideScroll:w,...x}=t,b=fa(ba,r),C=va(ba,r),E=la(r),R=ua(r),_=aa(r),[D,T]=e.useState(null),P=e.useRef(null),S=g(n,P,b.onContentChange),j=e.useRef(0),M=e.useRef(""),k=e.useRef(0),N=e.useRef(null),O=e.useRef("right"),I=e.useRef(0),L=w?Zo:e.Fragment,F=w?{as:y,allowPinchZoom:!0}:void 0,K=e=>{var t,n;const r=M.current+e,o=_().filter((e=>!e.disabled)),a=document.activeElement,i=null==(t=o.find((e=>e.ref.current===a)))?void 0:t.textValue,s=function(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0])),o=r?t[0]:t,a=n?e.indexOf(n):-1;let i=(s=e,c=Math.max(a,0),s.map(((e,t)=>s[(c+t)%s.length])));var s,c;1===o.length&&(i=i.filter((e=>e!==n)));const l=i.find((e=>e.toLowerCase().startsWith(o.toLowerCase())));return l!==n?l:void 0}(o.map((e=>e.textValue)),r,i),c=null==(n=o.find((e=>e.textValue===s)))?void 0:n.ref.current;!function e(t){M.current=t,window.clearTimeout(j.current),""!==t&&(j.current=window.setTimeout((()=>e("")),1e3))}(r),c&&setTimeout((()=>c.focus()))};e.useEffect((()=>()=>window.clearTimeout(j.current)),[]),Dr();const W=e.useCallback((e=>{var t,n;return O.current===(null==(t=N.current)?void 0:t.side)&&function(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return function(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const e=t[a].x,s=t[a].y,c=t[i].x,l=t[i].y;s>r!=l>r&&n<(c-e)*(r-s)/(l-s)+e&&(o=!o)}return o}(n,t)}(e,null==(n=N.current)?void 0:n.area)}),[]);return v.jsx(Ca,{scope:r,searchRef:M,onItemEnter:e.useCallback((e=>{W(e)&&e.preventDefault()}),[W]),onItemLeave:e.useCallback((e=>{var t;W(e)||(null==(t=P.current)||t.focus(),T(null))}),[W]),onTriggerLeave:e.useCallback((e=>{W(e)&&e.preventDefault()}),[W]),pointerGraceTimerRef:k,onPointerGraceIntentChange:e.useCallback((e=>{N.current=e}),[]),children:v.jsx(L,{...F,children:v.jsx(Mr,{asChild:!0,trapped:a,onMountAutoFocus:h(i,(e=>{var t;e.preventDefault(),null==(t=P.current)||t.focus({preventScroll:!0})})),onUnmountAutoFocus:s,children:v.jsx(A,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:p,onDismiss:m,children:v.jsx(to,{asChild:!0,...R,dir:C.dir,orientation:"vertical",loop:o,currentTabStopId:D,onCurrentTabStopIdChange:T,onEntryFocus:h(l,(e=>{C.isUsingKeyboardRef.current||e.preventDefault()})),preventScrollOnEntryFocus:!0,children:v.jsx(On,{role:"menu","aria-orientation":"vertical","data-state":Qa(b.open),"data-radix-menu-content":"",dir:C.dir,...E,...x,ref:S,style:{outline:"none",...x.style},onKeyDown:h(x.onKeyDown,(e=>{const t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&K(e.key));const o=P.current;if(e.target!==o)return;if(!ea.includes(e.key))return;e.preventDefault();const a=_().filter((e=>!e.disabled)).map((e=>e.ref.current));Qo.includes(e.key)&&a.reverse(),function(e){const t=document.activeElement;for(const n of e){if(n===t)return;if(n.focus(),document.activeElement!==t)return}}(a)})),onBlur:h(t.onBlur,(e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(j.current),M.current="")})),onPointerMove:h(t.onPointerMove,ni((e=>{const t=e.target,n=I.current!==e.clientX;if(e.currentTarget.contains(t)&&n){const t=e.clientX>I.current?"right":"left";O.current=t,I.current=e.clientX}})))})})})})})})}));Ra.displayName=ba;var Pa=e.forwardRef(((e,t)=>{const{__scopeMenu:n,...r}=e;return v.jsx(T.div,{role:"group",...r,ref:t})}));Pa.displayName="MenuGroup";var Sa=e.forwardRef(((e,t)=>{const{__scopeMenu:n,...r}=e;return v.jsx(T.div,{...r,ref:t})}));Sa.displayName="MenuLabel";var ja="MenuItem",Ma="menu.itemSelect",ka=e.forwardRef(((t,n)=>{const{disabled:r=!1,onSelect:o,...a}=t,i=e.useRef(null),s=va(ja,t.__scopeMenu),c=Ea(ja,t.__scopeMenu),l=g(n,i),u=e.useRef(!1);return v.jsx(Na,{...a,ref:l,disabled:r,onClick:h(t.onClick,(()=>{const e=i.current;if(!r&&e){const t=new CustomEvent(Ma,{bubbles:!0,cancelable:!0});e.addEventListener(Ma,(e=>null==o?void 0:o(e)),{once:!0}),P(e,t),t.defaultPrevented?u.current=!1:s.onClose()}})),onPointerDown:e=>{var n;null==(n=t.onPointerDown)||n.call(t,e),u.current=!0},onPointerUp:h(t.onPointerUp,(e=>{var t;u.current||null==(t=e.currentTarget)||t.click()})),onKeyDown:h(t.onKeyDown,(e=>{const t=""!==c.searchRef.current;r||t&&" "===e.key||Jo.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())}))})}));ka.displayName=ja;var Na=e.forwardRef(((t,n)=>{const{__scopeMenu:r,disabled:o=!1,textValue:a,...i}=t,s=Ea(ja,r),c=ua(r),l=e.useRef(null),u=g(n,l),[d,f]=e.useState(!1),[p,m]=e.useState("");return e.useEffect((()=>{const e=l.current;e&&m((e.textContent??"").trim())}),[i.children]),v.jsx(oa.ItemSlot,{scope:r,disabled:o,textValue:a??p,children:v.jsx(no,{asChild:!0,...c,focusable:!o,children:v.jsx(T.div,{role:"menuitem","data-highlighted":d?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...i,ref:u,onPointerMove:h(t.onPointerMove,ni((e=>{if(o)s.onItemLeave(e);else if(s.onItemEnter(e),!e.defaultPrevented){e.currentTarget.focus({preventScroll:!0})}}))),onPointerLeave:h(t.onPointerLeave,ni((e=>s.onItemLeave(e)))),onFocus:h(t.onFocus,(()=>f(!0))),onBlur:h(t.onBlur,(()=>f(!1)))})})})})),Oa=e.forwardRef(((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return v.jsx(Ha,{scope:e.__scopeMenu,checked:n,children:v.jsx(ka,{role:"menuitemcheckbox","aria-checked":ei(n)?"mixed":n,...o,ref:t,"data-state":ti(n),onSelect:h(o.onSelect,(()=>null==r?void 0:r(!!ei(n)||!n)),{checkForDefaultPrevented:!1})})})}));Oa.displayName="MenuCheckboxItem";var Aa="MenuRadioGroup",[Ia,La]=sa(Aa,{value:void 0,onValueChange:()=>{}}),Fa=e.forwardRef(((e,t)=>{const{value:n,onValueChange:r,...o}=e,a=S(r);return v.jsx(Ia,{scope:e.__scopeMenu,value:n,onValueChange:a,children:v.jsx(Pa,{...o,ref:t})})}));Fa.displayName=Aa;var Ka="MenuRadioItem",Wa=e.forwardRef(((e,t)=>{const{value:n,...r}=e,o=La(Ka,e.__scopeMenu),a=n===o.value;return v.jsx(Ha,{scope:e.__scopeMenu,checked:a,children:v.jsx(ka,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":ti(a),onSelect:h(r.onSelect,(()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)}),{checkForDefaultPrevented:!1})})})}));Wa.displayName=Ka;var Ba="MenuItemIndicator",[Ha,$a]=sa(Ba,{checked:!1}),Va=e.forwardRef(((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,a=$a(Ba,n);return v.jsx($,{present:r||ei(a.checked)||!0===a.checked,children:v.jsx(T.span,{...o,ref:t,"data-state":ti(a.checked)})})}));Va.displayName=Ba;var Ua=e.forwardRef(((e,t)=>{const{__scopeMenu:n,...r}=e;return v.jsx(T.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})}));Ua.displayName="MenuSeparator";var za=e.forwardRef(((e,t)=>{const{__scopeMenu:n,...r}=e,o=la(n);return v.jsx(An,{...o,...r,ref:t})}));za.displayName="MenuArrow";var[Xa,Ya]=sa("MenuSub"),qa="MenuSubTrigger",Ga=e.forwardRef(((t,n)=>{const r=fa(qa,t.__scopeMenu),o=va(qa,t.__scopeMenu),a=Ya(qa,t.__scopeMenu),i=Ea(qa,t.__scopeMenu),s=e.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:l}=i,u={__scopeMenu:t.__scopeMenu},d=e.useCallback((()=>{s.current&&window.clearTimeout(s.current),s.current=null}),[]);return e.useEffect((()=>d),[d]),e.useEffect((()=>{const e=c.current;return()=>{window.clearTimeout(e),l(null)}}),[c,l]),v.jsx(ma,{asChild:!0,...u,children:v.jsx(Na,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":a.contentId,"data-state":Qa(r.open),...t,ref:m(n,a.onTriggerChange),onClick:e=>{var n;null==(n=t.onClick)||n.call(t,e),t.disabled||e.defaultPrevented||(e.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:h(t.onPointerMove,ni((e=>{i.onItemEnter(e),e.defaultPrevented||t.disabled||r.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout((()=>{r.onOpenChange(!0),d()}),100))}))),onPointerLeave:h(t.onPointerLeave,ni((e=>{var t,n;d();const o=null==(t=r.content)?void 0:t.getBoundingClientRect();if(o){const t=null==(n=r.content)?void 0:n.dataset.side,a="right"===t,s=a?-5:5,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+s,y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout((()=>i.onPointerGraceIntentChange(null)),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}}))),onKeyDown:h(t.onKeyDown,(e=>{var n;const a=""!==i.searchRef.current;t.disabled||a&&" "===e.key||ta[o.dir].includes(e.key)&&(r.onOpenChange(!0),null==(n=r.content)||n.focus(),e.preventDefault())}))})})}));Ga.displayName=qa;var Za="MenuSubContent",Ja=e.forwardRef(((t,n)=>{const r=ya(ba,t.__scopeMenu),{forceMount:o=r.forceMount,...a}=t,i=fa(ba,t.__scopeMenu),s=va(ba,t.__scopeMenu),c=Ya(Za,t.__scopeMenu),l=e.useRef(null),u=g(n,l);return v.jsx(oa.Provider,{scope:t.__scopeMenu,children:v.jsx($,{present:o||i.open,children:v.jsx(oa.Slot,{scope:t.__scopeMenu,children:v.jsx(Ta,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:u,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null==(t=l.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:h(t.onFocusOutside,(e=>{e.target!==c.trigger&&i.onOpenChange(!1)})),onEscapeKeyDown:h(t.onEscapeKeyDown,(e=>{s.onClose(),e.preventDefault()})),onKeyDown:h(t.onKeyDown,(e=>{var t;const n=e.currentTarget.contains(e.target),r=na[s.dir].includes(e.key);n&&r&&(i.onOpenChange(!1),null==(t=c.trigger)||t.focus(),e.preventDefault())}))})})})})}));function Qa(e){return e?"open":"closed"}function ei(e){return"indeterminate"===e}function ti(e){return ei(e)?"indeterminate":e?"checked":"unchecked"}function ni(e){return t=>"mouse"===t.pointerType?e(t):void 0}Ja.displayName=Za;var ri=ha,oi=ma,ai=xa,ii=Ra,si=Pa,ci=Sa,li=ka,ui=Oa,di=Fa,fi=Wa,pi=Va,vi=Ua,hi=za,mi=Ga,gi=Ja,wi="DropdownMenu",[yi,xi]=_(wi,[ca]),bi=ca(),[Ci,Ei]=yi(wi),Ri=t=>{const{__scopeDropdownMenu:n,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:s,modal:c=!0}=t,l=bi(n),u=e.useRef(null),[d=!1,f]=U({prop:a,defaultProp:i,onChange:s});return v.jsx(Ci,{scope:n,triggerId:Ie(),triggerRef:u,contentId:Ie(),open:d,onOpenChange:f,onOpenToggle:e.useCallback((()=>f((e=>!e))),[f]),modal:c,children:v.jsx(ri,{...l,open:d,onOpenChange:f,dir:o,modal:c,children:r})})};Ri.displayName=wi;var _i="DropdownMenuTrigger",Di=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,a=Ei(_i,n),i=bi(n);return v.jsx(oi,{asChild:!0,...i,children:v.jsx(T.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:m(t,a.triggerRef),onPointerDown:h(e.onPointerDown,(e=>{r||0!==e.button||!1!==e.ctrlKey||(a.onOpenToggle(),a.open||e.preventDefault())})),onKeyDown:h(e.onKeyDown,(e=>{r||(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())}))})})}));Di.displayName=_i;var Ti=e=>{const{__scopeDropdownMenu:t,...n}=e,r=bi(t);return v.jsx(ai,{...r,...n})};Ti.displayName="DropdownMenuPortal";var Pi="DropdownMenuContent",Si=e.forwardRef(((t,n)=>{const{__scopeDropdownMenu:r,...o}=t,a=Ei(Pi,r),i=bi(r),s=e.useRef(!1);return v.jsx(ii,{id:a.contentId,"aria-labelledby":a.triggerId,...i,...o,ref:n,onCloseAutoFocus:h(t.onCloseAutoFocus,(e=>{var t;s.current||null==(t=a.triggerRef.current)||t.focus(),s.current=!1,e.preventDefault()})),onInteractOutside:h(t.onInteractOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;a.modal&&!r||(s.current=!0)})),style:{...t.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}));Si.displayName=Pi;var ji=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(si,{...o,...r,ref:t})}));ji.displayName="DropdownMenuGroup";var Mi=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(ci,{...o,...r,ref:t})}));Mi.displayName="DropdownMenuLabel";var ki=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(li,{...o,...r,ref:t})}));ki.displayName="DropdownMenuItem";var Ni=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(ui,{...o,...r,ref:t})}));Ni.displayName="DropdownMenuCheckboxItem";e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(di,{...o,...r,ref:t})})).displayName="DropdownMenuRadioGroup";var Oi=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(fi,{...o,...r,ref:t})}));Oi.displayName="DropdownMenuRadioItem";var Ai=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(pi,{...o,...r,ref:t})}));Ai.displayName="DropdownMenuItemIndicator";var Ii=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(vi,{...o,...r,ref:t})}));Ii.displayName="DropdownMenuSeparator";e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(hi,{...o,...r,ref:t})})).displayName="DropdownMenuArrow";var Li=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(mi,{...o,...r,ref:t})}));Li.displayName="DropdownMenuSubTrigger";var Fi=e.forwardRef(((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=bi(n);return v.jsx(gi,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}));Fi.displayName="DropdownMenuSubContent";var Ki=Ri,Wi=Di,Bi=Ti,Hi=Si,$i=ji,Vi=Mi,Ui=ki,zi=Ni,Xi=Oi,Yi=Ai,qi=Ii,Gi=Li,Zi=Fi,Ji="Popover",[Qi,es]=_(Ji,[mn]),ts=mn(),[ns,rs]=Qi(Ji),os=t=>{const{__scopePopover:n,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:s=!1}=t,c=ts(n),l=e.useRef(null),[u,d]=e.useState(!1),[f=!1,p]=U({prop:o,defaultProp:a,onChange:i});return v.jsx(kn,{...c,children:v.jsx(ns,{scope:n,contentId:Ie(),triggerRef:l,open:f,onOpenChange:p,onOpenToggle:e.useCallback((()=>p((e=>!e))),[p]),hasCustomAnchor:u,onCustomAnchorAdd:e.useCallback((()=>d(!0)),[]),onCustomAnchorRemove:e.useCallback((()=>d(!1)),[]),modal:s,children:r})})};os.displayName=Ji;var as="PopoverAnchor";e.forwardRef(((t,n)=>{const{__scopePopover:r,...o}=t,a=rs(as,r),i=ts(r),{onCustomAnchorAdd:s,onCustomAnchorRemove:c}=a;return e.useEffect((()=>(s(),()=>c())),[s,c]),v.jsx(Nn,{...i,...o,ref:n})})).displayName=as;var is="PopoverTrigger",ss=e.forwardRef(((e,t)=>{const{__scopePopover:n,...r}=e,o=rs(is,n),a=ts(n),i=g(t,o.triggerRef),s=v.jsx(T.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":ws(o.open),...r,ref:i,onClick:h(e.onClick,o.onOpenToggle)});return o.hasCustomAnchor?s:v.jsx(Nn,{asChild:!0,...a,children:s})}));ss.displayName=is;var cs="PopoverPortal",[ls,us]=Qi(cs,{forceMount:void 0}),ds=e=>{const{__scopePopover:t,forceMount:n,children:r,container:o}=e,a=rs(cs,t);return v.jsx(ls,{scope:t,forceMount:n,children:v.jsx($,{present:n||a.open,children:v.jsx(H,{asChild:!0,container:o,children:r})})})};ds.displayName=cs;var fs="PopoverContent",ps=e.forwardRef(((e,t)=>{const n=us(fs,e.__scopePopover),{forceMount:r=n.forceMount,...o}=e,a=rs(fs,e.__scopePopover);return v.jsx($,{present:r||a.open,children:a.modal?v.jsx(vs,{...o,ref:t}):v.jsx(hs,{...o,ref:t})})}));ps.displayName=fs;var vs=e.forwardRef(((t,n)=>{const r=rs(fs,t.__scopePopover),o=e.useRef(null),a=g(n,o),i=e.useRef(!1);return e.useEffect((()=>{const e=o.current;if(e)return lo(e)}),[]),v.jsx(Zo,{as:y,allowPinchZoom:!0,children:v.jsx(ms,{...t,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:h(t.onCloseAutoFocus,(e=>{var t;e.preventDefault(),i.current||null==(t=r.triggerRef.current)||t.focus()})),onPointerDownOutside:h(t.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;i.current=r}),{checkForDefaultPrevented:!1}),onFocusOutside:h(t.onFocusOutside,(e=>e.preventDefault()),{checkForDefaultPrevented:!1})})})})),hs=e.forwardRef(((t,n)=>{const r=rs(fs,t.__scopePopover),o=e.useRef(!1),a=e.useRef(!1);return v.jsx(ms,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{var n,i;null==(n=t.onCloseAutoFocus)||n.call(t,e),e.defaultPrevented||(o.current||null==(i=r.triggerRef.current)||i.focus(),e.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:e=>{var n,i;null==(n=t.onInteractOutside)||n.call(t,e),e.defaultPrevented||(o.current=!0,"pointerdown"===e.detail.originalEvent.type&&(a.current=!0));const s=e.target;(null==(i=r.triggerRef.current)?void 0:i.contains(s))&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&a.current&&e.preventDefault()}})})),ms=e.forwardRef(((e,t)=>{const{__scopePopover:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:i,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:l,onInteractOutside:u,...d}=e,f=rs(fs,n),p=ts(n);return Dr(),v.jsx(Mr,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:v.jsx(A,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:u,onEscapeKeyDown:s,onPointerDownOutside:c,onFocusOutside:l,onDismiss:()=>f.onOpenChange(!1),children:v.jsx(On,{"data-state":ws(f.open),role:"dialog",id:f.contentId,...p,...d,ref:t,style:{...d.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})})),gs="PopoverClose";e.forwardRef(((e,t)=>{const{__scopePopover:n,...r}=e,o=rs(gs,n);return v.jsx(T.button,{type:"button",...r,ref:t,onClick:h(e.onClick,(()=>o.onOpenChange(!1)))})})).displayName=gs;function ws(e){return e?"open":"closed"}e.forwardRef(((e,t)=>{const{__scopePopover:n,...r}=e,o=ts(n);return v.jsx(An,{...o,...r,ref:t})})).displayName="PopoverArrow";var ys=os,xs=ss,bs=ds,Cs=ps,Es=e.forwardRef(((e,t)=>v.jsx(T.label,{...e,ref:t,onMouseDown:t=>{var n;t.target.closest("button, input, select, textarea")||(null==(n=e.onMouseDown)||n.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}})));Es.displayName="Label";var Rs=Es;function _s(e,[t,n]){return Math.min(n,Math.max(t,e))}function Ds(t){const n=e.useRef({value:t,previous:t});return e.useMemo((()=>(n.current.value!==t&&(n.current.previous=n.current.value,n.current.value=t),n.current.previous)),[t])}var Ts=[" ","Enter","ArrowUp","ArrowDown"],Ps=[" ","Enter"],Ss="Select",[js,Ms,ks]=R(Ss),[Ns,Os]=_(Ss,[ks,mn]),As=mn(),[Is,Ls]=Ns(Ss),[Fs,Ks]=Ns(Ss),Ws=t=>{const{__scopeSelect:n,children:r,open:o,defaultOpen:a,onOpenChange:i,value:s,defaultValue:c,onValueChange:l,dir:u,name:d,autoComplete:f,disabled:p,required:h,form:m}=t,g=As(n),[w,y]=e.useState(null),[x,b]=e.useState(null),[C,E]=e.useState(!1),R=Rr(u),[_=!1,D]=U({prop:o,defaultProp:a,onChange:i}),[T,P]=U({prop:s,defaultProp:c,onChange:l}),S=e.useRef(null),j=!w||(m||!!w.closest("form")),[M,k]=e.useState(new Set),N=Array.from(M).map((e=>e.props.value)).join(";");return v.jsx(kn,{...g,children:v.jsxs(Is,{required:h,scope:n,trigger:w,onTriggerChange:y,valueNode:x,onValueNodeChange:b,valueNodeHasChildren:C,onValueNodeHasChildrenChange:E,contentId:Ie(),value:T,onValueChange:P,open:_,onOpenChange:D,dir:R,triggerPointerDownPosRef:S,disabled:p,children:[v.jsx(js.Provider,{scope:n,children:v.jsx(Fs,{scope:t.__scopeSelect,onNativeOptionAdd:e.useCallback((e=>{k((t=>new Set(t).add(e)))}),[]),onNativeOptionRemove:e.useCallback((e=>{k((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),children:r})}),j?v.jsxs(Dc,{"aria-hidden":!0,required:h,tabIndex:-1,name:d,autoComplete:f,value:T,onChange:e=>P(e.target.value),disabled:p,form:m,children:[void 0===T?v.jsx("option",{value:""}):null,Array.from(M)]},N):null]})})};Ws.displayName=Ss;var Bs="SelectTrigger",Hs=e.forwardRef(((t,n)=>{const{__scopeSelect:r,disabled:o=!1,...a}=t,i=As(r),s=Ls(Bs,r),c=s.disabled||o,l=g(n,s.onTriggerChange),u=Ms(r),d=e.useRef("touch"),[f,p,m]=Tc((e=>{const t=u().filter((e=>!e.disabled)),n=t.find((e=>e.value===s.value)),r=Pc(t,e,n);void 0!==r&&s.onValueChange(r.value)})),w=e=>{c||(s.onOpenChange(!0),m()),e&&(s.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return v.jsx(Nn,{asChild:!0,...i,children:v.jsx(T.button,{type:"button",role:"combobox","aria-controls":s.contentId,"aria-expanded":s.open,"aria-required":s.required,"aria-autocomplete":"none",dir:s.dir,"data-state":s.open?"open":"closed",disabled:c,"data-disabled":c?"":void 0,"data-placeholder":_c(s.value)?"":void 0,...a,ref:l,onClick:h(a.onClick,(e=>{e.currentTarget.focus(),"mouse"!==d.current&&w(e)})),onPointerDown:h(a.onPointerDown,(e=>{d.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(w(e),e.preventDefault())})),onKeyDown:h(a.onKeyDown,(e=>{const t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||p(e.key),t&&" "===e.key||Ts.includes(e.key)&&(w(),e.preventDefault())}))})})}));Hs.displayName=Bs;var $s="SelectValue",Vs=e.forwardRef(((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:a,placeholder:i="",...s}=e,c=Ls($s,n),{onValueNodeHasChildrenChange:l}=c,u=void 0!==a,d=g(t,c.onValueNodeChange);return B((()=>{l(u)}),[l,u]),v.jsx(T.span,{...s,ref:d,style:{pointerEvents:"none"},children:_c(c.value)?v.jsx(v.Fragment,{children:i}):a})}));Vs.displayName=$s;var Us=e.forwardRef(((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return v.jsx(T.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})}));Us.displayName="SelectIcon";var zs=e=>v.jsx(H,{asChild:!0,...e});zs.displayName="SelectPortal";var Xs="SelectContent",Ys=e.forwardRef(((t,r)=>{const o=Ls(Xs,t.__scopeSelect),[a,i]=e.useState();if(B((()=>{i(new DocumentFragment)}),[]),!o.open){const e=a;return e?n.createPortal(v.jsx(Gs,{scope:t.__scopeSelect,children:v.jsx(js.Slot,{scope:t.__scopeSelect,children:v.jsx("div",{children:t.children})})}),e):null}return v.jsx(Js,{...t,ref:r})}));Ys.displayName=Xs;var qs=10,[Gs,Zs]=Ns(Xs),Js=e.forwardRef(((t,n)=>{const{__scopeSelect:r,position:o="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:i,onPointerDownOutside:s,side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:p,collisionPadding:m,sticky:w,hideWhenDetached:x,avoidCollisions:b,...C}=t,E=Ls(Xs,r),[R,_]=e.useState(null),[D,T]=e.useState(null),P=g(n,(e=>_(e))),[S,j]=e.useState(null),[M,k]=e.useState(null),N=Ms(r),[O,I]=e.useState(!1),L=e.useRef(!1);e.useEffect((()=>{if(R)return lo(R)}),[R]),Dr();const F=e.useCallback((e=>{const[t,...n]=N().map((e=>e.ref.current)),[r]=n.slice(-1),o=document.activeElement;for(const a of e){if(a===o)return;if(null==a||a.scrollIntoView({block:"nearest"}),a===t&&D&&(D.scrollTop=0),a===r&&D&&(D.scrollTop=D.scrollHeight),null==a||a.focus(),document.activeElement!==o)return}}),[N,D]),K=e.useCallback((()=>F([S,R])),[F,S,R]);e.useEffect((()=>{O&&K()}),[O,K]);const{onOpenChange:W,triggerPointerDownPosRef:B}=E;e.useEffect((()=>{if(R){let e={x:0,y:0};const t=t=>{var n,r;e={x:Math.abs(Math.round(t.pageX)-((null==(n=B.current)?void 0:n.x)??0)),y:Math.abs(Math.round(t.pageY)-((null==(r=B.current)?void 0:r.y)??0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():R.contains(n.target)||W(!1),document.removeEventListener("pointermove",t),B.current=null};return null!==B.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[R,W,B]),e.useEffect((()=>{const e=()=>W(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[W]);const[H,$]=Tc((e=>{const t=N().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),r=Pc(t,e,n);r&&setTimeout((()=>r.ref.current.focus()))})),V=e.useCallback(((e,t,n)=>{const r=!L.current&&!n;(void 0!==E.value&&E.value===t||r)&&(j(e),r&&(L.current=!0))}),[E.value]),U=e.useCallback((()=>null==R?void 0:R.focus()),[R]),z=e.useCallback(((e,t,n)=>{const r=!L.current&&!n;(void 0!==E.value&&E.value===t||r)&&k(e)}),[E.value]),X="popper"===o?ec:Qs,Y=X===ec?{side:c,sideOffset:l,align:u,alignOffset:d,arrowPadding:f,collisionBoundary:p,collisionPadding:m,sticky:w,hideWhenDetached:x,avoidCollisions:b}:{};return v.jsx(Gs,{scope:r,content:R,viewport:D,onViewportChange:T,itemRefCallback:V,selectedItem:S,onItemLeave:U,itemTextRefCallback:z,focusSelectedItem:K,selectedItemText:M,position:o,isPositioned:O,searchRef:H,children:v.jsx(Zo,{as:y,allowPinchZoom:!0,children:v.jsx(Mr,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:h(a,(e=>{var t;null==(t=E.trigger)||t.focus({preventScroll:!0}),e.preventDefault()})),children:v.jsx(A,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:v.jsx(X,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...C,...Y,onPlaced:()=>I(!0),ref:P,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:h(C.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||$(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=N().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout((()=>F(t))),e.preventDefault()}}))})})})})})}));Js.displayName="SelectContentImpl";var Qs=e.forwardRef(((t,n)=>{const{__scopeSelect:r,onPlaced:o,...a}=t,i=Ls(Xs,r),s=Zs(Xs,r),[c,l]=e.useState(null),[u,d]=e.useState(null),f=g(n,(e=>d(e))),p=Ms(r),h=e.useRef(!1),m=e.useRef(!0),{viewport:w,selectedItem:y,selectedItemText:x,focusSelectedItem:b}=s,C=e.useCallback((()=>{if(i.trigger&&i.valueNode&&c&&u&&w&&y&&x){const e=i.trigger.getBoundingClientRect(),t=u.getBoundingClientRect(),n=i.valueNode.getBoundingClientRect(),r=x.getBoundingClientRect();if("rtl"!==i.dir){const o=r.left-t.left,a=n.left-o,i=e.left-a,s=e.width+i,l=Math.max(s,t.width),u=window.innerWidth-qs,d=_s(a,[qs,Math.max(qs,u-l)]);c.style.minWidth=s+"px",c.style.left=d+"px"}else{const o=t.right-r.right,a=window.innerWidth-n.right-o,i=window.innerWidth-e.right-a,s=e.width+i,l=Math.max(s,t.width),u=window.innerWidth-qs,d=_s(a,[qs,Math.max(qs,u-l)]);c.style.minWidth=s+"px",c.style.right=d+"px"}const a=p(),s=window.innerHeight-2*qs,l=w.scrollHeight,d=window.getComputedStyle(u),f=parseInt(d.borderTopWidth,10),v=parseInt(d.paddingTop,10),m=parseInt(d.borderBottomWidth,10),g=f+v+l+parseInt(d.paddingBottom,10)+m,b=Math.min(5*y.offsetHeight,g),C=window.getComputedStyle(w),E=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),_=e.top+e.height/2-qs,D=s-_,T=y.offsetHeight/2,P=f+v+(y.offsetTop+T),S=g-P;if(P<=_){const e=a.length>0&&y===a[a.length-1].ref.current;c.style.bottom="0px";const t=u.clientHeight-w.offsetTop-w.offsetHeight,n=P+Math.max(D,T+(e?R:0)+t+m);c.style.height=n+"px"}else{const e=a.length>0&&y===a[0].ref.current;c.style.top="0px";const t=Math.max(_,f+w.offsetTop+(e?E:0)+T)+S;c.style.height=t+"px",w.scrollTop=P-_+w.offsetTop}c.style.margin=`${qs}px 0`,c.style.minHeight=b+"px",c.style.maxHeight=s+"px",null==o||o(),requestAnimationFrame((()=>h.current=!0))}}),[p,i.trigger,i.valueNode,c,u,w,y,x,i.dir,o]);B((()=>C()),[C]);const[E,R]=e.useState();B((()=>{u&&R(window.getComputedStyle(u).zIndex)}),[u]);const _=e.useCallback((e=>{e&&!0===m.current&&(C(),null==b||b(),m.current=!1)}),[C,b]);return v.jsx(tc,{scope:r,contentWrapper:c,shouldExpandOnScrollRef:h,onScrollButtonChange:_,children:v.jsx("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:v.jsx(T.div,{...a,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})}));Qs.displayName="SelectItemAlignedPosition";var ec=e.forwardRef(((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=qs,...a}=e,i=As(n);return v.jsx(On,{...i,...a,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...a.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})}));ec.displayName="SelectPopperPosition";var[tc,nc]=Ns(Xs,{}),rc="SelectViewport",oc=e.forwardRef(((t,n)=>{const{__scopeSelect:r,nonce:o,...a}=t,i=Zs(rc,r),s=nc(rc,r),c=g(n,i.onViewportChange),l=e.useRef(0);return v.jsxs(v.Fragment,{children:[v.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),v.jsx(js.Slot,{scope:r,children:v.jsx(T.div,{"data-radix-select-viewport":"",role:"presentation",...a,ref:c,style:{position:"relative",flex:1,overflow:"hidden auto",...a.style},onScroll:h(a.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=s;if((null==r?void 0:r.current)&&n){const e=Math.abs(l.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*qs,o=parseFloat(n.style.minHeight),a=parseFloat(n.style.height),i=Math.max(o,a);if(i<r){const o=i+e,a=Math.min(r,o),s=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=s>0?s:0,n.style.justifyContent="flex-end")}}}l.current=t.scrollTop}))})})]})}));oc.displayName=rc;var ac="SelectGroup",[ic,sc]=Ns(ac);e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=Ie();return v.jsx(ic,{scope:n,id:o,children:v.jsx(T.div,{role:"group","aria-labelledby":o,...r,ref:t})})})).displayName=ac;var cc="SelectLabel",lc=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=sc(cc,n);return v.jsx(T.div,{id:o.id,...r,ref:t})}));lc.displayName=cc;var uc="SelectItem",[dc,fc]=Ns(uc),pc=e.forwardRef(((t,n)=>{const{__scopeSelect:r,value:o,disabled:a=!1,textValue:i,...s}=t,c=Ls(uc,r),l=Zs(uc,r),u=c.value===o,[d,f]=e.useState(i??""),[p,m]=e.useState(!1),w=g(n,(e=>{var t;return null==(t=l.itemRefCallback)?void 0:t.call(l,e,o,a)})),y=Ie(),x=e.useRef("touch"),b=()=>{a||(c.onValueChange(o),c.onOpenChange(!1))};if(""===o)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return v.jsx(dc,{scope:r,value:o,disabled:a,textId:y,isSelected:u,onItemTextChange:e.useCallback((e=>{f((t=>t||((null==e?void 0:e.textContent)??"").trim()))}),[]),children:v.jsx(js.ItemSlot,{scope:r,value:o,disabled:a,textValue:d,children:v.jsx(T.div,{role:"option","aria-labelledby":y,"data-highlighted":p?"":void 0,"aria-selected":u&&p,"data-state":u?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1,...s,ref:w,onFocus:h(s.onFocus,(()=>m(!0))),onBlur:h(s.onBlur,(()=>m(!1))),onClick:h(s.onClick,(()=>{"mouse"!==x.current&&b()})),onPointerUp:h(s.onPointerUp,(()=>{"mouse"===x.current&&b()})),onPointerDown:h(s.onPointerDown,(e=>{x.current=e.pointerType})),onPointerMove:h(s.onPointerMove,(e=>{var t;x.current=e.pointerType,a?null==(t=l.onItemLeave)||t.call(l):"mouse"===x.current&&e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:h(s.onPointerLeave,(e=>{var t;e.currentTarget===document.activeElement&&(null==(t=l.onItemLeave)||t.call(l))})),onKeyDown:h(s.onKeyDown,(e=>{var t;""!==(null==(t=l.searchRef)?void 0:t.current)&&" "===e.key||(Ps.includes(e.key)&&b()," "===e.key&&e.preventDefault())}))})})})}));pc.displayName=uc;var vc="SelectItemText",hc=e.forwardRef(((t,r)=>{const{__scopeSelect:o,className:a,style:i,...s}=t,c=Ls(vc,o),l=Zs(vc,o),u=fc(vc,o),d=Ks(vc,o),[f,p]=e.useState(null),h=g(r,(e=>p(e)),u.onItemTextChange,(e=>{var t;return null==(t=l.itemTextRefCallback)?void 0:t.call(l,e,u.value,u.disabled)})),m=null==f?void 0:f.textContent,w=e.useMemo((()=>v.jsx("option",{value:u.value,disabled:u.disabled,children:m},u.value)),[u.disabled,u.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:x}=d;return B((()=>(y(w),()=>x(w))),[y,x,w]),v.jsxs(v.Fragment,{children:[v.jsx(T.span,{id:u.textId,...s,ref:h}),u.isSelected&&c.valueNode&&!c.valueNodeHasChildren?n.createPortal(s.children,c.valueNode):null]})}));hc.displayName=vc;var mc="SelectItemIndicator",gc=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e;return fc(mc,n).isSelected?v.jsx(T.span,{"aria-hidden":!0,...r,ref:t}):null}));gc.displayName=mc;var wc="SelectScrollUpButton",yc=e.forwardRef(((t,n)=>{const r=Zs(wc,t.__scopeSelect),o=nc(wc,t.__scopeSelect),[a,i]=e.useState(!1),s=g(n,o.onScrollButtonChange);return B((()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollTop>0;i(e)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[r.viewport,r.isPositioned]),a?v.jsx(Cc,{...t,ref:s,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null}));yc.displayName=wc;var xc="SelectScrollDownButton",bc=e.forwardRef(((t,n)=>{const r=Zs(xc,t.__scopeSelect),o=nc(xc,t.__scopeSelect),[a,i]=e.useState(!1),s=g(n,o.onScrollButtonChange);return B((()=>{if(r.viewport&&r.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;i(n)};const t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[r.viewport,r.isPositioned]),a?v.jsx(Cc,{...t,ref:s,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null}));bc.displayName=xc;var Cc=e.forwardRef(((t,n)=>{const{__scopeSelect:r,onAutoScroll:o,...a}=t,i=Zs("SelectScrollButton",r),s=e.useRef(null),c=Ms(r),l=e.useCallback((()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)}),[]);return e.useEffect((()=>()=>l()),[l]),B((()=>{var e;const t=c().find((e=>e.ref.current===document.activeElement));null==(e=null==t?void 0:t.ref.current)||e.scrollIntoView({block:"nearest"})}),[c]),v.jsx(T.div,{"aria-hidden":!0,...a,ref:n,style:{flexShrink:0,...a.style},onPointerDown:h(a.onPointerDown,(()=>{null===s.current&&(s.current=window.setInterval(o,50))})),onPointerMove:h(a.onPointerMove,(()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(o,50))})),onPointerLeave:h(a.onPointerLeave,(()=>{l()}))})})),Ec=e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e;return v.jsx(T.div,{"aria-hidden":!0,...r,ref:t})}));Ec.displayName="SelectSeparator";var Rc="SelectArrow";function _c(e){return""===e||void 0===e}e.forwardRef(((e,t)=>{const{__scopeSelect:n,...r}=e,o=As(n),a=Ls(Rc,n),i=Zs(Rc,n);return a.open&&"popper"===i.position?v.jsx(An,{...o,...r,ref:t}):null})).displayName=Rc;var Dc=e.forwardRef(((t,n)=>{const{value:r,...o}=t,a=e.useRef(null),i=g(n,a),s=Ds(r);return e.useEffect((()=>{const e=a.current,t=window.HTMLSelectElement.prototype,n=Object.getOwnPropertyDescriptor(t,"value").set;if(s!==r&&n){const t=new Event("change",{bubbles:!0});n.call(e,r),e.dispatchEvent(t)}}),[s,r]),v.jsx(z,{asChild:!0,children:v.jsx("select",{...o,ref:i,defaultValue:r})})}));function Tc(t){const n=S(t),r=e.useRef(""),o=e.useRef(0),a=e.useCallback((e=>{const t=r.current+e;n(t),function e(t){r.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout((()=>e("")),1e3))}(t)}),[n]),i=e.useCallback((()=>{r.current="",window.clearTimeout(o.current)}),[]);return e.useEffect((()=>()=>window.clearTimeout(o.current)),[]),[r,a,i]}function Pc(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let a=(i=e,s=Math.max(o,0),i.map(((e,t)=>i[(s+t)%i.length])));var i,s;1===r.length&&(a=a.filter((e=>e!==n)));const c=a.find((e=>e.textValue.toLowerCase().startsWith(r.toLowerCase())));return c!==n?c:void 0}Dc.displayName="BubbleSelect";var Sc=Ws,jc=Hs,Mc=Vs,kc=Us,Nc=zs,Oc=Ys,Ac=oc,Ic=lc,Lc=pc,Fc=hc,Kc=gc,Wc=yc,Bc=bc,Hc=Ec,$c="Dialog",[Vc,Uc]=_($c),[zc,Xc]=Vc($c),Yc=t=>{const{__scopeDialog:n,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:s=!0}=t,c=e.useRef(null),l=e.useRef(null),[u=!1,d]=U({prop:o,defaultProp:a,onChange:i});return v.jsx(zc,{scope:n,triggerRef:c,contentRef:l,contentId:Ie(),titleId:Ie(),descriptionId:Ie(),open:u,onOpenChange:d,onOpenToggle:e.useCallback((()=>d((e=>!e))),[d]),modal:s,children:r})};Yc.displayName=$c;var qc="DialogTrigger",Gc=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Xc(qc,n),a=g(t,o.triggerRef);return v.jsx(T.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":hl(o.open),...r,ref:a,onClick:h(e.onClick,o.onOpenToggle)})}));Gc.displayName=qc;var Zc="DialogPortal",[Jc,Qc]=Vc(Zc,{forceMount:void 0}),el=t=>{const{__scopeDialog:n,forceMount:r,children:o,container:a}=t,i=Xc(Zc,n);return v.jsx(Jc,{scope:n,forceMount:r,children:e.Children.map(o,(e=>v.jsx($,{present:r||i.open,children:v.jsx(H,{asChild:!0,container:a,children:e})})))})};el.displayName=Zc;var tl="DialogOverlay",nl=e.forwardRef(((e,t)=>{const n=Qc(tl,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Xc(tl,e.__scopeDialog);return a.modal?v.jsx($,{present:r||a.open,children:v.jsx(rl,{...o,ref:t})}):null}));nl.displayName=tl;var rl=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Xc(tl,n);return v.jsx(Zo,{as:y,allowPinchZoom:!0,shards:[o.contentRef],children:v.jsx(T.div,{"data-state":hl(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})})),ol="DialogContent",al=e.forwardRef(((e,t)=>{const n=Qc(ol,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Xc(ol,e.__scopeDialog);return v.jsx($,{present:r||a.open,children:a.modal?v.jsx(il,{...o,ref:t}):v.jsx(sl,{...o,ref:t})})}));al.displayName=ol;var il=e.forwardRef(((t,n)=>{const r=Xc(ol,t.__scopeDialog),o=e.useRef(null),a=g(n,r.contentRef,o);return e.useEffect((()=>{const e=o.current;if(e)return lo(e)}),[]),v.jsx(cl,{...t,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:h(t.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()})),onPointerDownOutside:h(t.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:h(t.onFocusOutside,(e=>e.preventDefault()))})})),sl=e.forwardRef(((t,n)=>{const r=Xc(ol,t.__scopeDialog),o=e.useRef(!1),a=e.useRef(!1);return v.jsx(cl,{...t,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:e=>{var n,i;null==(n=t.onCloseAutoFocus)||n.call(t,e),e.defaultPrevented||(o.current||null==(i=r.triggerRef.current)||i.focus(),e.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:e=>{var n,i;null==(n=t.onInteractOutside)||n.call(t,e),e.defaultPrevented||(o.current=!0,"pointerdown"===e.detail.originalEvent.type&&(a.current=!0));const s=e.target;(null==(i=r.triggerRef.current)?void 0:i.contains(s))&&e.preventDefault(),"focusin"===e.detail.originalEvent.type&&a.current&&e.preventDefault()}})})),cl=e.forwardRef(((t,n)=>{const{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=t,c=Xc(ol,r),l=e.useRef(null),u=g(n,l);return Dr(),v.jsxs(v.Fragment,{children:[v.jsx(Mr,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:v.jsx(A,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":hl(c.open),...s,ref:u,onDismiss:()=>c.onOpenChange(!1)})}),v.jsxs(v.Fragment,{children:[v.jsx(yl,{titleId:c.titleId}),v.jsx(xl,{contentRef:l,descriptionId:c.descriptionId})]})]})})),ll="DialogTitle",ul=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Xc(ll,n);return v.jsx(T.h2,{id:o.titleId,...r,ref:t})}));ul.displayName=ll;var dl="DialogDescription",fl=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Xc(dl,n);return v.jsx(T.p,{id:o.descriptionId,...r,ref:t})}));fl.displayName=dl;var pl="DialogClose",vl=e.forwardRef(((e,t)=>{const{__scopeDialog:n,...r}=e,o=Xc(pl,n);return v.jsx(T.button,{type:"button",...r,ref:t,onClick:h(e.onClick,(()=>o.onOpenChange(!1)))})}));function hl(e){return e?"open":"closed"}vl.displayName=pl;var ml="DialogTitleWarning",[gl,wl]=function(t,n){const r=e.createContext(n),o=t=>{const{children:n,...o}=t,a=e.useMemo((()=>o),Object.values(o));return v.jsx(r.Provider,{value:a,children:n})};return o.displayName=t+"Provider",[o,function(o){const a=e.useContext(r);if(a)return a;if(void 0!==n)return n;throw new Error(`\`${o}\` must be used within \`${t}\``)}]}(ml,{contentName:ol,titleName:ll,docsSlug:"dialog"}),yl=({titleId:t})=>{const n=wl(ml),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.\n\nIf you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return e.useEffect((()=>{if(t){document.getElementById(t)}}),[r,t]),null},xl=({contentRef:t,descriptionId:n})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${wl("DialogDescriptionWarning").contentName}}.`;return e.useEffect((()=>{var e;const r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");if(n&&r){document.getElementById(n)}}),[r,t,n]),null},bl=Yc,Cl=Gc,El=el,Rl=nl,_l=al,Dl=ul,Tl=fl,Pl=vl,Sl="AlertDialog",[jl,Ml]=_(Sl,[Uc]),kl=Uc(),Nl=e=>{const{__scopeAlertDialog:t,...n}=e,r=kl(t);return v.jsx(bl,{...r,...n,modal:!0})};Nl.displayName=Sl;e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=kl(n);return v.jsx(Cl,{...o,...r,ref:t})})).displayName="AlertDialogTrigger";var Ol=e=>{const{__scopeAlertDialog:t,...n}=e,r=kl(t);return v.jsx(El,{...r,...n})};Ol.displayName="AlertDialogPortal";var Al=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=kl(n);return v.jsx(Rl,{...o,...r,ref:t})}));Al.displayName="AlertDialogOverlay";var Il="AlertDialogContent",[Ll,Fl]=jl(Il),Kl=e.forwardRef(((t,n)=>{const{__scopeAlertDialog:r,children:o,...a}=t,i=kl(r),s=e.useRef(null),c=g(n,s),l=e.useRef(null);return v.jsx(gl,{contentName:Il,titleName:Wl,docsSlug:"alert-dialog",children:v.jsx(Ll,{scope:r,cancelRef:l,children:v.jsxs(_l,{role:"alertdialog",...i,...a,ref:c,onOpenAutoFocus:h(a.onOpenAutoFocus,(e=>{var t;e.preventDefault(),null==(t=l.current)||t.focus({preventScroll:!0})})),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[v.jsx(b,{children:o}),v.jsx(Xl,{contentRef:s})]})})})}));Kl.displayName=Il;var Wl="AlertDialogTitle",Bl=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=kl(n);return v.jsx(Dl,{...o,...r,ref:t})}));Bl.displayName=Wl;var Hl="AlertDialogDescription",$l=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=kl(n);return v.jsx(Tl,{...o,...r,ref:t})}));$l.displayName=Hl;var Vl=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,o=kl(n);return v.jsx(Pl,{...o,...r,ref:t})}));Vl.displayName="AlertDialogAction";var Ul="AlertDialogCancel",zl=e.forwardRef(((e,t)=>{const{__scopeAlertDialog:n,...r}=e,{cancelRef:o}=Fl(Ul,n),a=kl(n),i=g(t,o);return v.jsx(Pl,{...a,...r,ref:i})}));zl.displayName=Ul;var Xl=({contentRef:t})=>{const n=`\`${Il}\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \`${Il}\` by passing a \`${Hl}\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${Il}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return e.useEffect((()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))}),[n,t]),null},Yl=Nl,ql=Ol,Gl=Al,Zl=Kl,Jl=Vl,Ql=zl,eu=Bl,tu=$l,nu="Switch",[ru,ou]=_(nu),[au,iu]=ru(nu),su=e.forwardRef(((t,n)=>{const{__scopeSwitch:r,name:o,checked:a,defaultChecked:i,required:s,disabled:c,value:l="on",onCheckedChange:u,form:d,...f}=t,[p,m]=e.useState(null),w=g(n,(e=>m(e))),y=e.useRef(!1),x=!p||(d||!!p.closest("form")),[b=!1,C]=U({prop:a,defaultProp:i,onChange:u});return v.jsxs(au,{scope:r,checked:b,disabled:c,children:[v.jsx(T.button,{type:"button",role:"switch","aria-checked":b,"aria-required":s,"data-state":du(b),"data-disabled":c?"":void 0,disabled:c,value:l,...f,ref:w,onClick:h(t.onClick,(e=>{C((e=>!e)),x&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())}))}),x&&v.jsx(uu,{control:p,bubbles:!y.current,name:o,value:l,checked:b,required:s,disabled:c,form:d,style:{transform:"translateX(-100%)"}})]})}));su.displayName=nu;var cu="SwitchThumb",lu=e.forwardRef(((e,t)=>{const{__scopeSwitch:n,...r}=e,o=iu(cu,n);return v.jsx(T.span,{"data-state":du(o.checked),"data-disabled":o.disabled?"":void 0,...r,ref:t})}));lu.displayName=cu;var uu=t=>{const{control:n,checked:r,bubbles:o=!0,...a}=t,i=e.useRef(null),s=Ds(r),c=pn(n);return e.useEffect((()=>{const e=i.current,t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(s!==r&&n){const t=new Event("click",{bubbles:o});n.call(e,r),e.dispatchEvent(t)}}),[s,r,o]),v.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...a,tabIndex:-1,ref:i,style:{...t.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function du(e){return e?"checked":"unchecked"}var fu=su,pu=lu,vu="Tabs",[hu,mu]=_(vu,[zr]),gu=zr(),[wu,yu]=hu(vu),xu=e.forwardRef(((e,t)=>{const{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:s,activationMode:c="automatic",...l}=e,u=Rr(s),[d,f]=U({prop:r,onChange:o,defaultProp:a});return v.jsx(wu,{scope:n,baseId:Ie(),value:d,onValueChange:f,orientation:i,dir:u,activationMode:c,children:v.jsx(T.div,{dir:u,"data-orientation":i,...l,ref:t})})}));xu.displayName=vu;var bu="TabsList",Cu=e.forwardRef(((e,t)=>{const{__scopeTabs:n,loop:r=!0,...o}=e,a=yu(bu,n),i=gu(n);return v.jsx(to,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:v.jsx(T.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})}));Cu.displayName=bu;var Eu="TabsTrigger",Ru=e.forwardRef(((e,t)=>{const{__scopeTabs:n,value:r,disabled:o=!1,...a}=e,i=yu(Eu,n),s=gu(n),c=Tu(i.baseId,r),l=Pu(i.baseId,r),u=r===i.value;return v.jsx(no,{asChild:!0,...s,focusable:!o,active:u,children:v.jsx(T.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":l,"data-state":u?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...a,ref:t,onMouseDown:h(e.onMouseDown,(e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(r)})),onKeyDown:h(e.onKeyDown,(e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(r)})),onFocus:h(e.onFocus,(()=>{const e="manual"!==i.activationMode;u||o||!e||i.onValueChange(r)}))})})}));Ru.displayName=Eu;var _u="TabsContent",Du=e.forwardRef(((t,n)=>{const{__scopeTabs:r,value:o,forceMount:a,children:i,...s}=t,c=yu(_u,r),l=Tu(c.baseId,o),u=Pu(c.baseId,o),d=o===c.value,f=e.useRef(d);return e.useEffect((()=>{const e=requestAnimationFrame((()=>f.current=!1));return()=>cancelAnimationFrame(e)}),[]),v.jsx($,{present:a||d,children:({present:e})=>v.jsx(T.div,{"data-state":d?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":l,hidden:!e,id:u,tabIndex:0,...s,ref:n,style:{...t.style,animationDuration:f.current?"0s":void 0},children:e&&i})})}));function Tu(e,t){return`${e}-trigger-${t}`}function Pu(e,t){return`${e}-content-${t}`}Du.displayName=_u;var Su=xu,ju=Cu,Mu=Ru,ku=Du,Nu="Checkbox",[Ou,Au]=_(Nu),[Iu,Lu]=Ou(Nu),Fu=e.forwardRef(((t,n)=>{const{__scopeCheckbox:r,name:o,checked:a,defaultChecked:i,required:s,disabled:c,value:l="on",onCheckedChange:u,form:d,...f}=t,[p,m]=e.useState(null),w=g(n,(e=>m(e))),y=e.useRef(!1),x=!p||(d||!!p.closest("form")),[b=!1,C]=U({prop:a,defaultProp:i,onChange:u}),E=e.useRef(b);return e.useEffect((()=>{const e=null==p?void 0:p.form;if(e){const t=()=>C(E.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}}),[p,C]),v.jsxs(Iu,{scope:r,state:b,disabled:c,children:[v.jsx(T.button,{type:"button",role:"checkbox","aria-checked":Hu(b)?"mixed":b,"aria-required":s,"data-state":$u(b),"data-disabled":c?"":void 0,disabled:c,value:l,...f,ref:w,onKeyDown:h(t.onKeyDown,(e=>{"Enter"===e.key&&e.preventDefault()})),onClick:h(t.onClick,(e=>{C((e=>!!Hu(e)||!e)),x&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())}))}),x&&v.jsx(Bu,{control:p,bubbles:!y.current,name:o,value:l,checked:b,required:s,disabled:c,form:d,style:{transform:"translateX(-100%)"},defaultChecked:!Hu(i)&&i})]})}));Fu.displayName=Nu;var Ku="CheckboxIndicator",Wu=e.forwardRef(((e,t)=>{const{__scopeCheckbox:n,forceMount:r,...o}=e,a=Lu(Ku,n);return v.jsx($,{present:r||Hu(a.state)||!0===a.state,children:v.jsx(T.span,{"data-state":$u(a.state),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})}));Wu.displayName=Ku;var Bu=t=>{const{control:n,checked:r,bubbles:o=!0,defaultChecked:a,...i}=t,s=e.useRef(null),c=Ds(r),l=pn(n);e.useEffect((()=>{const e=s.current,t=window.HTMLInputElement.prototype,n=Object.getOwnPropertyDescriptor(t,"checked").set;if(c!==r&&n){const t=new Event("click",{bubbles:o});e.indeterminate=Hu(r),n.call(e,!Hu(r)&&r),e.dispatchEvent(t)}}),[c,r,o]);const u=e.useRef(!Hu(r)&&r);return v.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a??u.current,...i,tabIndex:-1,ref:s,style:{...t.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function Hu(e){return"indeterminate"===e}function $u(e){return Hu(e)?"indeterminate":e?"checked":"unchecked"}var Vu=Fu,Uu=Wu;export{Cr as $,Jl as A,Su as B,Oc as C,Tl as D,Pe as E,Se as F,ke as G,Ne as H,kc as I,je as J,Me as K,Ic as L,Te as M,lr as N,Rl as O,Nc as P,ir as Q,Sc as R,Wc as S,jc as T,sr as U,Ac as V,cr as W,y as X,xr as Y,br as Z,fo as _,Bc as a,Gi as a0,Zi as a1,Bi as a2,Hi as a3,Ui as a4,zi as a5,Yi as a6,Xi as a7,Vi as a8,qi as a9,Ki as aa,Wi as ab,$i as ac,bs as ad,Cs as ae,ys as af,xs as ag,Rs as ah,Lc as b,Kc as c,Fc as d,Hc as e,Mc as f,_l as g,Pl as h,Dl as i,v as j,bl as k,El as l,Gl as m,Zl as n,eu as o,tu as p,Ql as q,Yl as r,ql as s,Vu as t,Uu as u,fu as v,pu as w,ju as x,Mu as y,ku as z};
