import{j as e}from"./chunk-CpdlqjqK.js";import{r as s,u as a,c as t,L as l}from"./chunk-b0HHmiEU.js";import{u as i,B as n,M as d,S as r,c,A as m,d as o,e as x,f as h,I as u,C as j,g as p,h as g,i as y}from"./index-DwRq5Uwb.js";import{J as N}from"./chunk-Cw96wKwP.js";import{L as v,U as b,B as f,d as w,e as k,P as C,a as S,X as A,f as B,M as P,g as E,h as D,i as U,H as F,j as M}from"./chunk-BGoCADfv.js";import"./chunk-BvIisuNF.js";import"./chunk-D1Z7_RhR.js";import"./chunk-DgNUPAoM.js";const O=()=>{var O;const{currentUser:Y,userData:I,refreshUserData:L,signOut:T}=i(),[R,z]=s.useState([]),[H,J]=s.useState(!0),[V,W]=s.useState(!1),[Z,q]=s.useState("dashboard"),G=a(),K=t(),[Q,X]=s.useState({displayName:"",phone:"",address:"",apartment:"",city:"",state:"",pincode:"",community:"",customCommunity:""}),[$,_]=s.useState([]),[ee,se]=s.useState([]),[ae,te]=s.useState(!1),[le,ie]=s.useState(null);s.useEffect((()=>{const e=K.pathname;e.includes("profile")?q("profile"):e.includes("books")?q("books"):e.includes("settings")?q("settings"):q("dashboard")}),[K.pathname]),s.useEffect((()=>{(async()=>{if(Y)try{J(!0);const e=await p(Y.uid);z(e),I&&(X({displayName:I.displayName||"",phone:I.phone||"",address:I.address||"",apartment:I.apartment||"",city:I.city||"",state:I.state||"",pincode:I.pincode||"",community:I.community||"",customCommunity:""}),I.pincode&&6===I.pincode.length&&ne(I.pincode))}catch(e){N.error("Failed to load profile data")}finally{J(!1)}})()}),[Y,I]);const ne=async e=>{if(6!==e.length)return _([]),void se([]);te(!0),ie(null);try{const s=await g(e);_(s);const a=s.map((e=>({value:e,label:e})));a.push({value:"Other",label:"Other (specify below)"}),se(a)}catch(s){ie("Failed to load communities for this pincode"),_([]),se([])}finally{te(!1)}},de=e=>{const{name:s,value:a}=e.target;X((e=>({...e,[s]:a}))),"pincode"===s&&6===a.length?ne(a):"pincode"===s&&6!==a.length&&(_([]),se([]))},re=e=>{switch(q(e),e){case"profile":G("/profile");break;case"books":G("/my-books");break;case"settings":G("/settings");break;default:G("/dashboard")}},ce=(null==I?void 0:I.displayName)||(null==Y?void 0:Y.displayName)||(null==(O=null==Y?void 0:Y.email)?void 0:O.split("@")[0])||"Reader";(null==I?void 0:I.email)||null==Y||Y.email;const me=R.length||0,oe=R.filter((e=>e.approvalStatus===n.Approved||!e.approvalStatus)).length,xe=R.filter((e=>e.approvalStatus===n.Pending)).length;return H?e.jsx(d,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:e.jsxs("div",{className:"flex flex-col md:flex-row gap-8",children:[e.jsxs("div",{className:"md:w-1/4",children:[e.jsx(r,{className:"h-40 w-40 rounded-full mx-auto"}),e.jsxs("div",{className:"mt-4 space-y-2",children:[e.jsx(r,{className:"h-6 w-full"}),e.jsx(r,{className:"h-4 w-3/4"}),e.jsx(r,{className:"h-4 w-1/2"})]})]}),e.jsxs("div",{className:"md:w-3/4 space-y-6",children:[e.jsx(r,{className:"h-8 w-1/2"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(r,{className:"h-20 w-full"}),e.jsx(r,{className:"h-20 w-full"}),e.jsx(r,{className:"h-20 w-full"}),e.jsx(r,{className:"h-20 w-full"})]})]})]})})})}):Y&&I?e.jsx(d,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:e.jsxs("div",{className:"flex flex-col md:flex-row",children:[e.jsxs("div",{className:"md:w-64 bg-gray-50 p-6 border-r border-gray-200",children:[e.jsxs("div",{className:"flex flex-col items-center mb-6",children:[e.jsxs(m,{className:"h-20 w-20",children:[e.jsx(o,{src:I.photoURL||"",alt:I.displayName||"User"}),e.jsx(x,{className:"text-xl bg-burgundy-100 text-burgundy-700",children:(null==I?void 0:I.displayName)?I.displayName.split(" ").map((e=>e[0])).join("").toUpperCase().substring(0,2):(null==(he=null==Y?void 0:Y.email)?void 0:he.substring(0,2).toUpperCase())||"U"})]}),e.jsx("h2",{className:"mt-4 text-lg font-semibold text-center",children:I.displayName}),e.jsx("p",{className:"text-sm text-gray-600 text-center",children:I.email})]}),e.jsxs("nav",{className:"space-y-1",children:[e.jsxs("button",{onClick:()=>re("dashboard"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("dashboard"===Z?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[e.jsx(v,{className:"h-4 w-4 mr-3"}),"Dashboard"]}),e.jsxs("button",{onClick:()=>re("profile"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("profile"===Z?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[e.jsx(b,{className:"h-4 w-4 mr-3"}),"Profile"]}),e.jsxs("button",{onClick:()=>re("books"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("books"===Z?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[e.jsx(f,{className:"h-4 w-4 mr-3"}),"My Books"]}),e.jsxs("button",{onClick:()=>re("settings"),className:"w-full flex items-center px-3 py-2 text-sm rounded-md transition-colors "+("settings"===Z?"bg-burgundy-100 text-burgundy-700 font-medium":"text-gray-700 hover:bg-gray-100"),children:[e.jsx(w,{className:"h-4 w-4 mr-3"}),"Settings"]})]}),e.jsx("div",{className:"mt-auto pt-6 border-t border-gray-200 mt-6",children:e.jsxs("button",{onClick:async()=>{try{await T(),G("/"),N.success("Signed out successfully")}catch(e){N.error("Failed to sign out")}},className:"w-full flex items-center px-3 py-2 text-sm rounded-md text-gray-700 hover:bg-gray-100 transition-colors",children:[e.jsx(k,{className:"h-4 w-4 mr-3"}),"Sign Out"]})})]}),e.jsxs("div",{className:"flex-1 p-6",children:["dashboard"===Z&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between mb-6",children:[e.jsxs("div",{children:[e.jsxs("h1",{className:"text-2xl font-bold text-navy-800 mb-2",children:["Welcome, ",ce,"!"]}),e.jsx("p",{className:"text-gray-600",children:"Manage your books and exchanges"})]}),e.jsx("div",{className:"mt-4 md:mt-0",children:e.jsx(l,{to:"/add-books",children:e.jsxs(c,{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-4 w-4"}),"Add New Books"]})})})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-burgundy-600",children:me}),e.jsx("div",{className:"text-sm text-gray-600",children:"Total Books"})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-green-600",children:oe}),e.jsx("div",{className:"text-sm text-gray-600",children:"Active Listings"})]}),e.jsxs("div",{className:"bg-gray-50 p-4 rounded-md shadow-sm",children:[e.jsx("div",{className:"text-3xl font-bold text-amber-600",children:xe}),e.jsx("div",{className:"text-sm text-gray-600",children:"Pending Approval"})]})]}),e.jsxs("div",{className:"mb-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-xl font-bold text-navy-800",children:"Your Books"}),e.jsx(c,{variant:"link",className:"text-burgundy-600",onClick:()=>re("books"),children:"View All"})]}),R.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:R.slice(0,4).map((s=>e.jsx(h,{book:s},s.id)))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),e.jsx(l,{to:"/add-books",children:e.jsxs(c,{children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]})]}),"profile"===Z&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Profile"}),V?e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(c,{variant:"outline",onClick:()=>{I&&(X({displayName:I.displayName||"",phone:I.phone||"",address:I.address||"",apartment:I.apartment||"",city:I.city||"",state:I.state||"",pincode:I.pincode||"",community:I.community||"",customCommunity:""}),I.pincode&&6===I.pincode.length?ne(I.pincode):(_([]),se([]))),W(!1)},className:"flex items-center gap-2",children:[e.jsx(A,{className:"h-4 w-4"}),"Cancel"]}),e.jsxs(c,{onClick:async e=>{if(e.preventDefault(),Y)try{const e={...Q,community:"Other"===Q.community?Q.customCommunity:Q.community},{customCommunity:s,...a}=e;await y(Y.uid,a),await L(),N.success("Profile updated successfully"),W(!1)}catch(s){N.error("Failed to update profile")}},className:"flex items-center gap-2",children:[e.jsx(B,{className:"h-4 w-4"}),"Save Changes"]})]}):e.jsxs(c,{onClick:()=>W(!0),className:"flex items-center gap-2",children:[e.jsx(S,{className:"h-4 w-4"}),"Edit Profile"]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[e.jsx(b,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Personal Information"]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Email"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(P,{className:"h-4 w-4 text-gray-400 mr-2"}),e.jsx("p",{children:I.email})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Phone"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(E,{className:"h-4 w-4 text-gray-400 mr-2"}),V?e.jsx(u,{name:"phone",value:Q.phone,onChange:de,placeholder:"Enter phone number"}):e.jsx("p",{children:I.phone||"Not provided"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Member Since"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(D,{className:"h-4 w-4 text-gray-400 mr-2"}),e.jsx("p",{children:(e=>{if(!e)return"N/A";const s=e.toDate?e.toDate():new Date(e);return new Intl.DateTimeFormat("en-IN",{year:"numeric",month:"long",day:"numeric"}).format(s)})(I.createdAt)})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Display Name"}),e.jsxs("div",{className:"flex items-center mt-1",children:[e.jsx(b,{className:"h-4 w-4 text-gray-400 mr-2"}),V?e.jsx(u,{name:"displayName",value:Q.displayName,onChange:de,placeholder:"Enter display name"}):e.jsx("p",{children:I.displayName||"Not provided"})]})]})]})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[e.jsxs("h3",{className:"font-medium text-navy-800 mb-4 flex items-center",children:[e.jsx(U,{className:"h-5 w-5 mr-2 text-burgundy-500"}),"Address Information"]}),V?e.jsxs("form",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Address"}),e.jsx(u,{name:"address",value:Q.address,onChange:de,placeholder:"Enter your address",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"City"}),e.jsx(u,{name:"city",value:Q.city,onChange:de,placeholder:"Enter city",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"State"}),e.jsx(u,{name:"state",value:Q.state,onChange:de,placeholder:"Enter state",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Pincode"}),e.jsx(u,{name:"pincode",value:Q.pincode,onChange:de,placeholder:"Enter pincode",className:"mt-1"})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"text-sm text-gray-500",children:["Community",e.jsx("span",{className:"text-xs text-blue-600 ml-1",children:"(affects book discovery)"})]}),ee.length>0?e.jsxs("div",{className:"mt-1",children:[e.jsx(j,{options:ee,value:Q.community,onChange:e=>{X((s=>({...s,community:e,customCommunity:"Other"===e?s.customCommunity:""})))},placeholder:"Select your community",emptyMessage:"No communities found",disabled:ae}),ae&&e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Loading communities..."}),le&&e.jsx("p",{className:"text-xs text-red-500 mt-1",children:le}),"Other"===Q.community&&e.jsx(u,{name:"customCommunity",value:Q.customCommunity,onChange:de,placeholder:"Enter your community name",className:"mt-2"})]}):e.jsxs("div",{className:"mt-1",children:[e.jsx(u,{name:"community",value:Q.community,onChange:de,placeholder:"Enter your community name",className:"mt-1"}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Enter a 6-digit pincode above to see available communities"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm text-gray-500",children:"Apartment/Building"}),e.jsx(u,{name:"apartment",value:Q.apartment,onChange:de,placeholder:"Enter apartment or building name",className:"mt-1"})]})]}):e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Address"}),e.jsxs("div",{className:"flex items-start mt-1",children:[e.jsx(F,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),e.jsx("p",{children:I.address||"Not provided"})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"City"}),e.jsx("p",{className:"mt-1",children:I.city||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"State"}),e.jsx("p",{className:"mt-1",children:I.state||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Pincode"}),e.jsx("p",{className:"mt-1",children:I.pincode||"Not provided"})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Community"}),e.jsxs("div",{className:"flex items-start mt-1",children:[e.jsx(F,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),e.jsxs("div",{children:[e.jsx("p",{children:I.community||"Not provided"}),I.community&&e.jsx("p",{className:"text-xs text-blue-600 mt-1",children:"Books from your community appear first in Browse Books"})]})]})]}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-500",children:"Apartment/Building"}),e.jsxs("div",{className:"flex items-start mt-1",children:[e.jsx(M,{className:"h-4 w-4 text-gray-400 mr-2 mt-1"}),e.jsx("p",{children:I.apartment||"Not provided"})]})]})]})]})]}),"books"===Z&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800",children:"My Books"}),e.jsx(l,{to:"/add-books",children:e.jsxs(c,{className:"flex items-center gap-2",children:[e.jsx(C,{className:"h-4 w-4"}),"Add New Book"]})})]}),R.length>0?e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:R.map((s=>e.jsx(h,{book:s},s.id)))}):e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-500 mb-4",children:"You haven't added any books yet"}),e.jsx(l,{to:"/add-books",children:e.jsxs(c,{children:[e.jsx(C,{className:"h-4 w-4 mr-2"}),"Add Your First Book"]})})]})]}),"settings"===Z&&e.jsxs("div",{children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-6",children:"Account Settings"}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Notification Preferences"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to customize your notification preferences here."})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5 mb-6",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4",children:"Privacy Settings"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"Coming soon! You'll be able to manage your privacy settings here."})]}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-5",children:[e.jsx("h3",{className:"font-medium text-navy-800 mb-4 text-red-600",children:"Danger Zone"}),e.jsx("p",{className:"text-gray-500 mb-4",children:"These actions are irreversible. Please proceed with caution."}),e.jsx(c,{variant:"destructive",disabled:!0,children:"Delete Account"})]})]})]})]})})})}):e.jsx(d,{children:e.jsx("div",{className:"container mx-auto px-4 py-8",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:[e.jsx("h1",{className:"text-2xl font-bold text-navy-800 mb-4",children:"User Not Found"}),e.jsx("p",{className:"text-gray-600 mb-6",children:"Please sign in to view your account."}),e.jsx(l,{to:"/signin",children:e.jsx(c,{children:"Sign In"})})]})})});var he};export{O as default};
