import{R as e}from"./chunk-b0HHmiEU.js";var t=e=>"checkbox"===e.type,r=e=>e instanceof Date,s=e=>null==e;const a=e=>"object"==typeof e;var i=e=>!s(e)&&!Array.isArray(e)&&a(e)&&!r(e),n=e=>i(e)&&e.target?t(e.target)?e.target.checked:e.target.value:e,l=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),o="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function u(e){let t;const r=Array.isArray(e);if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(o&&(e instanceof Blob||e instanceof FileList)||!r&&!i(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return i(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=u(e[r]));else t=e}return t}var d=e=>Array.isArray(e)?e.filter(Boolean):[],c=e=>void 0===e,f=(e,t,r)=>{if(!t||!i(e))return r;const a=d(t.split(/[,[\].]+?/)).reduce(((e,t)=>s(e)?e:e[t]),e);return c(a)||a===e?c(e[t])?r:e[t]:a},m=e=>"boolean"==typeof e,y=e=>/^\w*$/.test(e),v=e=>d(e.replace(/["|']|\]/g,"").split(/\.|\[/)),g=(e,t,r)=>{let s=-1;const a=y(t)?[t]:v(t),n=a.length,l=n-1;for(;++s<n;){const t=a[s];let n=r;if(s!==l){const r=e[t];n=i(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t)return;e[t]=n,e=e[t]}return e};const h="blur",b="focusout",_="change",p="onBlur",V="onChange",F="onSubmit",A="onTouched",S="all",x="max",w="min",D="maxLength",k="minLength",E="pattern",O="required",j="validate",C=e.createContext(null),T=()=>e.useContext(C),N=t=>{const{children:r,...s}=t;return e.createElement(C.Provider,{value:s},r)};var U=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==S&&(t._proxyFormState[a]=!s||S),r&&(r[a]=!0),e[a]}});return a},L=e=>i(e)&&!Object.keys(e).length,B=(e,t,r,s)=>{r(e);const{name:a,...i}=e;return L(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!s||S)))},M=e=>Array.isArray(e)?e:[e],R=(e,t,r)=>!e||!t||e===t||M(e).some((e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))));function P(t){const r=e.useRef(t);r.current=t,e.useEffect((()=>{const e=!t.disabled&&r.current.subject&&r.current.subject.subscribe({next:r.current.next});return()=>{e&&e.unsubscribe()}}),[t.disabled])}var q=e=>"string"==typeof e,W=(e,t,r,s,a)=>q(e)?(s&&t.watch.add(e),f(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),f(r,e)))):(s&&(t.watchAll=!0),r);function I(t){const r=T(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:l}=t||{},o=e.useRef(a);o.current=a,P({disabled:n,subject:s._subjects.values,next:e=>{R(o.current,e.name,l)&&c(u(W(o.current,s._names,e.values||s._formValues,!1,i)))}});const[d,c]=e.useState(s._getWatch(a,i));return e.useEffect((()=>s._removeUnmounted())),d}function $(t){const r=T(),{name:s,disabled:a,control:i=r.control,shouldUnregister:o}=t,d=l(i._names.array,s),y=I({control:i,name:s,defaultValue:f(i._formValues,s,f(i._defaultValues,s,t.defaultValue)),exact:!0}),v=function(t){const r=T(),{control:s=r.control,disabled:a,name:i,exact:n}=t||{},[l,o]=e.useState(s._formState),u=e.useRef(!0),d=e.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),c=e.useRef(i);return c.current=i,P({disabled:a,next:e=>u.current&&R(c.current,e.name,n)&&B(e,d.current,s._updateFormState)&&o({...s._formState,...e}),subject:s._subjects.state}),e.useEffect((()=>(u.current=!0,d.current.isValid&&s._updateValid(!0),()=>{u.current=!1})),[s]),U(l,s,d.current,!1)}({control:i,name:s,exact:!0}),b=e.useRef(i.register(s,{...t.rules,value:y,...m(t.disabled)?{disabled:t.disabled}:{}}));return e.useEffect((()=>{const e=i._options.shouldUnregister||o,t=(e,t)=>{const r=f(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=u(f(i._options.defaultValues,s));g(i._defaultValues,s,e),c(f(i._formValues,s))&&g(i._formValues,s,e)}return()=>{(d?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}}),[s,i,d,o]),e.useEffect((()=>{f(i._fields,s)&&i._updateDisabledField({disabled:a,fields:i._fields,name:s,value:f(i._fields,s)._f.value})}),[a,s,i]),{field:{name:s,value:y,...m(a)||v.disabled?{disabled:v.disabled||a}:{},onChange:e.useCallback((e=>b.current.onChange({target:{value:n(e),name:s},type:_})),[s]),onBlur:e.useCallback((()=>b.current.onBlur({target:{value:f(i._formValues,s),name:s},type:h})),[s,i]),ref:e.useCallback((e=>{const t=f(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}),[i._fields,s])},formState:v,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!f(v.errors,s)},isDirty:{enumerable:!0,get:()=>!!f(v.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!f(v.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!f(v.validatingFields,s)},error:{enumerable:!0,get:()=>f(v.errors,s)}})}}const H=e=>e.render($(e));var z=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},G=e=>({isOnSubmit:!e||e===F,isOnBlur:e===p,isOnChange:e===V,isOnAll:e===S,isOnTouch:e===A}),J=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const K=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=f(e,a);if(r){const{_f:e,...n}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(K(n,t))break}else if(i(n)&&K(n,t))break}}};var Q=(e,t,r)=>{const s=M(f(e,r));return g(s,"root",t[r]),g(e,r,s),e},X=e=>"file"===e.type,Y=e=>"function"==typeof e,Z=e=>{if(!o)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},ee=e=>q(e),te=e=>"radio"===e.type,re=e=>e instanceof RegExp;const se={value:!1,isValid:!1},ae={value:!0,isValid:!0};var ie=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!c(e[0].attributes.value)?c(e[0].value)||""===e[0].value?ae:{value:e[0].value,isValid:!0}:ae:se}return se};const ne={isValid:!1,value:null};var le=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),ne):ne;function oe(e,t,r="validate"){if(ee(e)||Array.isArray(e)&&e.every(ee)||m(e)&&!e)return{type:r,message:ee(e)?e:"",ref:t}}var ue=e=>i(e)&&!re(e)?e:{value:e,message:""},de=async(e,r,a,n,l)=>{const{ref:o,refs:u,required:d,maxLength:y,minLength:v,min:g,max:h,pattern:b,validate:_,name:p,valueAsNumber:V,mount:F,disabled:A}=e._f,S=f(r,p);if(!F||A)return{};const C=u?u[0]:o,T=e=>{n&&C.reportValidity&&(C.setCustomValidity(m(e)?"":e||""),C.reportValidity())},N={},U=te(o),B=t(o),M=U||B,R=(V||X(o))&&c(o.value)&&c(S)||Z(o)&&""===o.value||""===S||Array.isArray(S)&&!S.length,P=z.bind(null,p,a,N),W=(e,t,r,s=D,a=k)=>{const i=e?t:r;N[p]={type:e?s:a,message:i,ref:o,...P(e?s:a,i)}};if(l?!Array.isArray(S)||!S.length:d&&(!M&&(R||s(S))||m(S)&&!S||B&&!ie(u).isValid||U&&!le(u).isValid)){const{value:e,message:t}=ee(d)?{value:!!d,message:d}:ue(d);if(e&&(N[p]={type:O,message:t,ref:C,...P(O,t)},!a))return T(t),N}if(!(R||s(g)&&s(h))){let e,t;const r=ue(h),i=ue(g);if(s(S)||isNaN(S)){const s=o.valueAsDate||new Date(S),a=e=>new Date((new Date).toDateString()+" "+e),n="time"==o.type,l="week"==o.type;q(r.value)&&S&&(e=n?a(S)>a(r.value):l?S>r.value:s>new Date(r.value)),q(i.value)&&S&&(t=n?a(S)<a(i.value):l?S<i.value:s<new Date(i.value))}else{const a=o.valueAsNumber||(S?+S:S);s(r.value)||(e=a>r.value),s(i.value)||(t=a<i.value)}if((e||t)&&(W(!!e,r.message,i.message,x,w),!a))return T(N[p].message),N}if((y||v)&&!R&&(q(S)||l&&Array.isArray(S))){const e=ue(y),t=ue(v),r=!s(e.value)&&S.length>+e.value,i=!s(t.value)&&S.length<+t.value;if((r||i)&&(W(r,e.message,t.message),!a))return T(N[p].message),N}if(b&&!R&&q(S)){const{value:e,message:t}=ue(b);if(re(e)&&!S.match(e)&&(N[p]={type:E,message:t,ref:o,...P(E,t)},!a))return T(t),N}if(_)if(Y(_)){const e=oe(await _(S,r),C);if(e&&(N[p]={...e,...P(j,e.message)},!a))return T(e.message),N}else if(i(_)){let e={};for(const t in _){if(!L(e)&&!a)break;const s=oe(await _[t](S,r),C,t);s&&(e={...s,...P(t,s.message)},T(s.message),a&&(N[p]=e))}if(!L(e)&&(N[p]={ref:C,...e},!a))return N}return T(!0),N};function ce(e,t){const r=Array.isArray(t)?t:y(t)?[t]:v(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=c(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,n=r[a];return s&&delete s[n],0!==a&&(i(s)&&L(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!c(e[t]))return!1;return!0}(s))&&ce(e,r.slice(0,-1)),e}var fe=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},me=e=>s(e)||!a(e);function ye(e,t){if(me(e)||me(t))return e===t;if(r(e)&&r(t))return e.getTime()===t.getTime();const s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;for(const n of s){const s=e[n];if(!a.includes(n))return!1;if("ref"!==n){const e=t[n];if(r(s)&&r(e)||i(s)&&i(e)||Array.isArray(s)&&Array.isArray(e)?!ye(s,e):s!==e)return!1}}return!0}var ve=e=>"select-multiple"===e.type,ge=e=>Z(e)&&e.isConnected,he=e=>{for(const t in e)if(Y(e[t]))return!0;return!1};function be(e,t={}){const r=Array.isArray(e);if(i(e)||r)for(const a in e)Array.isArray(e[a])||i(e[a])&&!he(e[a])?(t[a]=Array.isArray(e[a])?[]:{},be(e[a],t[a])):s(e[a])||(t[a]=!0);return t}function _e(e,t,r){const a=Array.isArray(e);if(i(e)||a)for(const n in e)Array.isArray(e[n])||i(e[n])&&!he(e[n])?c(t)||me(r[n])?r[n]=Array.isArray(e[n])?be(e[n],[]):{...be(e[n])}:_e(e[n],s(t)?{}:t[n],r[n]):r[n]=!ye(e[n],t[n]);return r}var pe=(e,t)=>_e(e,t,be(t)),Ve=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>c(e)?e:t?""===e?NaN:e?+e:e:r&&q(e)?new Date(e):s?s(e):e;function Fe(e){const r=e.ref;if(!(e.refs?e.refs.every((e=>e.disabled)):r.disabled))return X(r)?r.files:te(r)?le(e.refs).value:ve(r)?[...r.selectedOptions].map((({value:e})=>e)):t(r)?ie(e.refs).value:Ve(c(r.value)?e.ref.value:r.value,e)}var Ae=e=>c(e)?e:re(e)?e.source:i(e)?re(e.value)?e.value.source:e.value:e;const Se="AsyncFunction";function xe(e,t,r){const s=f(e,r);if(s||y(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=f(t,s),n=f(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};a.pop()}return{name:r}}const we={mode:F,reValidateMode:V,shouldFocusError:!0};function De(e={}){let a,y={...we,...e},v={submitCount:0,isDirty:!1,isLoading:Y(y.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:y.errors||{},disabled:y.disabled||!1},_={},p=(i(y.defaultValues)||i(y.values))&&u(y.defaultValues||y.values)||{},V=y.shouldUnregister?{}:u(p),F={action:!1,mount:!1,watch:!1},A={mount:new Set,unMount:new Set,array:new Set,watch:new Set},x=0;const w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},D={values:fe(),array:fe(),state:fe()},k=G(y.mode),E=G(y.reValidateMode),O=y.criteriaMode===S,j=async t=>{if(!e.disabled&&(w.isValid||t)){const e=y.resolver?L((await B()).errors):await R(_,!0);e!==v.isValid&&D.state.next({isValid:e})}},C=(t,r)=>{e.disabled||!w.isValidating&&!w.validatingFields||((t||Array.from(A.mount)).forEach((e=>{e&&(r?g(v.validatingFields,e,r):ce(v.validatingFields,e))})),D.state.next({validatingFields:v.validatingFields,isValidating:!L(v.validatingFields)}))},T=(e,t,r,s)=>{const a=f(_,e);if(a){const i=f(V,e,c(r)?f(p,e):r);c(i)||s&&s.defaultChecked||t?g(V,e,t?i:Fe(a._f)):$(e,i),F.mount&&j()}},N=(t,r,s,a,i)=>{let n=!1,l=!1;const o={name:t};if(!e.disabled){const e=!!(f(_,t)&&f(_,t)._f&&f(_,t)._f.disabled);if(!s||a){w.isDirty&&(l=v.isDirty,v.isDirty=o.isDirty=P(),n=l!==o.isDirty);const s=e||ye(f(p,t),r);l=!(e||!f(v.dirtyFields,t)),s||e?ce(v.dirtyFields,t):g(v.dirtyFields,t,!0),o.dirtyFields=v.dirtyFields,n=n||w.dirtyFields&&l!==!s}if(s){const e=f(v.touchedFields,t);e||(g(v.touchedFields,t,s),o.touchedFields=v.touchedFields,n=n||w.touchedFields&&e!==s)}n&&i&&D.state.next(o)}return n?o:{}},U=(t,r,s,i)=>{const n=f(v.errors,t),l=w.isValid&&m(r)&&v.isValid!==r;var o;if(e.delayError&&s?(o=()=>((e,t)=>{g(v.errors,e,t),D.state.next({errors:v.errors})})(t,s),a=e=>{clearTimeout(x),x=setTimeout(o,e)},a(e.delayError)):(clearTimeout(x),a=null,s?g(v.errors,t,s):ce(v.errors,t)),(s?!ye(n,s):n)||!L(i)||l){const e={...i,...l&&m(r)?{isValid:r}:{},errors:v.errors,name:t};v={...v,...e},D.state.next(e)}},B=async e=>{C(e,!0);const t=await y.resolver(V,y.context,((e,t,r,s)=>{const a={};for(const i of e){const e=f(t,i);e&&g(a,i,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||A.mount,_,y.criteriaMode,y.shouldUseNativeValidation));return C(e),t},R=async(e,t,r={valid:!0})=>{for(const a in e){const n=e[a];if(n){const{_f:e,...l}=n;if(e){const l=A.array.has(e.name),o=n._f&&!((s=n._f)&&s.validate||!(Y(s.validate)&&s.validate.constructor.name===Se||i(s.validate)&&Object.values(s.validate).find((e=>e.constructor.name===Se))));o&&w.validatingFields&&C([a],!0);const u=await de(n,V,O,y.shouldUseNativeValidation&&!t,l);if(o&&w.validatingFields&&C([a]),u[e.name]&&(r.valid=!1,t))break;!t&&(f(u,e.name)?l?Q(v.errors,u,e.name):g(v.errors,e.name,u[e.name]):ce(v.errors,e.name))}!L(l)&&await R(l,t,r)}}var s;return r.valid},P=(t,r)=>!e.disabled&&(t&&r&&g(V,t,r),!ye(ae(),p)),I=(e,t,r)=>W(e,A,{...F.mount?V:c(t)?p:q(e)?{[e]:t}:t},r,t),$=(e,r,a={})=>{const i=f(_,e);let n=r;if(i){const a=i._f;a&&(!a.disabled&&g(V,e,Ve(r,a)),n=Z(a.ref)&&s(r)?"":r,ve(a.ref)?[...a.ref.options].forEach((e=>e.selected=n.includes(e.value))):a.refs?t(a.ref)?a.refs.length>1?a.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(n)?!!n.find((t=>t===e.value)):n===e.value))):a.refs[0]&&(a.refs[0].checked=!!n):a.refs.forEach((e=>e.checked=e.value===n)):X(a.ref)?a.ref.value="":(a.ref.value=n,a.ref.type||D.values.next({name:e,values:{...V}})))}(a.shouldDirty||a.shouldTouch)&&N(e,n,a.shouldTouch,a.shouldDirty,!0),a.shouldValidate&&se(e)},H=(e,t,s)=>{for(const a in t){const n=t[a],l=`${e}.${a}`,o=f(_,l);(A.array.has(e)||i(n)||o&&!o._f)&&!r(n)?H(l,n,s):$(l,n,s)}},z=(e,t,r={})=>{const a=f(_,e),i=A.array.has(e),n=u(t);g(V,e,n),i?(D.array.next({name:e,values:{...V}}),(w.isDirty||w.dirtyFields)&&r.shouldDirty&&D.state.next({name:e,dirtyFields:pe(p,V),isDirty:P(e,n)})):!a||a._f||s(n)?$(e,n,r):H(e,n,r),J(e,A)&&D.state.next({...v}),D.values.next({name:F.mount?e:void 0,values:{...V}})},ee=async t=>{F.mount=!0;const s=t.target;let i=s.name,l=!0;const o=f(_,i),u=e=>{l=Number.isNaN(e)||r(e)&&isNaN(e.getTime())||ye(e,f(V,i,e))};if(o){let r,c;const m=s.type?Fe(o._f):n(t),p=t.type===h||t.type===b,F=!((d=o._f).mount&&(d.required||d.min||d.max||d.maxLength||d.minLength||d.pattern||d.validate)||y.resolver||f(v.errors,i)||o._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(p,f(v.touchedFields,i),v.isSubmitted,E,k),S=J(i,A,p);g(V,i,m),p?(o._f.onBlur&&o._f.onBlur(t),a&&a(0)):o._f.onChange&&o._f.onChange(t);const x=N(i,m,p,!1),T=!L(x)||S;if(!p&&D.values.next({name:i,type:t.type,values:{...V}}),F)return w.isValid&&("onBlur"===e.mode?p&&j():j()),T&&D.state.next({name:i,...S?{}:x});if(!p&&S&&D.state.next({...v}),y.resolver){const{errors:e}=await B([i]);if(u(m),l){const t=xe(v.errors,_,i),s=xe(e,_,t.name||i);r=s.error,i=s.name,c=L(e)}}else C([i],!0),r=(await de(o,V,O,y.shouldUseNativeValidation))[i],C([i]),u(m),l&&(r?c=!1:w.isValid&&(c=await R(_,!0)));l&&(o._f.deps&&se(o._f.deps),U(i,c,r,x))}var d},re=(e,t)=>{if(f(v.errors,t)&&e.focus)return e.focus(),1},se=async(e,t={})=>{let r,s;const a=M(e);if(y.resolver){const t=await(async e=>{const{errors:t}=await B(e);if(e)for(const r of e){const e=f(t,r);e?g(v.errors,r,e):ce(v.errors,r)}else v.errors=t;return t})(c(e)?e:a);r=L(t),s=e?!a.some((e=>f(t,e))):r}else e?(s=(await Promise.all(a.map((async e=>{const t=f(_,e);return await R(t&&t._f?{[e]:t}:t)})))).every(Boolean),(s||v.isValid)&&j()):s=r=await R(_);return D.state.next({...!q(e)||w.isValid&&r!==v.isValid?{}:{name:e},...y.resolver||!e?{isValid:r}:{},errors:v.errors}),t.shouldFocus&&!s&&K(_,re,e?a:A.mount),s},ae=e=>{const t={...F.mount?V:p};return c(e)?t:q(e)?f(t,e):e.map((e=>f(t,e)))},ie=(e,t)=>({invalid:!!f((t||v).errors,e),isDirty:!!f((t||v).dirtyFields,e),error:f((t||v).errors,e),isValidating:!!f(v.validatingFields,e),isTouched:!!f((t||v).touchedFields,e)}),ne=(e,t,r)=>{const s=(f(_,e,{_f:{}})._f||{}).ref,a=f(v.errors,e)||{},{ref:i,message:n,type:l,...o}=a;g(v.errors,e,{...o,...t,ref:s}),D.state.next({name:e,errors:v.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},le=(e,t={})=>{for(const r of e?M(e):A.mount)A.mount.delete(r),A.array.delete(r),t.keepValue||(ce(_,r),ce(V,r)),!t.keepError&&ce(v.errors,r),!t.keepDirty&&ce(v.dirtyFields,r),!t.keepTouched&&ce(v.touchedFields,r),!t.keepIsValidating&&ce(v.validatingFields,r),!y.shouldUnregister&&!t.keepDefaultValue&&ce(p,r);D.values.next({values:{...V}}),D.state.next({...v,...t.keepDirty?{isDirty:P()}:{}}),!t.keepIsValid&&j()},oe=({disabled:e,name:t,field:r,fields:s,value:a})=>{if(m(e)&&F.mount||e){const i=e?void 0:c(a)?Fe(r?r._f:f(s,t)._f):a;g(V,t,i),N(t,i,!1,!1,!0)}},ue=(r,s={})=>{let a=f(_,r);const i=m(s.disabled)||m(e.disabled);return g(_,r,{...a||{},_f:{...a&&a._f?a._f:{ref:{name:r}},name:r,mount:!0,...s}}),A.mount.add(r),a?oe({field:a,disabled:m(s.disabled)?s.disabled:e.disabled,name:r,value:s.value}):T(r,!0,s.value),{...i?{disabled:s.disabled||e.disabled}:{},...y.progressive?{required:!!s.required,min:Ae(s.min),max:Ae(s.max),minLength:Ae(s.minLength),maxLength:Ae(s.maxLength),pattern:Ae(s.pattern)}:{},name:r,onChange:ee,onBlur:ee,ref:e=>{if(e){ue(r,s),a=f(_,r);const i=c(e.value)&&e.querySelectorAll&&e.querySelectorAll("input,select,textarea")[0]||e,n=(e=>te(e)||t(e))(i),l=a._f.refs||[];if(n?l.find((e=>e===i)):i===a._f.ref)return;g(_,r,{_f:{...a._f,...n?{refs:[...l.filter(ge),i,...Array.isArray(f(p,r))?[{}]:[]],ref:{type:i.type,name:r}}:{ref:i}}}),T(r,!1,void 0,i)}else a=f(_,r,{}),a._f&&(a._f.mount=!1),(y.shouldUnregister||s.shouldUnregister)&&(!l(A.array,r)||!F.action)&&A.unMount.add(r)}}},me=()=>y.shouldFocusError&&K(_,re,A.mount),he=(e,t)=>async r=>{let s;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let a=u(V);if(D.state.next({isSubmitting:!0}),y.resolver){const{errors:e,values:t}=await B();v.errors=e,a=t}else await R(_);if(ce(v.errors,"root"),L(v.errors)){D.state.next({errors:{}});try{await e(a,r)}catch(i){s=i}}else t&&await t({...v.errors},r),me(),setTimeout(me);if(D.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:L(v.errors)&&!s,submitCount:v.submitCount+1,errors:v.errors}),s)throw s},be=(t,r={})=>{const s=t?u(t):p,a=u(s),i=L(t),n=i?p:a;if(r.keepDefaultValues||(p=s),!r.keepValues){if(r.keepDirtyValues){const e=new Set([...A.mount,...Object.keys(pe(p,V))]);for(const t of Array.from(e))f(v.dirtyFields,t)?g(n,t,f(V,t)):z(t,f(n,t))}else{if(o&&c(t))for(const e of A.mount){const t=f(_,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(Z(e)){const t=e.closest("form");if(t){t.reset();break}}}}_={}}V=e.shouldUnregister?r.keepDefaultValues?u(p):{}:u(n),D.array.next({values:{...n}}),D.values.next({values:{...n}})}A={mount:r.keepDirtyValues?A.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},F.mount=!w.isValid||!!r.keepIsValid||!!r.keepDirtyValues,F.watch=!!e.shouldUnregister,D.state.next({submitCount:r.keepSubmitCount?v.submitCount:0,isDirty:!i&&(r.keepDirty?v.isDirty:!(!r.keepDefaultValues||ye(t,p))),isSubmitted:!!r.keepIsSubmitted&&v.isSubmitted,dirtyFields:i?{}:r.keepDirtyValues?r.keepDefaultValues&&V?pe(p,V):v.dirtyFields:r.keepDefaultValues&&t?pe(p,t):r.keepDirty?v.dirtyFields:{},touchedFields:r.keepTouched?v.touchedFields:{},errors:r.keepErrors?v.errors:{},isSubmitSuccessful:!!r.keepIsSubmitSuccessful&&v.isSubmitSuccessful,isSubmitting:!1})},_e=(e,t)=>be(Y(e)?e(V):e,t);return{control:{register:ue,unregister:le,getFieldState:ie,handleSubmit:he,setError:ne,_executeSchema:B,_getWatch:I,_getDirty:P,_updateValid:j,_removeUnmounted:()=>{for(const e of A.unMount){const t=f(_,e);t&&(t._f.refs?t._f.refs.every((e=>!ge(e))):!ge(t._f.ref))&&le(e)}A.unMount=new Set},_updateFieldArray:(t,r=[],s,a,i=!0,n=!0)=>{if(a&&s&&!e.disabled){if(F.action=!0,n&&Array.isArray(f(_,t))){const e=s(f(_,t),a.argA,a.argB);i&&g(_,t,e)}if(n&&Array.isArray(f(v.errors,t))){const e=s(f(v.errors,t),a.argA,a.argB);i&&g(v.errors,t,e),((e,t)=>{!d(f(e,t)).length&&ce(e,t)})(v.errors,t)}if(w.touchedFields&&n&&Array.isArray(f(v.touchedFields,t))){const e=s(f(v.touchedFields,t),a.argA,a.argB);i&&g(v.touchedFields,t,e)}w.dirtyFields&&(v.dirtyFields=pe(p,V)),D.state.next({name:t,isDirty:P(t,r),dirtyFields:v.dirtyFields,errors:v.errors,isValid:v.isValid})}else g(V,t,r)},_updateDisabledField:oe,_getFieldArray:t=>d(f(F.mount?V:p,t,e.shouldUnregister?f(p,t,[]):[])),_reset:be,_resetDefaultValues:()=>Y(y.defaultValues)&&y.defaultValues().then((e=>{_e(e,y.resetOptions),D.state.next({isLoading:!1})})),_updateFormState:e=>{v={...v,...e}},_disableForm:e=>{m(e)&&(D.state.next({disabled:e}),K(_,((t,r)=>{const s=f(_,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach((t=>{t.disabled=s._f.disabled||e})))}),0,!1))},_subjects:D,_proxyFormState:w,_setErrors:e=>{v.errors=e,D.state.next({errors:v.errors,isValid:!1})},get _fields(){return _},get _formValues(){return V},get _state(){return F},set _state(e){F=e},get _defaultValues(){return p},get _names(){return A},set _names(e){A=e},get _formState(){return v},set _formState(e){v=e},get _options(){return y},set _options(e){y={...y,...e}}},trigger:se,register:ue,handleSubmit:he,watch:(e,t)=>Y(e)?D.values.subscribe({next:r=>e(I(void 0,t),r)}):I(e,t,!0),setValue:z,getValues:ae,reset:_e,resetField:(e,t={})=>{f(_,e)&&(c(t.defaultValue)?z(e,u(f(p,e))):(z(e,t.defaultValue),g(p,e,u(t.defaultValue))),t.keepTouched||ce(v.touchedFields,e),t.keepDirty||(ce(v.dirtyFields,e),v.isDirty=t.defaultValue?P(e,u(f(p,e))):P()),t.keepError||(ce(v.errors,e),w.isValid&&j()),D.state.next({...v}))},clearErrors:e=>{e&&M(e).forEach((e=>ce(v.errors,e))),D.state.next({errors:e?v.errors:{}})},unregister:le,setError:ne,setFocus:(e,t={})=>{const r=f(_,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&e.select())}},getFieldState:ie}}function ke(t={}){const r=e.useRef(),s=e.useRef(),[a,i]=e.useState({isDirty:!1,isValidating:!1,isLoading:Y(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,defaultValues:Y(t.defaultValues)?void 0:t.defaultValues});r.current||(r.current={...De(t),formState:a});const n=r.current.control;return n._options=t,P({subject:n._subjects.state,next:e=>{B(e,n._proxyFormState,n._updateFormState,!0)&&i({...n._formState})}}),e.useEffect((()=>n._disableForm(t.disabled)),[n,t.disabled]),e.useEffect((()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}}),[n,a.isDirty]),e.useEffect((()=>{t.values&&!ye(t.values,s.current)?(n._reset(t.values,n._options.resetOptions),s.current=t.values,i((e=>({...e})))):n._resetDefaultValues()}),[t.values,n]),e.useEffect((()=>{t.errors&&n._setErrors(t.errors)}),[t.errors,n]),e.useEffect((()=>{n._state.mount||(n._updateValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()})),e.useEffect((()=>{t.shouldUnregister&&n._subjects.values.next({values:n._getWatch()})}),[t.shouldUnregister,n]),e.useEffect((()=>{r.current&&(r.current.watch=r.current.watch.bind({}))}),[a]),r.current.formState=U(a,n),r.current}const Ee=(e,t,r)=>{if(e&&"reportValidity"in e){const s=f(r,t);e.setCustomValidity(s&&s.message||""),e.reportValidity()}},Oe=(e,t)=>{for(const r in t.fields){const s=t.fields[r];s&&s.ref&&"reportValidity"in s.ref?Ee(s.ref,r,e):s.refs&&s.refs.forEach((t=>Ee(t,r,e)))}},je=(e,t)=>{t.shouldUseNativeValidation&&Oe(e,t);const r={};for(const s in e){const a=f(t.fields,s),i=Object.assign(e[s]||{},{ref:a&&a.ref});if(Ce(t.names||Object.keys(e),s)){const e=Object.assign({},f(r,s));g(e,"root",i),g(r,s,e)}else g(r,s,i)}return r},Ce=(e,t)=>e.some((e=>e.startsWith(t+".")));export{H as C,N as F,T as a,z as b,I as c,Oe as o,je as r,ke as u};
